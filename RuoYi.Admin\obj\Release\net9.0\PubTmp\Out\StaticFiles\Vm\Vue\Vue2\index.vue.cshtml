@{
    var moduleName = Model.moduleName;
    var businessName = Model.businessName;
    var BusinessName = Model.BusinessName;
    var dictType = "";
    var attrName = "";
    var comment = "";
    int parentheseIndex = -1;
    var subClassName = Model.SubClassName;
    var subclassName = Model.subClassName;
}
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
@foreach(var column in Model.Columns) {
  @if(column.Is_Query()) {
      dictType = column.DictType ?? "";
      attrName = column.NetFieldLower();
      parentheseIndex = column.ColumnComment?.IndexOf("（") ?? -1;
      comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;

     if(column.HtmlType == "input") {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-input
          v-model="queryParams.@(attrName)"
          placeholder="请输入@(comment)"
          clearable
          @@keyup.enter.native="handleQuery"
        />
      </el-form-item>
     } else if((column.HtmlType == "select" || column.HtmlType == "radio") && "" != dictType) {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-select v-model="queryParams.@(attrName)" placeholder="请选择@(comment)" clearable>
          <el-option
            v-for="dict in dict.type.@(dictType)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
     } else if((column.HtmlType == "select" || column.HtmlType == "radio") && "" != dictType) {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-select v-model="queryParams.@(attrName)" placeholder="请选择@(comment)" clearable>
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
    } else if(column.HtmlType == "datetime" && column.QueryType != "BETWEEN") {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-date-picker clearable
          v-model="queryParams.@(attrName)"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择@(comment)">
        </el-date-picker>
      </el-form-item>
    } else if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
      <el-form-item label="@(comment)">
        <el-date-picker
          v-model="daterange${AttrName}"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    }
  }
}
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @@click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @@click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @@click="handleAdd"
          v-hasPermi="['@(moduleName):@(businessName):add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @@click="handleUpdate"
          v-hasPermi="['@(moduleName):@(businessName):edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @@click="handleDelete"
          v-hasPermi="['@(moduleName):@(businessName):remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @@click="handleExport"
          v-hasPermi="['@(moduleName):@(businessName):export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @@queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="@(businessName)List" @@selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
@foreach(var column in Model.Columns) {
    attrName = column.NetFieldLower();
    dictType = column.DictType ?? "";
    parentheseIndex = column.ColumnComment?.IndexOf("（") ?? -1;
    comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
    if(column.Is_Pk()) {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" />
    } else if (column.Is_List() && column.HtmlType == "datetime") {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.@(attrName), '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    } else if (column.Is_List() && column.HtmlType == "imageUpload") {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.@(attrName)" :width="50" :height="50"/>
        </template>
      </el-table-column>
    } else if (column.Is_List() && "" != dictType) {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)">
        <template slot-scope="scope">
        @if(column.HtmlType == "checkbox") {
          <dict-tag :options="dict.type.@(dictType)" :value="scope.row.@(attrName) ? scope.row.@(attrName).split(',') : []"/>
        } else {
                                <dict-tag :options="dict.type.@(dictType)" :value="scope.row.@(attrName)" />
        }
        </template>
      </el-table-column>
    } else if (column.Is_List() && "" != attrName) {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" />
    }
}
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit"
            @@click="handleUpdate(scope.row)"
            v-hasPermi="['@(moduleName):@(businessName):edit']"
          >修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
            @@click="handleDelete(scope.row)"
            v-hasPermi="['@(moduleName):@(businessName):remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @@pagination="getList"
    />

    <!-- 添加或修改@(Model.FunctionName)对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
@foreach(var column in Model.Columns) {
  attrName = column.NetFieldLower();
  if(column.Is_Insert() && !column.Is_Pk()) {
    if(column.IsUsableColumn() || !column.IsSuperColumn()) {
      parentheseIndex = column.ColumnComment.IndexOf("（");
      comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
      dictType = column.DictType ?? "";
      var selectVal = column.NetType.Contains("int") || column.NetType.Contains("long") ? "parseInt(dict.value)" : "dict.value";
      var radioLabel = column.NetType.Contains("int") || column.NetType.Contains("long") ? "parseInt(dict.value)" : "dict.value";
      if(column.HtmlType == "input") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-input v-model="form.@(attrName)" placeholder="请输入@(comment)" />
        </el-form-item>
      } else if(column.HtmlType == "imageUpload") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <image-upload v-model="form.@(attrName)"/>
        </el-form-item>
      } else if(column.HtmlType == "fileUpload") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <file-upload v-model="form.@(attrName)"/>
        </el-form-item>
      } else if(column.HtmlType == "editor") {
        <el-form-item label="@(comment)">
          <editor v-model="form.@(attrName)" :min-height="192"/>
        </el-form-item>
      } else if(column.HtmlType == "select" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-select v-model="form.@(attrName)" placeholder="请选择@(comment)">
            <el-option
              v-for="dict in dict.type.@(dictType)"
              :key="dict.value"
              :label="dict.label"
              :value = "@(selectVal)"
            ></el-option>
          </el-select>
        </el-form-item>
      } else if(column.HtmlType == "select" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-select v-model="form.@(attrName)" placeholder="请选择@(comment)">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
      } else if(column.HtmlType == "checkbox" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-checkbox-group v-model="form.@(attrName)">
            <el-checkbox
              v-for="dict in dict.type.@(dictType)"
              :key="dict.value"
              :label="dict.value">
              {{dict.label}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      } else if(column.HtmlType == "checkbox" &&  "" == dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-checkbox-group v-model="form.@(attrName)">
            <el-checkbox>请选择字典生成</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      } else if(column.HtmlType == "radio" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-radio-group v-model="form.@(attrName)">
            <el-radio
              v-for="dict in dict.type.@(dictType)"
              :key="dict.value"
              :label="@(radioLabel)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      } else if(column.HtmlType == "radio" && "" == dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-radio-group v-model="form.@(attrName)">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
      } else if(column.HtmlType == "datetime") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-date-picker clearable
            v-model="form.@(attrName)"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择@(comment)">
          </el-date-picker>
        </el-form-item>
      } else if(column.HtmlType == "textarea") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-input v-model="form.@(attrName)" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      }
    }
  }
}
@if(Model.Table.IsSub()) {
        <el-divider content-position="center">@(Model.SubTable?.FunctionName)信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @@click="handleAdd@(subClassName)">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini" @@click="handleDelete@(subClassName)">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="@(subclassName)List" :row-class-name="row@(subClassName)Index" @@selection-change="handle@(subClassName)SelectionChange" ref="@(subclassName)">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
  @foreach(var column in Model.SubTable.Columns) {
    attrName = column.NetFieldLower();
    parentheseIndex = column.ColumnComment.IndexOf("（");
    comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;

    if(column.Is_Pk() || attrName == Model.SubTableFkName) {
    } else if(column.Is_List() && column.HtmlType == "input") {
          <el-table-column label="@(comment)" prop="@(attrName)" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.$javaField" placeholder="请输入@(comment)" />
            </template>
          </el-table-column>
    } else if(column.Is_List() && column.HtmlType == "datetime") {
          <el-table-column label="@(comment)" prop="@(attrName)" width="240">
            <template slot-scope="scope">
              <el-date-picker clearable v-model="scope.row.$javaField" type="date" value-format="yyyy-MM-dd" placeholder="请选择@(comment)" />
            </template>
          </el-table-column>
    } else if(column.Is_List() && (column.HtmlType == "select" || column.HtmlType == "radio") && "" != column.DictType) {
          <el-table-column label="@(comment)" prop="@(attrName)" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.$javaField" placeholder="请选择@(comment)">
                <el-option v-for="dict in dict.type.@(column.DictType)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
    } else if(column.Is_List() && (column.HtmlType == "select" || column.HtmlType == "radio") && "" == column.DictType) {
          <el-table-column label="@(comment)" prop="@(attrName)" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.$javaField" placeholder="请选择@(comment)">
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </template>
          </el-table-column>
    }
  }
        </el-table>
}
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @@click="submitForm">确 定</el-button>
        <el-button @@click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { list@(BusinessName), get@(BusinessName), del@(BusinessName), add@(BusinessName), update@(BusinessName) } from "@@/api/@(moduleName)/@(businessName)";

export default {
  name: "@(BusinessName)",
@if(Model.Dicts != "") {
  @:dicts: [@(Model.Dicts)],
}
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
@if(Model.Table.IsSub()){
      // 子表选中数据
      @:checked@(subClassName): [],
}
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // @(Model.FunctionName)表格数据
      @(businessName)List: [],
@if(Model.Table.IsSub()) {
      @:// @(Model.SubTable.FunctionName)表格数据
      @:@(subclassName)List: [],
}
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
@foreach (var column in Model.Columns) {
   if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
       attrName = column.NetFieldLower();
      @:// @(comment)时间范围
      @:daterange@(attrName): [],
   }
}
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
@foreach (var column in Model.Columns) {
  if(column.Is_Query()) {
        @:@(column.NetFieldLower()): null,
  }
}
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
@foreach (var column in Model.Columns) {
  @if(column.Is_Query()) {
    parentheseIndex = column.ColumnComment.IndexOf("（");
    comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
    var trigger = column.HtmlType == "select" || column.HtmlType == "radio" ? "change" : "blur";
        @:@column.NetFieldLower(): [
        @:  { required: true, message: "@(comment)不能为空", trigger: "@(trigger)" }
        @:],
  }
}
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询@(Model.FunctionName)列表 */
    getList() {
      this.loading = true;
@foreach (var column in Model.Columns) {
  if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
      @:this.queryParams.params = {};
      break;
  }
}
@foreach (var column in Model.Columns) {
  if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
      attrName = column.NetFieldLower();
      @:if (null != this.daterange@(attrName) && '' != this.daterange@(attrName)) {
        @:this.queryParams.params["begin@(attrName)"] = this.daterange@(attrName)[0];
        @:this.queryParams.params["end@(attrName)"] = this.daterange@(attrName)[1];
      @:}
  }
}
      list@(BusinessName)(this.queryParams).then(response => {
        this.@(businessName)List = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
@foreach (var column in Model.Columns) {
  var comma = Model.Columns.IndexOf(column) != Model.Columns.Count - 1 ? "," : "";
  if(column.HtmlType == "checkbox") {
        @:@column.NetFieldLower(): []@(comma)
  } else {
        @:@column.NetFieldLower(): null@(comma)
  }
}
      };
@if(Model.Table.IsSub()) {
      @:this.@(subclassName)List = [];
}
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
@foreach (var column in Model.Columns) {
  if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
      attrName = column.NetFieldLower();
      @:this.daterange@(attrName) = [];
  }
}
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.@(Model.PkColumn.NetFieldLower()))
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加@(Model.FunctionName)";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const @(Model.PkColumn.NetFieldLower()) = row.@(Model.PkColumn.NetFieldLower()) || this.ids;
      get@(BusinessName)(@(Model.PkColumn.NetFieldLower())).then(response => {
        this.form = response.data;
@foreach (var column in Model.Columns) {
  if(column.HtmlType == "checkbox") {
        @:this.form.@(column.NetFieldLower()) = this.form.@(attrName).split(",");
  }
}
@if(Model.Table.IsSub()) {
        @:this.@(subclassName)List = response.data.@(subclassName)List;
}
        this.open = true;
        this.title = "修改@(Model.FunctionName)";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
@foreach (var column in Model.Columns) {
  if(column.HtmlType == "checkbox") {
          @:this.form.@(column.NetFieldLower()) = this.form.@(attrName).join(",");
  }
}
@if(Model.Table.IsSub()) {
          @:this.form.@(subclassName)List = this.@(subclassName)List;
}
          if (this.form.@(Model.PkColumn.NetFieldLower()) != null) {
            update@(BusinessName)(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            add@(BusinessName)(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const @(Model.PkColumn.NetFieldLower())s = row.@(Model.PkColumn.NetFieldLower()) || this.ids;
          this.$modal.confirm('是否确认删除@(Model.FunctionName)编号为"' + @(Model.PkColumn.NetFieldLower())s + '"的数据项？').then(function() {
        return del@(BusinessName)(@(Model.PkColumn.NetFieldLower())s);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
@if(Model.Table.IsSub()) {
    @:/** @(Model.SubTable.FunctionName)序号 */
    @:row@(subClassName)Index({ row, rowIndex }) {
    @:  row.index = rowIndex + 1;
    @:},
    @:/** @(Model.SubTable.FunctionName)添加按钮操作 */
    @:handleAdd@(subClassName)() {
    @:  let obj = {};
  @foreach(var column in Model.SubTable.Columns) {
    if(column.Is_Pk() || column.NetField == Model.SubTableFkName) {
    } else if(column.Is_List() && "" != column.NetField) {
    @:  obj.@(column.NetFieldLower()) = "";
    }
  }
    @:  this.@(subclassName)List.push(obj);
    @:},
    @:/** @(Model.SubTable.FunctionName)删除按钮操作 */
    @:handleDelete@(subClassName)() {
    @:  if (this.checked@(subClassName).length == 0) {
    @:    this.$modal.msgError("请先选择要删除的@(Model.SubTable.FunctionName)数据");
    @:  } else {
    @:    const @(subclassName)List = this.@(subclassName)List;
    @:    const checked@(subClassName) = this.checked@(subClassName);
    @:    this.@(subclassName)List = @(subclassName)List.filter(function(item) {
    @:      return checked@(subClassName).indexOf(item.index) == -1
    @:    });
    @:  }
    @:},
    /** 复选框选中数据 */
    @:handle@(subClassName)SelectionChange(selection) {
    @:  this.checked@(subClassName) = selection.map(item => item.index)
    @:},
}
    /** 导出按钮操作 */
    handleExport() {
      this.download('@(moduleName)/@(businessName)/export', {
        ...this.queryParams
      }, `@(businessName)_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

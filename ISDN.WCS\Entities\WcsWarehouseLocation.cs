using SqlSugar;
using System;
using System.Collections.Generic;
using RuoYi.Data.Entities;

namespace ISDN.WCS.Data.Entities
{
    /// <summary>
    ///  库位管理 对象 wcs_warehouse_location
    ///  author zgq
    ///  date   2025-08-13 16:36:43
    /// </summary>
    [SugarTable("wcs_warehouse_location", "库位")]
    public class WcsWarehouseLocation : IsdnBaseEntity
  {
        /// <summary>
        /// 货架编码 (shelves)
        /// </summary>
        [SugarColumn(ColumnName = "shelves", ColumnDescription = "货架编码")]
        public string? Shelves { get; set; }
        
        /// <summary>
        /// x轴坐标 (x)
        /// </summary>
        [SugarColumn(ColumnName = "x", ColumnDescription = "x轴坐标")]
        public string? X { get; set; }
        
        /// <summary>
        /// y轴坐标 (y)
        /// </summary>
        [SugarColumn(ColumnName = "y", ColumnDescription = "y轴坐标")]
        public string? Y { get; set; }
        
        /// <summary>
        /// z轴坐标 (z)
        /// </summary>
        [SugarColumn(ColumnName = "z", ColumnDescription = "z轴坐标")]
        public string? Z { get; set; }
        
        /// <summary>
        /// 所在层数 (layer)
        /// </summary>
        [SugarColumn(ColumnName = "layer", ColumnDescription = "所在层数")]
        public int? Layer { get; set; }
        
        /// <summary>
        /// 货位深度(1或2) (deep)
        /// </summary>
        [SugarColumn(ColumnName = "deep", ColumnDescription = "货位深度(1或2)")]
        public int? Deep { get; set; }
        
        /// <summary>
        /// 库位ID (location_id)
        /// </summary>
        [SugarColumn(ColumnName = "location_id", ColumnDescription = "库位ID", IsPrimaryKey = true, IsIdentity = true)]
        public long LocationId { get; set; }
        
        /// <summary>
        /// 库位名称 (location_name)
        /// </summary>
        [SugarColumn(ColumnName = "location_name", ColumnDescription = "库位名称")]
        public string LocationName { get; set; }
        
        /// <summary>
        /// 库位编码 (location_code)
        /// </summary>
        [SugarColumn(ColumnName = "location_code", ColumnDescription = "库位编码")]
        public string LocationCode { get; set; }
        
        /// <summary>
        /// 巷道ID (lane_id)
        /// </summary>
        [SugarColumn(ColumnName = "lane_id", ColumnDescription = "巷道ID")]
        public string? LaneId { get; set; }
        
        /// <summary>
        /// 库位类型 (location_type)
        /// </summary>
        [SugarColumn(ColumnName = "location_type", ColumnDescription = "库位类型")]
        public int? LocationType { get; set; }
        
        /// <summary>
        /// 生效 (is_visible_flag)
        /// </summary>
        [SugarColumn(ColumnName = "is_visible_flag", ColumnDescription = "生效")]
        public int? IsVisibleFlag { get; set; }
        
        /// <summary>
        /// 创建人id (created_by)
        /// </summary>
        [SugarColumn(ColumnName = "created_by", ColumnDescription = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id (updated_by)
        /// </summary>
        [SugarColumn(ColumnName = "updated_by", ColumnDescription = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间 (created_time)
        /// </summary>
        [SugarColumn(ColumnName = "created_time", ColumnDescription = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间 (updated_time)
        /// </summary>
        [SugarColumn(ColumnName = "updated_time", ColumnDescription = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
    }
}

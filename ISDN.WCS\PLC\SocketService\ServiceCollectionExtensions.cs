using ISDN.WCS.PLC.Socket;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ISDN.WCS.Repositories;
using System;

namespace ISDN.WCS.PLC.SocketService;

/// <summary>
/// 服务集合扩展
/// 提供Socket服务的注册方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加Socket服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSocketServices(this IServiceCollection services)
    {
        // 注册数据仓库
        services.AddScoped<WcsDeviceRepository>();

        //// 注册PLCServerSocket（如果尚未注册）
        //services.AddSingleton<PLCServerSocket>();

        //// 注册SocketService作为核心服务
        //services.AddSingleton<SocketService>();
        services.AddHostedService<PLCClientSocketManager>();
        services.AddSingleton<PLCClientSocketManager>();  // 关键补充
        return services;
    }
}

<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <NoWarn>1701;1702;8618;</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <NoWarn>1701;1702;8618;</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="Enums\**" />
        <EmbeddedResource Remove="Enums\**" />
        <None Remove="Enums\**" />
        <Compile Remove="Dtos\SysJobLogDto.cs" />
    </ItemGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
        <PackageReference Include="MySqlConnector" Version="2.4.0" />
        <PackageReference Include="SqlSugarCore" Version="5.1.4.170" />
    </ItemGroup>

</Project>

{"Version": 1, "Hash": "kgIyeeAQFdtMo7n97IGTCCpaMsnvUa3L+FByb9wA1/E=", "Source": "RuoYi.Admin", "BasePath": "_content/RuoYi.Admin", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "RuoYi.Admin\\wwwroot", "Source": "RuoYi.Admin", "ContentRoot": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Admin\\wwwroot\\", "BasePath": "_content/RuoYi.Admin", "Pattern": "**"}], "Assets": [{"Identity": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Admin\\wwwroot\\images\\logo.png", "SourceId": "RuoYi.Admin", "SourceType": "Discovered", "ContentRoot": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Admin\\wwwroot\\", "BasePath": "_content/RuoYi.Admin", "RelativePath": "images/logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "txuyb3nd63", "Integrity": "36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\logo.png", "FileLength": 8993, "LastWriteTime": "2025-08-27T03:47:46+00:00"}], "Endpoints": [{"Route": "images/logo.png", "AssetFile": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Admin\\wwwroot\\images\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8993"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI=\""}, {"Name": "Last-Modified", "Value": "Wed, 27 Aug 2025 03:47:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI="}]}, {"Route": "images/logo.txuyb3nd63.png", "AssetFile": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Admin\\wwwroot\\images\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8993"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI=\""}, {"Name": "Last-Modified", "Value": "Wed, 27 Aug 2025 03:47:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "txuyb3nd63"}, {"Name": "label", "Value": "images/logo.png"}, {"Name": "integrity", "Value": "sha256-36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI="}]}]}
{"openapi": "3.0.4", "info": {"title": "System", "version": "1.0.0"}, "paths": {"/login": {"post": {"tags": ["SysLogin"], "operationId": "login-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/LoginBody"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginBody"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginBody"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginBody"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/logout": {"post": {"tags": ["SysLogin"], "operationId": "logout-Post", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/getInfo": {"get": {"tags": ["SysLogin"], "operationId": "getInfo-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/getRouters": {"get": {"tags": ["SysLogin"], "operationId": "getRouters-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/config/list": {"get": {"tags": ["SysConfig"], "operationId": "system-config-list-Get", "parameters": [{"name": "ConfigId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ConfigName", "in": "query", "required": true, "schema": {"maxLength": 100, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"maxLength": 100, "type": "string"}}, {"name": "ConfigValue", "in": "query", "required": true, "schema": {"maxLength": 500, "type": "string"}}, {"name": "ConfigType", "in": "query", "schema": {"type": "string"}}, {"name": "ConfigTypeDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysConfigDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysConfigDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysConfigDto"}}}}}}}, "/system/config/{id}": {"get": {"tags": ["SysConfig"], "operationId": "system-config-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/config/configKey/{configKey}": {"get": {"tags": ["SysConfig"], "operationId": "system-config-configKey-configKey-Get", "parameters": [{"name": "config<PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/config": {"post": {"tags": ["SysConfig"], "operationId": "system-config-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysConfig"], "operationId": "system-config-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysConfigDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/config/{configIds}": {"delete": {"tags": ["SysConfig"], "operationId": "system-config-configIds-Delete", "parameters": [{"name": "configIds", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/config/export": {"post": {"tags": ["SysConfig"], "operationId": "system-config-export-Post", "parameters": [{"name": "ConfigId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ConfigName", "in": "query", "required": true, "schema": {"maxLength": 100, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"maxLength": 100, "type": "string"}}, {"name": "ConfigValue", "in": "query", "required": true, "schema": {"maxLength": 500, "type": "string"}}, {"name": "ConfigType", "in": "query", "schema": {"type": "string"}}, {"name": "ConfigTypeDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/system/config/refreshCache": {"delete": {"tags": ["SysConfig"], "operationId": "system-config-refreshCache-Delete", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dept/list": {"get": {"tags": ["SysDept"], "operationId": "system-dept-list-Get", "parameters": [{"name": "DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Ancestors", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Leader", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"maxLength": 30, "type": "string"}}, {"name": "Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "Children", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ParentIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dept/list/exclude/{deptId}": {"get": {"tags": ["SysDept"], "operationId": "system-dept-list-exclude-deptId-Get", "parameters": [{"name": "deptId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dept/{deptId}": {"get": {"tags": ["SysDept"], "operationId": "system-dept-deptId-Get", "parameters": [{"name": "deptId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "delete": {"tags": ["SysDept"], "operationId": "system-dept-deptId-Delete", "parameters": [{"name": "deptId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dept": {"post": {"tags": ["SysDept"], "operationId": "system-dept-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysDept"], "operationId": "system-dept-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDeptDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/data/list": {"get": {"tags": ["SysDictData"], "operationId": "system-dict-data-list-Get", "parameters": [{"name": "DictCode", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DictSort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DictLabel", "in": "query", "required": true, "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "DictValue", "in": "query", "required": true, "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "DictType", "in": "query", "required": true, "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "CssClass", "in": "query", "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "ListClass", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "IsDefaultDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysDictDataDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysDictDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysDictDataDto"}}}}}}}, "/system/dict/data/{dictCode}": {"get": {"tags": ["SysDictData"], "operationId": "system-dict-data-dictCode-Get", "parameters": [{"name": "dictCode", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/data/type/{dictType}": {"get": {"tags": ["SysDictData"], "operationId": "system-dict-data-type-dictType-Get", "parameters": [{"name": "dictType", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/data": {"post": {"tags": ["SysDictData"], "operationId": "system-dict-data-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysDictData"], "operationId": "system-dict-data-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictDataDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/data/{dictCodes}": {"delete": {"tags": ["SysDictData"], "operationId": "system-dict-data-dictCodes-Delete", "parameters": [{"name": "dictCodes", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/data/export": {"post": {"tags": ["SysDictData"], "operationId": "system-dict-data-export-Post", "parameters": [{"name": "DictCode", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DictSort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DictLabel", "in": "query", "required": true, "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "DictValue", "in": "query", "required": true, "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "DictType", "in": "query", "required": true, "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "CssClass", "in": "query", "schema": {"maxLength": 100, "minLength": 0, "type": "string"}}, {"name": "ListClass", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "IsDefaultDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/system/dict/type/list": {"get": {"tags": ["SysDictType"], "operationId": "system-dict-type-list-Get", "parameters": [{"name": "DictId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DictName", "in": "query", "schema": {"type": "string"}}, {"name": "DictType", "in": "query", "schema": {"pattern": "^[a-z][a-z0-9_]*$", "type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysDictTypeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysDictTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysDictTypeDto"}}}}}}}, "/system/dict/type/{id}": {"get": {"tags": ["SysDictType"], "operationId": "system-dict-type-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/type": {"post": {"tags": ["SysDictType"], "operationId": "system-dict-type-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysDictType"], "operationId": "system-dict-type-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictTypeDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/type/{ids}": {"delete": {"tags": ["SysDictType"], "operationId": "system-dict-type-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/type/refreshCache": {"delete": {"tags": ["SysDictType"], "operationId": "system-dict-type-refreshCache-Delete", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/type/optionselect": {"get": {"tags": ["SysDictType"], "operationId": "system-dict-type-optionselect-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/dict/type/export": {"post": {"tags": ["SysDictType"], "operationId": "system-dict-type-export-Post", "parameters": [{"name": "DictId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DictName", "in": "query", "schema": {"type": "string"}}, {"name": "DictType", "in": "query", "schema": {"pattern": "^[a-z][a-z0-9_]*$", "type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/system/menu/list": {"get": {"tags": ["SysMenu"], "operationId": "system-menu-list-Get", "parameters": [{"name": "MenuId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "MenuName", "in": "query", "required": true, "schema": {"maxLength": 50, "type": "string"}}, {"name": "ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Path", "in": "query", "schema": {"maxLength": 200, "type": "string"}}, {"name": "Component", "in": "query", "schema": {"maxLength": 255, "type": "string"}}, {"name": "Query", "in": "query", "schema": {"type": "string"}}, {"name": "IsFrame", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "MenuType", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Visible", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Perms", "in": "query", "schema": {"maxLength": 100, "type": "string"}}, {"name": "Icon", "in": "query", "schema": {"type": "string"}}, {"name": "RoleStatus", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "MenuTypes", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/menu/{menuId}": {"get": {"tags": ["SysMenu"], "operationId": "system-menu-menuId-Get", "parameters": [{"name": "menuId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "delete": {"tags": ["SysMenu"], "operationId": "system-menu-menuId-Delete", "parameters": [{"name": "menuId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/menu/treeselect": {"get": {"tags": ["SysMenu"], "operationId": "system-menu-treeselect-Get", "parameters": [{"name": "MenuId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "MenuName", "in": "query", "required": true, "schema": {"maxLength": 50, "type": "string"}}, {"name": "ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Path", "in": "query", "schema": {"maxLength": 200, "type": "string"}}, {"name": "Component", "in": "query", "schema": {"maxLength": 255, "type": "string"}}, {"name": "Query", "in": "query", "schema": {"type": "string"}}, {"name": "IsFrame", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "MenuType", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "Visible", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Perms", "in": "query", "schema": {"maxLength": 100, "type": "string"}}, {"name": "Icon", "in": "query", "schema": {"type": "string"}}, {"name": "RoleStatus", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "MenuTypes", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/menu/roleMenuTreeselect/{roleId}": {"get": {"tags": ["SysMenu"], "operationId": "system-menu-roleMenuTreeselect-roleId-Get", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/menu": {"post": {"tags": ["SysMenu"], "operationId": "system-menu-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysMenu"], "operationId": "system-menu-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysMenuDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/notice/list": {"get": {"tags": ["SysNotice"], "operationId": "system-notice-list-Get", "parameters": [{"name": "NoticeId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "NoticeTitle", "in": "query", "required": true, "schema": {"maxLength": 50, "type": "string"}}, {"name": "NoticeType", "in": "query", "schema": {"type": "string"}}, {"name": "NoticeContent", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysNoticeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysNoticeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysNoticeDto"}}}}}}}, "/system/notice/{id}": {"get": {"tags": ["SysNotice"], "operationId": "system-notice-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/notice": {"post": {"tags": ["SysNotice"], "operationId": "system-notice-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysNotice"], "operationId": "system-notice-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysNoticeDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/notice/{ids}": {"delete": {"tags": ["SysNotice"], "operationId": "system-notice-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/post/list": {"get": {"tags": ["SysPost"], "operationId": "system-post-list-Get", "parameters": [{"name": "PostId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "PostCode", "in": "query", "required": true, "schema": {"maxLength": 64, "type": "string"}}, {"name": "PostCodeLike", "in": "query", "schema": {"type": "string"}}, {"name": "PostName", "in": "query", "required": true, "schema": {"maxLength": 50, "type": "string"}}, {"name": "PostNameLike", "in": "query", "schema": {"type": "string"}}, {"name": "PostSort", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysPostDto"}}}}}}}, "/system/post/{id}": {"get": {"tags": ["SysPost"], "operationId": "system-post-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/post": {"post": {"tags": ["SysPost"], "operationId": "system-post-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysPost"], "operationId": "system-post-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysPostDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/post/{ids}": {"delete": {"tags": ["SysPost"], "operationId": "system-post-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/post/export": {"post": {"tags": ["SysPost"], "operationId": "system-post-export-Post", "parameters": [{"name": "PostId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "PostCode", "in": "query", "required": true, "schema": {"maxLength": 64, "type": "string"}}, {"name": "PostCodeLike", "in": "query", "schema": {"type": "string"}}, {"name": "PostName", "in": "query", "required": true, "schema": {"maxLength": 50, "type": "string"}}, {"name": "PostNameLike", "in": "query", "schema": {"type": "string"}}, {"name": "PostSort", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/system/post/optionselect": {"get": {"tags": ["SysPost"], "operationId": "system-post-optionselect-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/profile": {"get": {"tags": ["SysProfile"], "operationId": "system-user-profile-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysProfile"], "operationId": "system-user-profile-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/profile/updatePwd": {"put": {"tags": ["SysProfile"], "operationId": "system-user-profile-updatePwd-Put", "parameters": [{"name": "oldPassword", "in": "query", "schema": {"type": "string"}}, {"name": "newPassword", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/register": {"post": {"tags": ["SysRegister"], "operationId": "register-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/RegisterBody"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RegisterBody"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterBody"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterBody"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/list": {"get": {"tags": ["SysRole"], "operationId": "system-role-list-Get", "parameters": [{"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "RoleName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"maxLength": 100, "type": "string"}}, {"name": "RoleSort", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "DataScope", "in": "query", "schema": {"type": "string"}}, {"name": "DataScopeDesc", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "Flag", "in": "query", "schema": {"type": "boolean"}}, {"name": "MenuIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "DeptIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "Permissions", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysRoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysRoleDto"}}}}}}}, "/system/role/{id}": {"get": {"tags": ["SysRole"], "operationId": "system-role-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role": {"post": {"tags": ["SysRole"], "operationId": "system-role-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysRole"], "operationId": "system-role-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/dataScope": {"put": {"tags": ["SysRole"], "operationId": "system-role-dataScope-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/changeStatus": {"put": {"tags": ["SysRole"], "operationId": "system-role-changeStatus-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysRoleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/{ids}": {"delete": {"tags": ["SysRole"], "operationId": "system-role-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/export": {"post": {"tags": ["SysRole"], "operationId": "system-role-export-Post", "parameters": [{"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "RoleName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"maxLength": 100, "type": "string"}}, {"name": "RoleSort", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "DataScope", "in": "query", "schema": {"type": "string"}}, {"name": "DataScopeDesc", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "Flag", "in": "query", "schema": {"type": "boolean"}}, {"name": "MenuIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "DeptIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "Permissions", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/system/role/optionselect": {"post": {"tags": ["SysRole"], "operationId": "system-role-optionselect-Post", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/authUser/allocatedList": {"get": {"tags": ["SysRole"], "operationId": "system-role-authUser-allocatedList-Get", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"maxLength": 30, "minLength": 0, "type": "string"}}, {"name": "Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Phonenumber", "in": "query", "schema": {"maxLength": 11, "minLength": 0, "type": "string"}}, {"name": "Sex", "in": "query", "schema": {"type": "string"}}, {"name": "SexDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Avatar", "in": "query", "schema": {"type": "string"}}, {"name": "Password", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "LoginIp", "in": "query", "schema": {"type": "string"}}, {"name": "LoginDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.Ancestors", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DeptName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Dept.Leader", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Phone", "in": "query", "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Dept.Status", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Children", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}}}, {"name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "Dept.RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "Dept.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Roles", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDto"}}}, {"name": "RoleIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "PostIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "IsAllocated", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserDto"}}}}}}}, "/system/role/authUser/unallocatedList": {"get": {"tags": ["SysRole"], "operationId": "system-role-authUser-unallocatedList-Get", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"maxLength": 30, "minLength": 0, "type": "string"}}, {"name": "Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Phonenumber", "in": "query", "schema": {"maxLength": 11, "minLength": 0, "type": "string"}}, {"name": "Sex", "in": "query", "schema": {"type": "string"}}, {"name": "SexDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Avatar", "in": "query", "schema": {"type": "string"}}, {"name": "Password", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "LoginIp", "in": "query", "schema": {"type": "string"}}, {"name": "LoginDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.Ancestors", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DeptName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Dept.Leader", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Phone", "in": "query", "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Dept.Status", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Children", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}}}, {"name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "Dept.RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "Dept.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Roles", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDto"}}}, {"name": "RoleIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "PostIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "IsAllocated", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserDto"}}}}}}}, "/system/role/authUser/cancel": {"put": {"tags": ["SysRole"], "operationId": "system-role-authUser-cancel-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysUserRoleDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysUserRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysUserRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysUserRoleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/authUser/cancelAll": {"put": {"tags": ["SysRole"], "operationId": "system-role-authUser-cancelAll-Put", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/authUser/selectAll": {"put": {"tags": ["SysRole"], "operationId": "system-role-authUser-selectAll-Put", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/role/deptTree/{roleId}": {"get": {"tags": ["SysRole"], "operationId": "system-role-deptTree-roleId-Get", "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/list": {"get": {"tags": ["SysUser"], "operationId": "system-user-list-Get", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"maxLength": 30, "minLength": 0, "type": "string"}}, {"name": "Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Phonenumber", "in": "query", "schema": {"maxLength": 11, "minLength": 0, "type": "string"}}, {"name": "Sex", "in": "query", "schema": {"type": "string"}}, {"name": "SexDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Avatar", "in": "query", "schema": {"type": "string"}}, {"name": "Password", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "LoginIp", "in": "query", "schema": {"type": "string"}}, {"name": "LoginDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.Ancestors", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DeptName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Dept.Leader", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Phone", "in": "query", "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Dept.Status", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Children", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}}}, {"name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "Dept.RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "Dept.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Roles", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDto"}}}, {"name": "RoleIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "PostIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "IsAllocated", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUser"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUser"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUser"}}}}}}}, "/system/user": {"get": {"tags": ["SysUser"], "operationId": "system-user-Get", "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["SysUser"], "operationId": "system-user-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysUser"], "operationId": "system-user-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/{userId}": {"get": {"tags": ["SysUser"], "operationId": "system-user-userId-Get", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/{ids}": {"delete": {"tags": ["SysUser"], "operationId": "system-user-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/resetPwd": {"put": {"tags": ["SysUser"], "operationId": "system-user-resetPwd-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/changeStatus": {"put": {"tags": ["SysUser"], "operationId": "system-user-changeStatus-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/authRole/{userId}": {"get": {"tags": ["SysUser"], "operationId": "system-user-authRole-userId-Get", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/authRole": {"put": {"tags": ["SysUser"], "operationId": "system-user-authRole-Put", "parameters": [{"name": "userId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "roleIds", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/deptTree": {"get": {"tags": ["SysUser"], "operationId": "system-user-deptTree-Get", "parameters": [{"name": "DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Ancestors", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Leader", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"maxLength": 30, "type": "string"}}, {"name": "Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "Children", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ParentIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/system/user/importTemplate": {"post": {"tags": ["SysUser"], "operationId": "system-user-importTemplate-Post", "responses": {"200": {"description": "OK"}}}}, "/system/user/export": {"post": {"tags": ["SysUser"], "operationId": "system-user-export-Post", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"maxLength": 30, "minLength": 0, "type": "string"}}, {"name": "Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Phonenumber", "in": "query", "schema": {"maxLength": 11, "minLength": 0, "type": "string"}}, {"name": "Sex", "in": "query", "schema": {"type": "string"}}, {"name": "SexDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Avatar", "in": "query", "schema": {"type": "string"}}, {"name": "Password", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "LoginIp", "in": "query", "schema": {"type": "string"}}, {"name": "LoginDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.DeptId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.Ancestors", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DeptName", "in": "query", "required": true, "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.OrderNum", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "Dept.Leader", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Phone", "in": "query", "schema": {"maxLength": 30, "type": "string"}}, {"name": "Dept.Email", "in": "query", "schema": {"maxLength": 50, "type": "string", "format": "email"}}, {"name": "Dept.Status", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.DelFlag", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.ParentName", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Children", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}}}, {"name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "boolean"}}, {"name": "Dept.RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Dept.ParentIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "Dept.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Dept.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Dept.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Roles", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDto"}}}, {"name": "RoleIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "PostIds", "in": "query", "schema": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, {"name": "RoleId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "IsAllocated", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"LoginBody": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "uuid": {"type": "string", "nullable": true}}, "additionalProperties": false}, "QueryParam": {"type": "object", "properties": {"beginTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "dataScopeSql": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterBody": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "uuid": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SqlSugarPagedList_SysConfigDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysConfigDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysDictDataDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictDataDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysDictTypeDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictTypeDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysNoticeDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysNoticeDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysPostDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysPostDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysRoleDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysUser": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysUser"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysUserDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysUserDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SysConfigDto": {"required": ["config<PERSON><PERSON>", "config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "configId": {"type": "integer", "format": "int32", "nullable": true}, "configName": {"maxLength": 100, "minLength": 1, "type": "string"}, "configKey": {"maxLength": 100, "minLength": 1, "type": "string"}, "configValue": {"maxLength": 500, "minLength": 1, "type": "string"}, "configType": {"type": "string", "nullable": true}, "configTypeDesc": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysDept": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "deptId": {"type": "integer", "format": "int64"}, "parentId": {"type": "integer", "format": "int64"}, "ancestors": {"type": "string", "nullable": true}, "deptName": {"type": "string", "nullable": true}, "orderNum": {"type": "integer", "format": "int32", "nullable": true}, "leader": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/SysDept"}, "nullable": true}}, "additionalProperties": false}, "SysDeptDto": {"required": ["deptName", "orderNum"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "deptId": {"type": "integer", "format": "int64", "nullable": true}, "parentId": {"type": "integer", "format": "int64", "nullable": true}, "ancestors": {"type": "string", "nullable": true}, "deptName": {"maxLength": 30, "minLength": 1, "type": "string"}, "orderNum": {"type": "integer", "format": "int32"}, "leader": {"type": "string", "nullable": true}, "phone": {"maxLength": 30, "type": "string", "nullable": true}, "email": {"maxLength": 50, "type": "string", "format": "email", "nullable": true}, "status": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}, "nullable": true}, "deptCheckStrictly": {"type": "boolean", "nullable": true}, "roleId": {"type": "integer", "format": "int64", "nullable": true}, "parentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "SysDictDataDto": {"required": ["dict<PERSON><PERSON>l", "dictType", "dict<PERSON><PERSON>ue"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "dictCode": {"type": "integer", "format": "int64"}, "dictSort": {"type": "integer", "format": "int32", "nullable": true}, "dictLabel": {"maxLength": 100, "minLength": 0, "type": "string"}, "dictValue": {"maxLength": 100, "minLength": 0, "type": "string"}, "dictType": {"maxLength": 100, "minLength": 0, "type": "string"}, "cssClass": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "listClass": {"type": "string", "nullable": true}, "isDefault": {"type": "string", "nullable": true}, "isDefaultDesc": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusDesc": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysDictTypeDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "dictId": {"type": "integer", "format": "int64", "nullable": true}, "dictName": {"type": "string", "nullable": true}, "dictType": {"pattern": "^[a-z][a-z0-9_]*$", "type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusDesc": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysMenuDto": {"required": ["menuName", "menuType", "orderNum"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "menuId": {"type": "integer", "format": "int64"}, "menuName": {"maxLength": 50, "minLength": 1, "type": "string"}, "parentName": {"type": "string", "nullable": true}, "parentId": {"type": "integer", "format": "int64"}, "orderNum": {"type": "integer", "format": "int32"}, "path": {"maxLength": 200, "type": "string", "nullable": true}, "component": {"maxLength": 255, "type": "string", "nullable": true}, "query": {"type": "string", "nullable": true}, "isFrame": {"type": "string", "nullable": true}, "isCache": {"type": "string", "nullable": true}, "menuType": {"minLength": 1, "type": "string"}, "visible": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "perms": {"maxLength": 100, "type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "roleStatus": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "menuTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "SysNoticeDto": {"required": ["noticeTitle"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "noticeId": {"type": "integer", "format": "int32", "nullable": true}, "noticeTitle": {"maxLength": 50, "minLength": 1, "type": "string"}, "noticeType": {"type": "string", "nullable": true}, "noticeContent": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysPostDto": {"required": ["postCode", "postName", "postSort"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "postId": {"type": "integer", "format": "int64", "nullable": true}, "postCode": {"maxLength": 64, "minLength": 1, "type": "string"}, "postCodeLike": {"type": "string", "nullable": true}, "postName": {"maxLength": 50, "minLength": 1, "type": "string"}, "postNameLike": {"type": "string", "nullable": true}, "postSort": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "statusDesc": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "SysRoleDto": {"required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleSort"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "roleId": {"type": "integer", "format": "int64"}, "roleName": {"maxLength": 30, "minLength": 1, "type": "string"}, "roleKey": {"maxLength": 100, "minLength": 1, "type": "string"}, "roleSort": {"type": "integer", "format": "int32"}, "dataScope": {"type": "string", "nullable": true}, "dataScopeDesc": {"type": "string", "nullable": true}, "menuCheckStrictly": {"type": "boolean", "nullable": true}, "deptCheckStrictly": {"type": "boolean", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusDesc": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "flag": {"type": "boolean", "nullable": true}, "menuIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "deptIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "userId": {"type": "integer", "format": "int64", "nullable": true}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysUser": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "userId": {"type": "integer", "format": "int64"}, "deptId": {"type": "integer", "format": "int64", "nullable": true}, "userName": {"type": "string", "nullable": true}, "nickName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phonenumber": {"type": "string", "nullable": true}, "sex": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "loginIp": {"type": "string", "nullable": true}, "loginDate": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "dept": {"$ref": "#/components/schemas/SysDept"}}, "additionalProperties": false}, "SysUserDto": {"required": ["userName"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "userId": {"type": "integer", "format": "int64", "nullable": true}, "deptId": {"type": "integer", "format": "int64", "nullable": true}, "userName": {"maxLength": 30, "minLength": 1, "type": "string"}, "nickName": {"maxLength": 30, "minLength": 0, "type": "string", "nullable": true}, "email": {"maxLength": 50, "type": "string", "format": "email", "nullable": true}, "phonenumber": {"maxLength": 11, "minLength": 0, "type": "string", "nullable": true}, "sex": {"type": "string", "nullable": true}, "sexDesc": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusDesc": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "loginIp": {"type": "string", "nullable": true}, "loginDate": {"type": "string", "format": "date-time"}, "dept": {"$ref": "#/components/schemas/SysDeptDto"}, "deptName": {"type": "string", "nullable": true}, "deptLeader": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDto"}, "nullable": true}, "roleIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "postIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "roleId": {"type": "integer", "format": "int64", "nullable": true}, "isAllocated": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "SysUserRoleDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "userId": {"type": "integer", "format": "int64"}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "roleId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}
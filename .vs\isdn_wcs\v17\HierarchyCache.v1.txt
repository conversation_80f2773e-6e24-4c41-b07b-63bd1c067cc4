﻿++解决方案 'isdn_wcs' ‎ (8 个项目，共 8 个)
i:{00000000-0000-0000-0000-000000000000}:isdn_wcs.sln
++解决方案项
i:{00000000-0000-0000-0000-000000000000}:解决方案项
++.editorConfig
i:{06f6111d-f0a0-4572-92d8-4bc2abecbdf3}:E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\.editorConfig
++Directory.Build.props
i:{06f6111d-f0a0-4572-92d8-4bc2abecbdf3}:E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\Directory.Build.props
++Directory.Build.targets
i:{06f6111d-f0a0-4572-92d8-4bc2abecbdf3}:E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\Directory.Build.targets
++RuoYi.Admin
i:{00000000-0000-0000-0000-000000000000}:RuoYi.Admin
++Connected Services 
i:{************************************}:>2983
++Properties
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\properties\
++wwwroot
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\wwwroot\
++依赖项
i:{************************************}:>2992
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>2991
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>2987
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:>2986
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>2985
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>2988
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>2990
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>2989
++Authorization
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\authorization\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\
++Common
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\
++Controllers
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\controllers\
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\controllers\
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\monitor\controllers\
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\controllers\
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\controllers\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\controllers\
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\controllers\
++HomeController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\controllers\homecontroller.cs
++SampleController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\controllers\samplecontroller.cs
++logs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\
++application-Error-2025-08-27.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-error-2025-08-27.log
++application-Error-2025-08-28.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-error-2025-08-28.log
++application-Error-2025-09-01.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-error-2025-09-01.log
++application-Error-2025-09-02.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-error-2025-09-02.log
++application-Error-2025-09-03.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-error-2025-09-03.log
++application-Error-2025-09-04.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-error-2025-09-04.log
++application-Information-2025-08-27.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-information-2025-08-27.log
++application-Information-2025-08-28.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-information-2025-08-28.log
++application-Information-2025-09-01.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-information-2025-09-01.log
++application-Information-2025-09-02.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-information-2025-09-02.log
++application-Information-2025-09-03.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-information-2025-09-03.log
++application-Information-2025-09-04.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-information-2025-09-04.log
++application-Warning-2025-08-27.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-warning-2025-08-27.log
++application-Warning-2025-08-28.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-warning-2025-08-28.log
++application-Warning-2025-09-01.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-warning-2025-09-01.log
++application-Warning-2025-09-02.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-warning-2025-09-02.log
++application-Warning-2025-09-03.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-warning-2025-09-03.log
++application-Warning-2025-09-04.log
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\logs\application-warning-2025-09-04.log
++Monitor
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\monitor\
++Views
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\views\
++Home
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\views\home\
++Index.cshtml
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\views\home\index.cshtml
++Shared
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\views\shared\
++_ViewImports.cshtml
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\views\_viewimports.cshtml
++_ViewStart.cshtml
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\views\_viewstart.cshtml
++appsettings.json
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\appsettings.json
++appsettings.Development.json
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\appsettings.development.json
++appsettings.Production.json
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\appsettings.production.json
++Dockerfile
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\dockerfile
++GlobalUsings.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\globalusings.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\globalusings.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\globalusings.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\globalusings.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\globalusings.cs
++Program.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\program.cs
++RuoYi.Admin.xml
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\ruoyi.admin.xml
++SingleFilePublish.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\singlefilepublish.cs
++Startup.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\startup.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\startup.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\startup.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\startup.cs
++未发现任何服务依赖项
i:{************************************}:>2984
++PublishProfiles
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\properties\publishprofiles\
++launchSettings.json
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\properties\launchsettings.json
++images
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\wwwroot\images\
++分析器
i:{************************************}:>3094
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3124
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>3146
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:>2993
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3066
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3017
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3164
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3042
++框架
i:{************************************}:>3116
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3140
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>3162
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:>3007
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3082
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3033
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3180
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3058
++项目
i:{************************************}:>3086
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3119
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>3142
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3063
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3012
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3037
++AuthorizationExtensions.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\authorization\authorizationextensions.cs
++AuthorizationMiddlewareResultHandler.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\authorization\authorizationmiddlewareresulthandler.cs
++JwtHandler.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\authorization\jwthandler.cs
++Dtos
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\dtos\
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\dtos\
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\slave\dtos\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\dtos\
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\dtos\
++Services
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\services\
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\services\
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\slave\services\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\services\
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\services\
++_Layout.cshtml
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\views\shared\_layout.cshtml
++.dockerignore
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\.dockerignore
++FolderProfile.pubxml
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\properties\publishprofiles\folderprofile.pubxml
++FolderProfile1.pubxml
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\properties\publishprofiles\folderprofile1.pubxml
++logo.png
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\wwwroot\images\logo.png
++Microsoft.AspNetCore.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.Analyzers
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{************************************}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.Extensions.Logging.Generators
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.6\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\8.0.17\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{************************************}:c:\program files\dotnet\sdk\9.0.301\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.6\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.17\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{************************************}:>3117
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:>3008
++Microsoft.NETCore.App
i:{************************************}:>3118
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3141
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>3163
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:>3009
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3083
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3034
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3181
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3059
++ISDN.WCS
i:{************************************}:>3091
i:{00000000-0000-0000-0000-000000000000}:ISDN.WCS
++RuoYi.Common
i:{************************************}:>3088
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3121
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>3144
i:{00000000-0000-0000-0000-000000000000}:RuoYi.Common
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3014
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3039
++RuoYi.Data
i:{************************************}:>3089
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3122
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>3145
i:{00000000-0000-0000-0000-000000000000}:RuoYi.Data
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3065
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3015
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3040
++RuoYi.Framework
i:{************************************}:>3087
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3120
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:>3143
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3064
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3013
i:{00000000-0000-0000-0000-000000000000}:RuoYi.Framework
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3038
++RuoYi.Generator
i:{************************************}:>3093
i:{00000000-0000-0000-0000-000000000000}:RuoYi.Generator
++RuoYi.Quartz
i:{************************************}:>3090
i:{00000000-0000-0000-0000-000000000000}:RuoYi.Quartz
++RuoYi.System
i:{************************************}:>3092
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:>3123
i:{00000000-0000-0000-0000-000000000000}:RuoYi.System
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3016
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3041
++CaptchaController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\controllers\captchacontroller.cs
++IndexController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\controllers\indexcontroller.cs
++Mapper.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\dtos\mapper.cs
++ISystemService.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\services\isystemservice.cs
++SystemService.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\common\services\systemservice.cs
++CacheController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\monitor\controllers\cachecontroller.cs
++ServerController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\monitor\controllers\servercontroller.cs
++SysLogininforController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\monitor\controllers\syslogininforcontroller.cs
++SysOperLogController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\monitor\controllers\sysoperlogcontroller.cs
++SysUserOnlineController.cs
i:{************************************}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.admin\monitor\controllers\sysuseronlinecontroller.cs
++TestController.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\controllers\testcontroller.cs
++WcsContainerController.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\controllers\wcscontainercontroller.cs
++WcsDeviceController.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\controllers\wcsdevicecontroller.cs
++WcsLaneController.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\controllers\wcslanecontroller.cs
++WcsTaskController.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\controllers\wcstaskcontroller.cs
++WcsWarehouseLocationController.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\controllers\wcswarehouselocationcontroller.cs
++Entities
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\entities\
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\slave\entities\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\entities\
++PLC
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\
++Dto
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\
++Socket
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socket\
++PLCClientSocket.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socket\plcclientsocket.cs
++PLCClientSocketManager.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socket\plcclientsocketmanager.cs
++PLCServerSocket.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socket\plcserversocket.cs
++SocketEventArgs.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socket\socketeventargs.cs
++SocketServer
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketserver\
++SocketService
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketservice\
++LittleEndianHelper.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketservice\littleendianhelper.cs
++PlcToWcs.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketservice\plctowcs.cs
++ServiceCollectionExtensions.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketservice\servicecollectionextensions.cs
++SocketBackgroundService.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketservice\socketbackgroundservice.cs
++SocketService.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketservice\socketservice.cs
++WcsToPlc.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\socketservice\wcstoplc.cs
++Repositories
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\repositories\
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\slave\repositories\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\repositories\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\repositories\
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\repositories\
++WcsContainerService.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\services\wcscontainerservice.cs
++WcsDeviceService.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\services\wcsdeviceservice.cs
++WcsLaneService.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\services\wcslaneservice.cs
++WcsTaskService.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\services\wcstaskservice.cs
++WcsWarehouseLocationService.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\services\wcswarehouselocationservice.cs
++ExtensionsClass.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\extensionsclass.cs
++WcsContainerDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\dtos\wcscontainerdto.cs
++WcsDeviceDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\dtos\wcsdevicedto.cs
++WcsLaneDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\dtos\wcslanedto.cs
++WcsTaskDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\dtos\wcstaskdto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\wcstaskdto.cs
++WcsWarehouseLocationDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\dtos\wcswarehouselocationdto.cs
++WcsContainer.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\entities\wcscontainer.cs
++WcsDevice.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\entities\wcsdevice.cs
++WcsLane.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\entities\wcslane.cs
++WcsTask.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\entities\wcstask.cs
++WcsTaskStep.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\entities\wcstaskstep.cs
++WcsWarehouseLocation.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\entities\wcswarehouselocation.cs
++CheckPointDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\checkpointdto.cs
++PLCEquipmentDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\plcequipmentdto.cs
++PLCMeasurementDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\plcmeasurementdto.cs
++PLCTaskStatusDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\plctaskstatusdto.cs
++PLCWeighingDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\plcweighingdto.cs
++ResultDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\resultdto.cs
++WcsFeedbackDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\wcsfeedbackdto.cs
++WmsTaskDto.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\plc\dto\wmstaskdto.cs
++WcsContainerRepository.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\repositories\wcscontainerrepository.cs
++WcsDeviceRepository.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\repositories\wcsdevicerepository.cs
++WcsLaneRepository.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\repositories\wcslanerepository.cs
++WcsTaskRepository.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\repositories\wcstaskrepository.cs
++WcsWarehouseLocationRepository.cs
i:{f166745c-478b-42d3-9d9d-96482bd099e1}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\isdn.wcs\repositories\wcswarehouselocationrepository.cs
++Interceptors
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\interceptors\
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\interceptors\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\interceptors\
++Slave
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\slave\
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\slave\
++SysConfigController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysconfigcontroller.cs
++SysDeptController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysdeptcontroller.cs
++SysDictDataController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysdictdatacontroller.cs
++SysDictTypeController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysdicttypecontroller.cs
++SysLoginController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\syslogincontroller.cs
++SysMenuController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysmenucontroller.cs
++SysNoticeController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysnoticecontroller.cs
++SysPostController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\syspostcontroller.cs
++SysProfileController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysprofilecontroller.cs
++SysRegisterController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysregistercontroller.cs
++SysRoleController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysrolecontroller.cs
++SysUserController.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\controllers\sysusercontroller.cs
++LogAttribute.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\interceptors\logattribute.cs
++SysConfigRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysconfigrepository.cs
++SysDeptRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysdeptrepository.cs
++SysDictDataRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysdictdatarepository.cs
++SysDictTypeRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysdicttyperepository.cs
++SysLogininforRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\syslogininforrepository.cs
++SysMenuRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysmenurepository.cs
++SysNoticeRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysnoticerepository.cs
++SysOperLogRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysoperlogrepository.cs
++SysPostRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\syspostrepository.cs
++SysRoleDeptRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysroledeptrepository.cs
++SysRoleMenuRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysrolemenurepository.cs
++SysRoleRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysrolerepository.cs
++SysUserPostRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysuserpostrepository.cs
++SysUserRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysuserrepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\slave\repositories\sysuserrepository.cs
++SysUserRoleRepository.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\repositories\sysuserrolerepository.cs
++ServerService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\serverservice.cs
++SysConfigService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysconfigservice.cs
++SysDeptService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysdeptservice.cs
++SysDictDataService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysdictdataservice.cs
++SysDictTypeService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysdicttypeservice.cs
++SysLogininforService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\syslogininforservice.cs
++SysLoginService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysloginservice.cs
++SysMenuService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysmenuservice.cs
++SysNoticeService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysnoticeservice.cs
++SysOperLogService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysoperlogservice.cs
++SysPasswordService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\syspasswordservice.cs
++SysPermissionService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\syspermissionservice.cs
++SysPostService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\syspostservice.cs
++SysRegisterService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysregisterservice.cs
++SysRoleDeptService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysroledeptservice.cs
++SysRoleMenuService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysrolemenuservice.cs
++SysRoleService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysroleservice.cs
++SysUserOnlineService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysuseronlineservice.cs
++SysUserPostService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysuserpostservice.cs
++SysUserService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\sysuserservice.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\slave\services\sysuserservice.cs
++TokenService.cs
i:{4163d950-81ca-475d-a4e1-dfe95cecba68}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.system\services\tokenservice.cs
++Attributes
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\apicontroller\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\attributes\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\attributes\
++Constants
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\constants\
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\constants\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\constants\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configuration\constants\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\constants\
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\constants\
++Models
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\
++包
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:>3010
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3084
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3035
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3182
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3060
++ExcelAttribute.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\attributes\excelattribute.cs
++CacheConstants.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\constants\cacheconstants.cs
++Constants.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\constants\constants.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configuration\constants\constants.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\constants\constants.cs
++DataConstants.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\constants\dataconstants.cs
++BaseDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\basedto.cs
++GenTableColumnDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\gentablecolumndto.cs
++GenTableDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\gentabledto.cs
++SysConfigDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysconfigdto.cs
++SysDeptDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysdeptdto.cs
++SysDictDataDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysdictdatadto.cs
++SysDictTypeDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysdicttypedto.cs
++SysLogininforDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\syslogininfordto.cs
++SysMenuDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysmenudto.cs
++SysNoticeDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysnoticedto.cs
++SysOperLogDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysoperlogdto.cs
++SysPostDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\syspostdto.cs
++SysRoleDeptDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysroledeptdto.cs
++SysRoleDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysroledto.cs
++SysRoleMenuDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysrolemenudto.cs
++SysUserDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysuserdto.cs
++SysUserPostDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysuserpostdto.cs
++SysUserRoleDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\sysuserroledto.cs
++TreeDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\dtos\treedto.cs
++BaseEntity.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\baseentity.cs
++GenTable.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\gentable.cs
++GenTableColumn.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\gentablecolumn.cs
++SysConfig.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysconfig.cs
++SysDept.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysdept.cs
++SysDictData.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysdictdata.cs
++SysDictType.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysdicttype.cs
++SysLogininfor.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\syslogininfor.cs
++SysMenu.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysmenu.cs
++SysNotice.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysnotice.cs
++SysOperLog.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysoperlog.cs
++SysPost.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\syspost.cs
++SysRole.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysrole.cs
++SysRoleDept.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysroledept.cs
++SysRoleMenu.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysrolemenu.cs
++SysUser.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysuser.cs
++SysUserPost.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysuserpost.cs
++SysUserRole.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\sysuserrole.cs
++TreeEntity.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\entities\treeentity.cs
++Server
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\server\
++LoginBody.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\loginbody.cs
++LoginUser.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\loginuser.cs
++MetaVo.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\metavo.cs
++RegisterBody.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\registerbody.cs
++RouterVo.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\routervo.cs
++SysCache.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\syscache.cs
++SysUserOnline.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\sysuseronline.cs
++TreeSelect.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\treeselect.cs
++SqlSugarCore (5.1.4.170)
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:>3011
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3194
++Clr.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\server\clr.cs
++Cpu.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\server\cpu.cs
++Mem.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\server\mem.cs
++Server.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\server\server.cs
++Sys.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\server\sys.cs
++SysFile.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\models\server\sysfile.cs
++SlaveSysUserDto.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\slave\dtos\slavesysuserdto.cs
++SlaveSysUser.cs
i:{35b3c60c-7628-4c52-b696-c856ab462b66}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.data\slave\entities\slavesysuser.cs
++Data
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\data\
++Enums
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\enums\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\enums\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\enums\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\enums\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\enums\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\enums\
++Files
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\files\
++Utils
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\utils\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\utils\
++AppConstants.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\constants\appconstants.cs
++DataScopeConstants.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\constants\datascopeconstants.cs
++MessageConstants.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\constants\messageconstants.cs
++UserConstants.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\constants\userconstants.cs
++CommonController.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\controllers\commoncontroller.cs
++BaseRepository.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\data\baserepository.cs
++BaseService.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\data\baseservice.cs
++PageDomain.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\data\pagedomain.cs
++BusinessStatus.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\enums\businessstatus.cs
++BusinessType.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\enums\businesstype.cs
++OperatorType.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\enums\operatortype.cs
++UserStatus.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\enums\userstatus.cs
++FileExtensions.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\files\fileextensions.cs
++FileUploadUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\files\fileuploadutils.cs
++DataScopeAttribute.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\interceptors\datascopeattribute.cs
++PermissionContextHolder.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\interceptors\permissioncontextholder.cs
++AddressUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\addressutils.cs
++ConvertUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\convertutils.cs
++DictUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\dictutils.cs
++ExcelUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\excelutils.cs
++LogUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\logutils.cs
++MimeTypeUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\mimetypeutils.cs
++PageUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\pageutils.cs
++SecurityUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\securityutils.cs
++StringUtils.cs
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.common\utils\stringutils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\stringutils.cs
++BCrypt.Net-Next (4.0.3)
i:{8accb8aa-8ec3-45c3-ac07-d21d67b3d0fa}:>3085
++Jobs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\jobs\
++Tasks
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\tasks\
++ScheduleConstants.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\constants\scheduleconstants.cs
++SysJobController.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\controllers\sysjobcontroller.cs
++SysJobLogController.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\controllers\sysjoblogcontroller.cs
++SysJobDto.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\dtos\sysjobdto.cs
++SysJobLogDto.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\dtos\sysjoblogdto.cs
++SysJob.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\entities\sysjob.cs
++SysJobLog.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\entities\sysjoblog.cs
++ConcurrentStatus.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\enums\concurrentstatus.cs
++JobMisfirePolicy.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\enums\jobmisfirepolicy.cs
++ScheduleStatus.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\enums\schedulestatus.cs
++TaskCode.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\enums\taskcode.cs
++AbstractQuartzJob.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\jobs\abstractquartzjob.cs
++QuartzDisallowConcurrentExecution.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\jobs\quartzdisallowconcurrentexecution.cs
++QuartzExecution.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\jobs\quartzexecution.cs
++SysJobLogRepository.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\repositories\sysjoblogrepository.cs
++SysJobRepository.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\repositories\sysjobrepository.cs
++SysJobLogService.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\services\sysjoblogservice.cs
++SysJobService.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\services\sysjobservice.cs
++RyTask.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\tasks\rytask.cs
++CronUtils.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\utils\cronutils.cs
++JobInvokeUtils.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\utils\jobinvokeutils.cs
++ScheduleUtils.cs
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.quartz\utils\scheduleutils.cs
++Quartz (3.13.1)
i:{21f845aa-320a-43b0-b5a5-5929445a2c64}:>3036
++ApiController
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\apicontroller\
++App
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\
++AspNetCore
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\
++Cache
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\
++Captcha
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\captcha\
++Components
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\
++Configs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configs\
++ConfigurableOptions
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\
++Configuration
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configuration\
++CorsAccessor
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\
++DataEncryption
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\
++DataValidation
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\
++DependencyInjection
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\
++Exceptions
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\exceptions\
++Extensions
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configuration\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jwtbearer\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\templates\extensions\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\extensions\
++Filters
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\filters\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\filters\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\filters\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\filters\
++JsonSerialization
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\jsonserialization\
++JwtBearer
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jwtbearer\
++Localization
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\
++Logging
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\
++Options
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jwtbearer\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\options\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\extensions\options\
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\options\
++RateLimit
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\
++Reflection
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\
++RemoteRequest
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\
++SpecificationDocument
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\
++SqlSugar
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\
++Templates
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\templates\
++UnifyResult
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unifyresult\
++UnitOfWork
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\unitofwork\
++VirtualFileServer
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\virtualfileserver\
++AjaxResult.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ajaxresult.cs
++RyApp.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ryapp.cs
++Penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\apicontroller\penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\internal\penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\internal\penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\internal\penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\internal\penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\internal\penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\internal\penetrates.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\internal\penetrates.cs
++Internal
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\internal\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\internal\
++SingleFile
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\singlefile\
++Startups
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\startups\
++App.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\app.cs
++ModelBinders
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\
++BadPageResult.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\badpageresult.cs
++TaskAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\attributes\taskattribute.cs
++Handlers
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\handlers\
++Providers
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\providers\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\providers\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\providers\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\providers\
++Requirements
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\requirements\
++Memory
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\memory\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\memory\
++Redis
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\redis\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\redis\
++ICache.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\icache.cs
++CaptchaExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\captcha\captchaextensions.cs
++Contexts
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\contexts\
++Dependencies
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\dependencies\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\dependencies\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\dependencies\
++IApplicationComponent.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\iapplicationcomponent.cs
++IServiceComponent.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\iservicecomponent.cs
++IWebComponent.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\iwebcomponent.cs
++RuoYiConfig.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configs\ruoyiconfig.cs
++UserConfig.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configs\userconfig.cs
++Encryptions
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\encryptions\
++Validators
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\validators\
++ValidatorContext.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\validatorcontext.cs
++Scoped.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\scoped.cs
++Assets
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\exceptions\assets\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\assets\
++Retry.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\exceptions\retry.cs
++ServiceException.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\exceptions\serviceexception.cs
++DateTimeExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\extensions\datetimeextensions.cs
++EnumExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\extensions\enumextensions.cs
++ListExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\extensions\listextensions.cs
++ObjectExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\extensions\objectextensions.cs
++GlobalExceptionFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\filters\globalexceptionfilter.cs
++TransactionalInterceptor.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\interceptors\transactionalinterceptor.cs
++Converters
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\
++JSON.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\json.cs
++JWTEncryption.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jwtbearer\jwtencryption.cs
++L.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\l.cs
++Implantations
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\
++Log.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\log.cs
++GlobalRateLimitConfig.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\globalratelimitconfig.cs
++IpRateLimitConfig.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\ipratelimitconfig.cs
++LimitType.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\limittype.cs
++RateLimitExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\ratelimitextensions.cs
++DynamicProxies
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\dynamicproxies\
++Proxies
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\proxies\
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\proxies\
++Reflect.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\reflect.cs
++Events
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\events\
++Http.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\http.cs
++Builders
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\builders\
++SqlSugarSetup.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\sqlsugarsetup.cs
++TP.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\templates\tp.cs
++UnifyContext.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unifyresult\unifycontext.cs
++FilterAttributes
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\filterattributes\
++IUnitOfWork.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\iunitofwork.cs
++AssemblyUtils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\assemblyutils.cs
++CmdUtils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\cmdutils.cs
++ExceptionUtils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\exceptionutils.cs
++IpUtils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\iputils.cs
++MathUtils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\mathutils.cs
++ReflectUtils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\reflectutils.cs
++ZipUtils.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\utils\ziputils.cs
++FS.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\virtualfileserver\fs.cs
++AspectCore.Abstractions (2.4.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3183
++AspectCore.Extensions.DependencyInjection (2.4.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3197
++Ben.Demystifier (0.4.1)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3192
++Hardware.Info (101.0.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3188
++Lazy.Captcha.Core (2.0.9)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3203
++Mapster (7.4.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3191
++Microsoft.AspNetCore.Authentication.JwtBearer (8.0.10)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3196
++Microsoft.AspNetCore.Mvc.NewtonsoftJson (8.0.10)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3189
++Microsoft.CodeAnalysis.CSharp (4.11.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3184
++Microsoft.Extensions.Caching.StackExchangeRedis (8.0.10)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3202
++Microsoft.Extensions.DependencyModel (8.0.2)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3199
++MiniExcel (1.34.2)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3198
++MiniProfiler.AspNetCore.Mvc (4.3.8)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3190
++MiniProfiler.Shared (4.3.8)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3195
++RedisRateLimiting.AspNetCore (1.2.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3204
++SharpZipLib (1.4.2)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3200
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3062
++SkiaSharp.NativeAssets.Linux.NoDependencies (2.88.9)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3193
++Swashbuckle.AspNetCore.Swagger (8.0.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3185
++Swashbuckle.AspNetCore.SwaggerGen (8.0.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3187
++Swashbuckle.AspNetCore.SwaggerUI (8.0.0)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3201
++UAParser.Core (4.0.4)
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:>3186
++ApiDescriptionSettingsAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\apicontroller\attributes\apidescriptionsettingsattribute.cs
++AppStartupAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\attributes\appstartupattribute.cs
++AppApplicationBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\appapplicationbuilderextensions.cs
++AppServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\appservicecollectionextensions.cs
++AppWebApplicationBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\appwebapplicationbuilderextensions.cs
++HostBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\hostbuilderextensions.cs
++IConfigurationExtenstions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\iconfigurationextenstions.cs
++StartupFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\filters\startupfilter.cs
++InternalApp.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\internal\internalapp.cs
++AppSettingsOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\options\appsettingsoptions.cs
++ISingleFilePublish.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\singlefile\isinglefilepublish.cs
++AppStartup.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\startups\appstartup.cs
++FakeStartup.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\startups\fakestartup.cs
++GenericHostLifetimeEventsHostedService.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\startups\generichostlifetimeeventshostedservice.cs
++HostingStartup.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\startups\hostingstartup.cs
++AspNetCoreBuilderServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\extensions\aspnetcorebuilderservicecollectionextensions.cs
++HttpContextExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\extensions\httpcontextextensions.cs
++IHostExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\extensions\ihostextensions.cs
++ModelBindingContextExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\extensions\modelbindingcontextextensions.cs
++Binders
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\binders\
++Converts
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\converts\
++AppAuthorizeAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\attributes\appauthorizeattribute.cs
++AppRoleAuthorizeAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\attributes\approleauthorizeattribute.cs
++AuthorizationHandlerContextExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\extensions\authorizationhandlercontextextensions.cs
++AuthorizationServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\extensions\authorizationservicecollectionextensions.cs
++AppAuthorizeHandler.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\handlers\appauthorizehandler.cs
++AppAuthorizationPolicyProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\providers\appauthorizationpolicyprovider.cs
++AppAuthorizeRequirement.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\authorization\requirements\appauthorizerequirement.cs
++CacheType.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\enums\cachetype.cs
++CacheExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\extensions\cacheextensions.cs
++MemoryCache.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\memory\memorycache.cs
++CacheConfig.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\options\cacheconfig.cs
++RedisConfig.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\options\redisconfig.cs
++InfoDetail.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\redis\infodetail.cs
++RedisCache.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\cache\redis\rediscache.cs
++DependsOnAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\attributes\dependsonattribute.cs
++ComponentContext.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\contexts\componentcontext.cs
++IComponent.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\dependencies\icomponent.cs
++ComponentApplicationBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\extensions\componentapplicationbuilderextensions.cs
++ComponentServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\components\extensions\componentservicecollectionextensions.cs
++MapSettingsAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\attributes\mapsettingsattribute.cs
++OptionsSettingsAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\attributes\optionssettingsattribute.cs
++ConfigurableOptionsServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\extensions\configurableoptionsservicecollectionextensions.cs
++IConfigurableOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configurableoptions\options\iconfigurableoptions.cs
++IConfigurationBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configuration\extensions\iconfigurationbuilderextensions.cs
++IConfigurationExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\configuration\extensions\iconfigurationextensions.cs
++CorsAccessorApplicationBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\extensions\corsaccessorapplicationbuilderextensions.cs
++CorsAccessorServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\extensions\corsaccessorservicecollectionextensions.cs
++CorsAccessorSettingsOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\corsaccessor\options\corsaccessorsettingsoptions.cs
++AESEncryption.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\encryptions\aesencryption.cs
++DESCEncryption.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\encryptions\descencryption.cs
++MD5Encryption.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\encryptions\md5encryption.cs
++RSAEncryption.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\encryptions\rsaencryption.cs
++StringEncryptionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dataencryption\extensions\stringencryptionextensions.cs
++DataValidationAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\attributes\datavalidationattribute.cs
++NonValidationAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\attributes\nonvalidationattribute.cs
++ValidationItemMetadataAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\attributes\validationitemmetadataattribute.cs
++ValidationMessageAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\attributes\validationmessageattribute.cs
++ValidationMessageTypeAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\attributes\validationmessagetypeattribute.cs
++ValidationTypeAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\attributes\validationtypeattribute.cs
++ValidationPattern.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\enums\validationpattern.cs
++ValidationTypes.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\enums\validationtypes.cs
++DataValidationExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\extensions\datavalidationextensions.cs
++DataValidationServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\extensions\datavalidationservicecollectionextensions.cs
++DataValidationFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\filters\datavalidationfilter.cs
++DataValidationPageFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\filters\datavalidationpagefilter.cs
++DataValidationResult.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\internal\datavalidationresult.cs
++ValidationMetadata.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\internal\validationmetadata.cs
++ValidationTypeMessageSettingsOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\options\validationtypemessagesettingsoptions.cs
++IValidationMessageTypeProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\providers\ivalidationmessagetypeprovider.cs
++DataValidator.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\validators\datavalidator.cs
++InjectionAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\attributes\injectionattribute.cs
++SuppressProxyAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\attributes\suppressproxyattribute.cs
++SuppressSnifferAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\attributes\suppresssnifferattribute.cs
++IPrivateDependency.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\dependencies\iprivatedependency.cs
++IScoped.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\dependencies\iscoped.cs
++ISingleton.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\dependencies\isingleton.cs
++ITransient.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\dependencies\itransient.cs
++InjectionActions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\enums\injectionactions.cs
++InjectionPatterns.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\enums\injectionpatterns.cs
++RegisterType.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\enums\registertype.cs
++DependencyInjectionServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\extensions\dependencyinjectionservicecollectionextensions.cs
++ExternalService.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\internal\externalservice.cs
++DependencyInjectionSettingsOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\options\dependencyinjectionsettingsoptions.cs
++INamedServiceProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\providers\inamedserviceprovider.cs
++NamedServiceProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\dependencyinjection\providers\namedserviceprovider.cs
++error.html
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\exceptions\assets\error.html
++NewtonsoftJson
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\newtonsoftjson\
++SystemTextJson
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\systemtextjson\
++JsonSerializationServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\extensions\jsonserializationservicecollectionextensions.cs
++NewtonsoftJsonExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\extensions\newtonsoftjsonextensions.cs
++SystemTextJsonExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\extensions\systemtextjsonextensions.cs
++IJsonSerializerProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\providers\ijsonserializerprovider.cs
++NewtonsoftJsonSerializerProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\providers\newtonsoftjsonserializerprovider.cs
++SystemTextJsonSerializerProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\providers\systemtextjsonserializerprovider.cs
++JWTAuthorizationServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jwtbearer\extensions\jwtauthorizationservicecollectionextensions.cs
++JWTSettingsOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jwtbearer\options\jwtsettingsoptions.cs
++IHtmlLocalizerFactoryExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\extensions\ihtmllocalizerfactoryextensions.cs
++ILocalizerExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\extensions\ilocalizerextensions.cs
++IStringLocalizerFactoryExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\extensions\istringlocalizerfactoryextensions.cs
++LocalizationApplicationBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\extensions\localizationapplicationbuilderextensions.cs
++LocalizationServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\extensions\localizationservicecollectionextensions.cs
++LocalizationSettingsOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\localization\options\localizationsettingsoptions.cs
++ILoggerExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\iloggerextensions.cs
++ILoggerFactoryExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\iloggerfactoryextensions.cs
++ILoggingBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\iloggingbuilderextensions.cs
++LogContextExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\logcontextextensions.cs
++LoggingServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\loggingservicecollectionextensions.cs
++LogMessageExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\logmessageextensions.cs
++StringLoggingExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\extensions\stringloggingextensions.cs
++Console
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\console\
++Database
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\database\
++Empty
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\empty\
++File
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\file\
++Monitors
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\
++LogContext.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\logcontext.cs
++LogMessage.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\logmessage.cs
++Logging.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\internal\logging.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\internal\logging.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\logging.cs
++StringLoggingPart.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\internal\stringloggingpart.cs
++StringLoggingPartMethods.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\internal\stringloggingpartmethods.cs
++StringLoggingPartSetters.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\internal\stringloggingpartsetters.cs
++FailureMessageAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\attributes\failuremessageattribute.cs
++OptionsBuilderAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\attributes\optionsbuilderattribute.cs
++OptionsBuilderMethodMapAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\attributes\optionsbuildermethodmapattribute.cs
++IConfigureOptionsBuilder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\dependencies\iconfigureoptionsbuilder.cs
++IOptionsBuilderDependency.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\dependencies\ioptionsbuilderdependency.cs
++IPostConfigureOptionsBuilder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\dependencies\ipostconfigureoptionsbuilder.cs
++IValidateOptionsBuilder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\dependencies\ivalidateoptionsbuilder.cs
++OptionsBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\options\extensions\optionsbuilderextensions.cs
++MemoryGlobalRateLimiterPolicy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\memory\memoryglobalratelimiterpolicy.cs
++MemoryIpRateLimiterPolicy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\memory\memoryipratelimiterpolicy.cs
++GlobalRateLimiterPolicy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\redis\globalratelimiterpolicy.cs
++IpRateLimiterPolicy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\ratelimit\redis\ipratelimiterpolicy.cs
++ClassProxyGenerator.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\dynamicproxies\classproxygenerator.cs
++DynamicDispatchProxy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\dynamicproxies\dynamicdispatchproxy.cs
++Invocation.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\dynamicproxies\invocation.cs
++MethodInfoExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\extensions\methodinfoextensions.cs
++MethodParameterInfo.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\internal\methodparameterinfo.cs
++AspectDispatchProxy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\proxies\aspectdispatchproxy.cs
++AspectDispatchProxyGenerator.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\proxies\aspectdispatchproxygenerator.cs
++IDispatchProxy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\proxies\idispatchproxy.cs
++IGlobalDispatchProxy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\reflection\proxies\iglobaldispatchproxy.cs
++HttpMethods
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\
++Parameters
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\parameters\
++ClientAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\clientattribute.cs
++HeadersAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\headersattribute.cs
++InterceptorAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\interceptorattribute.cs
++RetryPolicyAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\retrypolicyattribute.cs
++InterceptorTypes.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\enums\interceptortypes.cs
++HttpRequestFaildedEventArgs.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\events\httprequestfaildedeventargs.cs
++HttpRequestMessageExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\extensions\httprequestmessageextensions.cs
++HttpResponseMessageExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\extensions\httpresponsemessageextensions.cs
++RemoteRequestServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\extensions\remoterequestservicecollectionextensions.cs
++RemoteRequestStringExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\extensions\remoterequeststringextensions.cs
++HttpFile.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\internal\httpfile.cs
++HttpRequestPart.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\internal\httprequestpart.cs
++HttpRequestPartMethods.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\internal\httprequestpartmethods.cs
++HttpRequestPartSetters.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\internal\httprequestpartsetters.cs
++RepeatKeyEqualityComparer.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\internal\repeatkeyequalitycomparer.cs
++HttpDispatchProxy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\proxies\httpdispatchproxy.cs
++IHttpDispatchProxy.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\proxies\ihttpdispatchproxy.cs
++index.html
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\assets\index.html
++index-mini-profiler.html
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\assets\index-mini-profiler.html
++EnumToNumberAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\attributes\enumtonumberattribute.cs
++OperationIdAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\attributes\operationidattribute.cs
++SchemaIdAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\attributes\schemaidattribute.cs
++SpecificationDocumentBuilder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\builders\specificationdocumentbuilder.cs
++SpecificationDocumentApplicationBuilderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\extensions\specificationdocumentapplicationbuilderextensions.cs
++SpecificationDocumentServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\extensions\specificationdocumentservicecollectionextensions.cs
++AnySchemaFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\filters\anyschemafilter.cs
++ApiActionFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\filters\apiactionfilter.cs
++EnumSchemaFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\filters\enumschemafilter.cs
++TagsOrderDocumentFilter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\filters\tagsorderdocumentfilter.cs
++GroupExtraInfo.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\internal\groupextrainfo.cs
++SpecificationAuth.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\internal\specificationauth.cs
++SpecificationLoginInfo.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\internal\specificationlogininfo.cs
++SpecificationOpenApiInfo.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\internal\specificationopenapiinfo.cs
++SpecificationOpenApiSecurityRequirementItem.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\internal\specificationopenapisecurityrequirementitem.cs
++SpecificationOpenApiSecurityScheme.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\internal\specificationopenapisecurityscheme.cs
++SpecificationDocumentSettingsOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\specificationdocument\options\specificationdocumentsettingsoptions.cs
++PagedQueryableExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\extensions\pagedqueryableextensions.cs
++SqlSugarServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\extensions\sqlsugarservicecollectionextensions.cs
++SqlProfiler.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\internal\sqlprofiler.cs
++SqlSugarPagedList.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\internal\sqlsugarpagedlist.cs
++ISqlSugarRepository.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\repositories\isqlsugarrepository.cs
++SqlSugarRepository.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\repositories\sqlsugarrepository.cs
++SqlSugarUnitOfWork.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\sqlsugar\unitofwork\sqlsugarunitofwork.cs
++StringRenderExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\templates\extensions\stringrenderextensions.cs
++UnitOfWorkServiceCollectionExtensions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\extensions\unitofworkservicecollectionextensions.cs
++UnitOfWorkAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\unitofwork\filterattributes\unitofworkattribute.cs
++AddInjectOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\options\addinjectoptions.cs
++InjectOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\options\injectoptions.cs
++UseInjectOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\app\extensions\options\useinjectoptions.cs
++FromConvertAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\attributes\fromconvertattribute.cs
++FromConvertBinder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\binders\fromconvertbinder.cs
++FromConvertBinderProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\binders\fromconvertbinderprovider.cs
++TimestampToDateTimeModelBinder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\binders\timestamptodatetimemodelbinder.cs
++DateTimeModelConvertBinder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\converts\datetimemodelconvertbinder.cs
++DateTimeOffsetModelConvertBinder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\converts\datetimeoffsetmodelconvertbinder.cs
++IModelConvertBinder.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\aspnetcore\modelbinders\converts\imodelconvertbinder.cs
++DataValidationOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\datavalidation\extensions\options\datavalidationoptions.cs
++NewtonsoftJsonDateOnlyJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\newtonsoftjson\newtonsoftjsondateonlyjsonconverter.cs
++NewtonsoftJsonDateTimeJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\newtonsoftjson\newtonsoftjsondatetimejsonconverter.cs
++NewtonsoftJsonDateTimeOffsetJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\newtonsoftjson\newtonsoftjsondatetimeoffsetjsonconverter.cs
++NewtonsoftJsonLongToStringJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\newtonsoftjson\newtonsoftjsonlongtostringjsonconverter.cs
++NewtonsoftJsonTimeOnlyJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\newtonsoftjson\newtonsoftjsontimeonlyjsonconverter.cs
++SystemTextJsonDateOnlyJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\systemtextjson\systemtextjsondateonlyjsonconverter.cs
++SystemTextJsonDateTimeJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\systemtextjson\systemtextjsondatetimejsonconverter.cs
++SystemTextJsonDateTimeOffsetJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\systemtextjson\systemtextjsondatetimeoffsetjsonconverter.cs
++SystemTextJsonLongToStringJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\systemtextjson\systemtextjsonlongtostringjsonconverter.cs
++SystemTextJsonTimeOnlyJsonConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\jsonserialization\converters\systemtextjson\systemtextjsontimeonlyjsonconverter.cs
++ConsoleColors.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\console\consolecolors.cs
++ConsoleFormatterExtend.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\console\consoleformatterextend.cs
++ConsoleFormatterExtendOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\console\consoleformatterextendoptions.cs
++DatabaseLogger.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\database\databaselogger.cs
++DatabaseLoggerOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\database\databaseloggeroptions.cs
++DatabaseLoggerProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\database\databaseloggerprovider.cs
++DatabaseLoggerSettings.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\database\databaseloggersettings.cs
++DatabaseWriteError.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\database\databasewriteerror.cs
++IDatabaseLoggingWriter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\database\idatabaseloggingwriter.cs
++EmptyLogger.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\empty\emptylogger.cs
++EmptyLoggerProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\empty\emptyloggerprovider.cs
++FileLogger.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\file\filelogger.cs
++FileLoggerOptions.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\file\fileloggeroptions.cs
++FileLoggerProvider.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\file\fileloggerprovider.cs
++FileLoggerSettings.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\file\fileloggersettings.cs
++FileLoggingWriter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\file\fileloggingwriter.cs
++FileWriteError.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\file\filewriteerror.cs
++ContractResolverTypes.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\contractresolvertypes.cs
++JsonBehavior.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\jsonbehavior.cs
++JsonElementConverter.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\jsonelementconverter.cs
++LoggingMonitorAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\loggingmonitorattribute.cs
++LoggingMonitorMethod.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\loggingmonitormethod.cs
++LoggingMonitorSettings.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\loggingmonitorsettings.cs
++PropertyNamesContractResolver.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\propertynamescontractresolver.cs
++SuppressMonitorAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\logging\implantations\monitors\suppressmonitorattribute.cs
++DeleteAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\deleteattribute.cs
++GetAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\getattribute.cs
++HeadAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\headattribute.cs
++HttpMethodBaseAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\httpmethodbaseattribute.cs
++PatchAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\patchattribute.cs
++PostAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\postattribute.cs
++PutAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\httpmethods\putattribute.cs
++JsonSerializationAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\jsonserialization\jsonserializationattribute.cs
++JsonSerializerOptionsAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\jsonserialization\jsonserializeroptionsattribute.cs
++BodyAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\parameters\bodyattribute.cs
++ParameterBaseAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\parameters\parameterbaseattribute.cs
++QueryStringAttribute.cs
i:{9ab3519d-e9fc-4158-b4af-5e2a1013ea82}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.framework\remoterequest\attributes\parameters\querystringattribute.cs
++RepoSql
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\reposql\
++StaticFiles
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\
++gensettings.json
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\gensettings.json
++GenConstants.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\constants\genconstants.cs
++GenController.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\controllers\gencontroller.cs
++SqlAndParameter.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\dtos\sqlandparameter.cs
++TemplateContext.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\dtos\templatecontext.cs
++GenOptions.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\options\genoptions.cs
++GenTableColumnRepository.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\repositories\gentablecolumnrepository.cs
++GenTableRepository.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\repositories\gentablerepository.cs
++IDbSql.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\reposql\idbsql.cs
++MySql.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\reposql\mysql.cs
++RepoSqlService.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\reposql\reposqlservice.cs
++SqlServer.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\reposql\sqlserver.cs
++GenTableColumnService.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\services\gentablecolumnservice.cs
++GenTableService.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\services\gentableservice.cs
++Vm
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\
++GenUtils.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\utils\genutils.cs
++TemplateUtils.cs
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\utils\templateutils.cs
++RazorEngineCore (2024.4.1)
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:>3061
++Js
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\js\
++Net
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\
++Sql
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\sql\
++Vue
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\vue\
++api.js.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\js\api.js.cshtml
++Controller.cs.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\controller.cs.cshtml
++Dto.cs.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\dto.cs.cshtml
++Entity.cs.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\entity.cs.cshtml
++Repository.cs.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\repository.cs.cshtml
++Service.cs.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\service.cs.cshtml
++SubDto.cs.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\subdto.cs.cshtml
++SubEntity.cs.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\net\subentity.cs.cshtml
++mysql.sql.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\sql\mysql.sql.cshtml
++sqlserver.sql.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\sql\sqlserver.sql.cshtml
++Vue2
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\vue\vue2\
++Vue3
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\vue\vue3\
++index.vue.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\vue\vue2\index.vue.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\vue\vue3\index.vue.cshtml
++index-tree.vue.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\vue\vue2\index-tree.vue.cshtml
i:{ef0267d8-3b3f-4cc1-9e4e-8cd14b11fb9b}:e:\sourcecode\selfsvncode\wcs\isdn_wcs\ruoyi.generator\staticfiles\vm\vue\vue3\index-tree.vue.cshtml

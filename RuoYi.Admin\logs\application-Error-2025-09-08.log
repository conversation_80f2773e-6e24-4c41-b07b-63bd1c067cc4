fail: 2025-09-08 08:55:51.7832607 +08:00 星期一 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: <PERSON><PERSON>; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-db7d8baaad750f9cf74cf080a7b86ae0-926a63a1039be156-00
      ┣ 服务线程 ID：               #22
      ┣ 执行耗时：                  24158ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    username=admin; rememberMe=true; ISDNLanguageType=1; authToken=Z6_BusModel:1_98035e15-7eba-48fd-a770-17c51cb2a90a; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; password=MsGWAvgc1IkC7xddHZPdbiD6ceGaY/ce3ZI52vqhMmEbyVJNkQMkU/TFFA145YkjdKwerE7fzjCxsBSYrPyFGQ==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjI1ZDVlNDcyLTIxMTMtNDIzNi1hMDVjLThiZTRhN2EyZjdkNyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc2NTUwNzgsImlhdCI6MTc1NzA1MDI3OCwibmJmIjoxNzU3MDUwMjc4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.NatvXPP3hnjs-e-9HXeBnoyoiqBqtosD3aaOkecn_Vw
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    username=admin; rememberMe=true; ISDNLanguageType=1; authToken=Z6_BusModel%3A1_98035e15-7eba-48fd-a770-17c51cb2a90a; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; password=MsGWAvgc1IkC7xddHZPdbiD6ceGaY/ce3ZI52vqhMmEbyVJNkQMkU/TFFA145YkjdKwerE7fzjCxsBSYrPyFGQ==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjI1ZDVlNDcyLTIxMTMtNDIzNi1hMDVjLThiZTRhN2EyZjdkNyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc2NTUwNzgsImlhdCI6MTc1NzA1MDI3OCwibmJmIjoxNzU3MDUwMjc4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.NatvXPP3hnjs-e-9HXeBnoyoiqBqtosD3aaOkecn_Vw
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                      SqlSugar.SqlSugarException
      ┣ 消息：                      Connect Timeout expired.
      ┣ 错误堆栈：                  at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at SqlSugar.QueryableProvider`1.FirstAsync()
         at SqlSugar.QueryableProvider`1.FirstAsync(Expression`1 expression)
         at SqlSugar.SqlSugarRepository`1.FirstOrDefaultAsync(Expression`1 whereExpression) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Framework\SqlSugar\Repositories\SqlSugarRepository.cs:line 204
         at RuoYi.Common.Data.BaseRepository`2.FirstOrDefaultAsync(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseRepository.cs:line 264
         at RuoYi.Common.Data.BaseService`2.FirstOrDefaultAsync(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseService.cs:line 124
         at RuoYi.System.Slave.Services.SysUserService.GetAsync(Nullable`1 id) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Slave\Services\SysUserService.cs:line 30
         at RuoYi.Admin.SampleController.Get(Nullable`1 id) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin\Controllers\SampleController.cs:line 34
         at lambda_method29(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Connect Timeout expired.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at SqlSugar.QueryableProvider`1.FirstAsync()
         at SqlSugar.QueryableProvider`1.FirstAsync(Expression`1 expression)
         at SqlSugar.SqlSugarRepository`1.FirstOrDefaultAsync(Expression`1 whereExpression) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Framework\SqlSugar\Repositories\SqlSugarRepository.cs:line 204
         at RuoYi.Common.Data.BaseRepository`2.FirstOrDefaultAsync(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseRepository.cs:line 264
         at RuoYi.Common.Data.BaseService`2.FirstOrDefaultAsync(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseService.cs:line 124
         at RuoYi.System.Slave.Services.SysUserService.GetAsync(Nullable`1 id) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Slave\Services\SysUserService.cs:line 30
         at RuoYi.Admin.SampleController.Get(Nullable`1 id) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin\Controllers\SampleController.cs:line 34
         at lambda_method29(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-09-08 08:55:51.7917167 +08:00 星期一 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #5
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Connect Timeout expired.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at SqlSugar.QueryableProvider`1.FirstAsync()
         at SqlSugar.QueryableProvider`1.FirstAsync(Expression`1 expression)
         at SqlSugar.SqlSugarRepository`1.FirstOrDefaultAsync(Expression`1 whereExpression) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Framework\SqlSugar\Repositories\SqlSugarRepository.cs:line 204
         at RuoYi.Common.Data.BaseRepository`2.FirstOrDefaultAsync(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseRepository.cs:line 264
         at RuoYi.Common.Data.BaseService`2.FirstOrDefaultAsync(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseService.cs:line 124
         at RuoYi.System.Slave.Services.SysUserService.GetAsync(Nullable`1 id) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Slave\Services\SysUserService.cs:line 30
         at RuoYi.Admin.SampleController.Get(Nullable`1 id) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin\Controllers\SampleController.cs:line 34
         at lambda_method29(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++

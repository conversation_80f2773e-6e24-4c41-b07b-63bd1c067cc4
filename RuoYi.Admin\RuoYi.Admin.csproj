﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
        <PublishReadyToRunComposite>true</PublishReadyToRunComposite>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\ISDN.WCS\ISDN.WCS.csproj" />
        <ProjectReference Include="..\RuoYi.Common\RuoYi.Common.csproj" />
        <ProjectReference Include="..\RuoYi.Data\RuoYi.Data.csproj" />
        <ProjectReference Include="..\RuoYi.Framework\RuoYi.Framework.csproj" />
        <ProjectReference Include="..\RuoYi.Generator\RuoYi.Generator.csproj" />
        <ProjectReference Include="..\RuoYi.Quartz\RuoYi.Quartz.csproj" />
        <ProjectReference Include="..\RuoYi.System\RuoYi.System.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <None Update="RuoYi.Admin.xml">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>

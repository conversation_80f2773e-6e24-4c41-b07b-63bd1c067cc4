using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ISDN.WCS.PLC.Socket
{
    /// <summary>
    /// PLC客户端Socket
    /// 用于连接PLC服务端，发送和接收数据
    /// </summary>
    public class PLCClientSocket : IDisposable
    {
        private TcpClient _tcpClient;
        private NetworkStream _networkStream;
        private bool _isConnected;
        private readonly string _clientId;
        private readonly string _serverIp;
        private readonly int _serverPort;
        private Timer _heartbeatTimer;
        private const int HEARTBEAT_INTERVAL_MS = 30000; // 心跳间隔，30秒
        private const int RECONNECT_INTERVAL_MS = 5000; // 重连间隔，5秒
        private bool _disposed = false;
        private CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 客户端连接信息类
        /// </summary>
        public class ClientInfo
        {
            public string ClientId { get; set; }
            public string ServerIp { get; set; }
            public int ServerPort { get; set; }
            public bool IsConnected { get; set; }
            public DateTime LastConnectTime { get; set; }
            public DateTime LastHeartbeatTime { get; set; }
        }

        /// <summary>
        /// 数据接收事件
        /// </summary>
        //public event EventHandler<SocketDataReceivedEventArgs> DataReceived;
        public delegate Task AsyncSocketDataReceivedEventHandler(object sender, SocketDataReceivedEventArgs e);

        public event AsyncSocketDataReceivedEventHandler DataReceived;

        /// <summary>
        /// 客户端连接事件
        /// </summary>
        public event EventHandler<SocketClientConnectedEventArgs> Connected;

        /// <summary>
        /// 客户端断开连接事件
        /// </summary>
        public event EventHandler<SocketClientDisconnectedEventArgs> Disconnected;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="clientId">客户端ID，用于区分不同的客户端</param>
        /// <param name="serverIp">服务端IP地址</param>
        /// <param name="serverPort">服务端端口号</param>
        public PLCClientSocket(string clientId, string serverIp, int serverPort)
        {
            _clientId = clientId;
            _serverIp = serverIp;
            _serverPort = serverPort;
            _isConnected = false;
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// 连接到服务端
        /// </summary>
        public async Task<bool> ConnectAsync()
        {
            try
            {
                if (_isConnected)
                {
                    return true;
                }

                _tcpClient = new TcpClient();
                await _tcpClient.ConnectAsync(_serverIp, _serverPort);
                _networkStream = _tcpClient.GetStream();
                _isConnected = true;

                LogInfo($"客户端 {_clientId} 已成功连接到服务端 {_serverIp}:{_serverPort}");

                // 触发连接事件
                OnConnected();

                // 启动心跳定时器
                StartHeartbeat();

                // 开始接收数据
                _ = Task.Run(() => ReceiveDataAsync(_cancellationTokenSource.Token));

                return true;
            }
            catch (Exception ex)
            {
                LogError($"客户端 {_clientId} 连接服务端 {_serverIp}:{_serverPort} 失败: {ex.Message}");
                _isConnected = false;
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            try
            {
                if (!_isConnected)
                {
                    return;
                }

                StopHeartbeat();
                _networkStream?.Close();
                _tcpClient?.Close();
                _isConnected = false;

                LogInfo($"客户端 {_clientId} 已断开与服务端 {_serverIp}:{_serverPort} 的连接");

                // 触发断开连接事件
                OnDisconnected();
            }
            catch (Exception ex)
            {
                LogError($"客户端 {_clientId} 断开连接失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送数据到服务端
        /// </summary>
        /// <param name="data">要发送的数据</param>
        public async Task<bool> SendDataAsync(string data)
        {
            try
            {
                if (!_isConnected || _tcpClient == null || !_tcpClient.Connected)
                {
                    throw new Exception($"设备 {_clientId} 未连接");
                    return false;
                }

                byte[] bytes = Encoding.UTF8.GetBytes(data);
                await _networkStream.WriteAsync(bytes, 0, bytes.Length);
                await _networkStream.FlushAsync();

                LogDebug($"客户端 {_clientId} 发送数据成功: {data}");
                return true;
            }
            catch (Exception ex)
            {
                await HandleConnectionError();
                throw new Exception(ex.Message, ex);
                return false;
            }
        }

        /// <summary>
        /// 发送二进制数据到服务端（小端格式）
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="binaryData">二进制数据</param>
        public async Task<bool> SendBinaryDataAsync(string dataType, byte[] binaryData)
        {
            try
            {
                if (!_isConnected || _tcpClient == null || !_tcpClient.Connected)
                {
                    LogWarning($"客户端 {_clientId} 未连接，无法发送二进制数据");
                    return false;
                }

                // 将二进制数据转换为十六进制字符串并添加数据类型
                string hexData = BitConverter.ToString(binaryData).Replace("-", "");
                string message = $"TYPE|{dataType}|{hexData}";

                byte[] bytes = Encoding.UTF8.GetBytes(message);
                await _networkStream.WriteAsync(bytes, 0, bytes.Length);
                await _networkStream.FlushAsync();

                LogDebug($"客户端 {_clientId} 发送二进制数据成功，类型: {dataType}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"客户端 {_clientId} 发送二进制数据失败: {ex.Message}");
                await HandleConnectionError();
                return false;
            }
        }

        /// <summary>
        /// 异步接收数据
        /// </summary>
        private async Task ReceiveDataAsync(CancellationToken cancellationToken)
        {
            try
            {
                byte[] buffer = new byte[4096];
                while (_isConnected && _tcpClient != null && _tcpClient.Connected && !cancellationToken.IsCancellationRequested)
                {
                    int bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                    if (bytesRead == 0)
                    {
                        // 连接断开
                        await HandleConnectionError();
                        break;
                    }

                    string data = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    LogDebug($"客户端 {_clientId} 接收到数据: {data}");

                    // 处理接收到的数据
                    await OnDataReceived(data);
                }
            }
            catch (OperationCanceledException)
            {
                // 任务被取消，正常退出
                LogDebug($"客户端 {_clientId} 接收数据任务已取消");
            }
            catch (Exception ex)
            {
                if (!cancellationToken.IsCancellationRequested)
                {
                    LogError($"客户端 {_clientId} 接收数据失败: {ex.Message}");
                    await HandleConnectionError();
                }
            }
        }

        /// <summary>
        /// 处理连接错误，尝试重连
        /// </summary>
        private async Task HandleConnectionError()
        {
            if (_isConnected)
            {
                Disconnect();

                // 尝试重连
                LogInfo($"客户端 {_clientId} 将在 {RECONNECT_INTERVAL_MS}ms 后尝试重连");
                await Task.Delay(RECONNECT_INTERVAL_MS);
                await ConnectAsync();
            }
        }

        /// <summary>
        /// 启动心跳检测
        /// </summary>
        private void StartHeartbeat()
        {
            _heartbeatTimer = new Timer(async (_) =>
            {
                if (_isConnected && _tcpClient != null && _tcpClient.Connected)
                {
                    try
                    {
                        await SendDataAsync("HEARTBEAT");
                    }
                    catch (Exception ex)
                    {
                        LogError($"客户端 {_clientId} 发送心跳包失败: {ex.Message}");
                        await HandleConnectionError();
                    }
                }
            }, null, HEARTBEAT_INTERVAL_MS, HEARTBEAT_INTERVAL_MS);
        }

        /// <summary>
        /// 停止心跳检测
        /// </summary>
        private void StopHeartbeat()
        {
            if (_heartbeatTimer != null)
            {
                _heartbeatTimer.Dispose();
                _heartbeatTimer = null;
            }
        }

        /// <summary>
        /// 获取客户端连接信息
        /// </summary>
        public ClientInfo GetClientInfo()
        {
            return new ClientInfo
            {
                ClientId = _clientId,
                ServerIp = _serverIp,
                ServerPort = _serverPort,
                IsConnected = _isConnected,
                LastConnectTime = DateTime.Now
            };
        }

        /// <summary>
        /// 检查是否已连接
        /// </summary>
        public bool IsConnected => _isConnected && _tcpClient != null && _tcpClient.Connected;

        #region 事件触发方法

        /// <summary>
        /// 触发数据接收事件
        /// </summary>
        /// <param name="data">接收到的数据</param>
        protected virtual async Task OnDataReceived(string data)
        {
            await DataReceived?.Invoke(this, new SocketDataReceivedEventArgs
            {
                ClientId = _clientId,
                Data = data
            });
        }

        /// <summary>
        /// 触发连接事件
        /// </summary>
        protected virtual void OnConnected()
        {
            Connected?.Invoke(this, new SocketClientConnectedEventArgs
            {
                ClientId = _clientId,
                IpAddress = _serverIp,
                Port = _serverPort
            });
        }

        /// <summary>
        /// 触发断开连接事件
        /// </summary>
        protected virtual void OnDisconnected()
        {
            Disconnected?.Invoke(this, new SocketClientDisconnectedEventArgs
            {
                ClientId = _clientId
            });
        }

        #endregion

        #region 日志辅助方法

        private void LogInfo(string message)
        {
            Console.WriteLine(message);
        }

        private void LogWarning(string message)
        {
            Console.WriteLine(message);
        }

        private void LogError(string message)
        {
            Console.WriteLine(message);
        }

        private void LogDebug(string message)
        {
            Console.WriteLine(message);
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    StopHeartbeat();
                    Disconnect();
                }

                _disposed = true;
            }
        }

        #endregion
    }
}
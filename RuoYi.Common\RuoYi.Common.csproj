﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <NoWarn>1701;1702;8600;8604;</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <NoWarn>1701;1702;8600;8604;</NoWarn>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="Configs\**" />
        <EmbeddedResource Remove="Configs\**" />
        <None Remove="Configs\**" />
    </ItemGroup>

    <ItemGroup>
        <Compile Remove="Constants\Constants.cs" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\RuoYi.Data\RuoYi.Data.csproj" />
        <ProjectReference Include="..\RuoYi.Framework\RuoYi.Framework.csproj" />
    </ItemGroup>

</Project>

using SqlSugar;
using System;
using System.Collections.Generic;
using RuoYi.Data.Entities;

namespace ISDN.WCS.Data.Entities
{
    /// <summary>
    ///  容器档 对象 wcs_container
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    [SugarTable("wcs_container", "容器档")]
    public class WcsContainer : IsdnBaseEntity
  {
        /// <summary>
        /// 容器ID (container_id)
        /// </summary>
        [SugarColumn(ColumnName = "container_id", ColumnDescription = "容器ID", IsPrimaryKey = true, IsIdentity = true)]
        public long ContainerId { get; set; }
        
        /// <summary>
        /// 容器名称 (container_name)
        /// </summary>
        [SugarColumn(ColumnName = "container_name", ColumnDescription = "容器名称")]
        public string ContainerName { get; set; }
        
        /// <summary>
        /// 容器编号 (container_code)
        /// </summary>
        [SugarColumn(ColumnName = "container_code", ColumnDescription = "容器编号")]
        public string ContainerCode { get; set; }
        
        /// <summary>
        /// 所在库位 (location_id)
        /// </summary>
        [SugarColumn(ColumnName = "location_id", ColumnDescription = "所在库位")]
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 当前位置 (current_adrr)
        /// </summary>
        [SugarColumn(ColumnName = "current_adrr", ColumnDescription = "当前位置")]
        public string? CurrentAdrr { get; set; }
        
        /// <summary>
        /// 生效 (is_visible_flag)
        /// </summary>
        [SugarColumn(ColumnName = "is_visible_flag", ColumnDescription = "生效")]
        public int IsVisibleFlag { get; set; }
        
        /// <summary>
        /// 创建人id (created_by)
        /// </summary>
        [SugarColumn(ColumnName = "created_by", ColumnDescription = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id (updated_by)
        /// </summary>
        [SugarColumn(ColumnName = "updated_by", ColumnDescription = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间 (created_time)
        /// </summary>
        [SugarColumn(ColumnName = "created_time", ColumnDescription = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间 (updated_time)
        /// </summary>
        [SugarColumn(ColumnName = "updated_time", ColumnDescription = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
    }
}

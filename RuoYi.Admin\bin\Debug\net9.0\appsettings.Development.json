{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "ConnectionConfigs": [
    {
      "ConfigId": "master",
      "ConnectionString": "Server=*************;port=3306;Database=isdn_wcs;Uid=root;Pwd=********;AllowLoadLocalInfile=true;",
      "DbType": "MySql",
      "IsAutoCloseConnection": true
    }
    //{
    //  "ConfigId": "slave",
    //  "ConnectionString": "Server=localhost;Database=ry_net2;Uid=root;Pwd=**************;AllowLoadLocalInfile=true;",
    //  "DbType": "MySql",
    //  "IsAutoCloseConnection": true
    //}
  ],
  "CacheConfig": {
    "CacheType": "Memory",
    // 缓存类型: Redis/Memory, 当 CacheType: Redis时, RedisConfig有效
    "RedisConfig": {
      "Host": "**************",
      "Port": "26379",
      "Database": 0,
      "Password": "nas!@#redis#@!",
      "InstanceName": "ruoyi_net:"
    }
  },
  "AppSettings": {
    "InjectSpecificationDocument": true
  }
}

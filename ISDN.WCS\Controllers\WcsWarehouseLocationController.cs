using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Enums;
using RuoYi.Common.Utils;
using RuoYi.Framework;
using RuoYi.Framework.Extensions;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
//using ISDN.Data.Dtos;
using ISDN.WCS.Services;
using ISDN.WCS.Data.Dtos;

namespace ISDN.WCS.Controllers
{
    /// <summary>
    /// 库位管理
    ///  author zgq
    ///  date   2025-08-13 16:36:43
    /// </summary>
    [ApiDescriptionSettings("Wcs")]
    [Route("wcs/location")]
    public class WcsWarehouseLocationController : ControllerBase
    {
        private readonly ILogger<WcsWarehouseLocationController> _logger;
        private readonly WcsWarehouseLocationService _wcsWarehouseLocationService;

        public WcsWarehouseLocationController(ILogger<WcsWarehouseLocationController> logger,
            WcsWarehouseLocationService wcsWarehouseLocationService)
        {
            _logger = logger;
            _wcsWarehouseLocationService = wcsWarehouseLocationService;
        }

        /// <summary>
        /// 查询库位管理列表
        /// </summary>
        [HttpGet("list")]
        [AppAuthorize("wcs:location:list")]
        public async Task<SqlSugarPagedList<WcsWarehouseLocationDto>> GetWcsWarehouseLocationPagedList([FromQuery] WcsWarehouseLocationDto dto)
        {
           return await _wcsWarehouseLocationService.GetDtoPagedListAsync(dto);
        }

        /// <summary>
        /// 获取 库位管理 详细信息
        /// </summary>
        [HttpGet("")]
        [HttpGet("{id}")]
        [AppAuthorize("wcs:location:query")]
        public async Task<AjaxResult> Get(long id)
        {
            var data = await _wcsWarehouseLocationService.GetDtoAsync(id);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 新增 库位管理
        /// </summary>
        [HttpPost("")]
        [AppAuthorize("wcs:location:add")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "库位管理", BusinessType = BusinessType.INSERT)]
        public async Task<AjaxResult> Add([FromBody] WcsWarehouseLocationDto dto)
        {
            var data = await _wcsWarehouseLocationService.InsertAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 修改 库位管理
        /// </summary>
        [HttpPut("")]
        [AppAuthorize("wcs:location:edit")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "库位管理", BusinessType = BusinessType.UPDATE)]
        public async Task<AjaxResult> Edit([FromBody] WcsWarehouseLocationDto dto)
        {
            var data = await _wcsWarehouseLocationService.UpdateAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 删除 库位管理
        /// </summary>
        [HttpDelete("{ids}")]
        [AppAuthorize("wcs:location:remove")]
        [RuoYi.System.Log(Title = "库位管理", BusinessType = BusinessType.DELETE)]
        public async Task<AjaxResult> Remove(string ids)
        {
            var idList = ids.SplitToList<long>();
            var data = await _wcsWarehouseLocationService.DeleteAsync(idList);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 导入 库位管理
        /// </summary>
        [HttpPost("import")]
        [AppAuthorize("wcs:location:import")]
        [RuoYi.System.Log(Title = "库位管理", BusinessType = BusinessType.IMPORT)]
        public async Task Import([Required] IFormFile file)
        {
            var stream = new MemoryStream();
            file.CopyTo(stream);
            var list = await ExcelUtils.ImportAsync<WcsWarehouseLocationDto>(stream);
            await _wcsWarehouseLocationService.ImportDtoBatchAsync(list);
        }

        /// <summary>
        /// 导出 库位管理
        /// </summary>
        [HttpPost("export")]
        [AppAuthorize("wcs:location:export")]
        [RuoYi.System.Log(Title = "库位管理", BusinessType = BusinessType.EXPORT)]
        public async Task Export(WcsWarehouseLocationDto dto)
        {
            var list = await _wcsWarehouseLocationService.GetDtoListAsync(dto);
            await ExcelUtils.ExportAsync(App.HttpContext.Response, list);
        }
    }
}

﻿#if !NET5_0
using System.Text.Json;
using System.Text.Json.Serialization;

namespace RuoYi.Framework.JsonSerialization;

/// <summary>
///   TimeOnly 类型序列化
/// </summary>
[SuppressSniffer]
public class SystemTextJsonTimeOnlyJsonConverter : JsonConverter<TimeOnly>
{
  /// <summary>
  ///   构造函数
  /// </summary>
  public SystemTextJsonTimeOnlyJsonConverter()
    : this(default)
  {
  }

  /// <summary>
  ///   构造函数
  /// </summary>
  /// <param name="format"></param>
  public SystemTextJsonTimeOnlyJsonConverter(string format = "HH:mm:ss")
  {
    Format = format;
  }

  /// <summary>
  ///   时间格式化格式
  /// </summary>
  public string Format { get; }

  /// <summary>
  ///   反序列化
  /// </summary>
  /// <param name="reader"></param>
  /// <param name="typeToConvert"></param>
  /// <param name="options"></param>
  /// <returns></returns>
  public override TimeOnly Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    return TimeOnly.Parse(reader.GetString());
  }

  /// <summary>
  ///   序列化
  /// </summary>
  /// <param name="writer"></param>
  /// <param name="value"></param>
  /// <param name="options"></param>
  public override void Write(Utf8JsonWriter writer, TimeOnly value, JsonSerializerOptions options)
  {
    writer.WriteStringValue(value.ToString(Format));
  }
}

/// <summary>
///   TimeOnly? 类型序列化
/// </summary>
[SuppressSniffer]
public class SystemTextJsonNullableTimeOnlyJsonConverter : JsonConverter<TimeOnly?>
{
  /// <summary>
  ///   默认构造函数
  /// </summary>
  public SystemTextJsonNullableTimeOnlyJsonConverter()
    : this(default)
  {
  }

  /// <summary>
  ///   构造函数
  /// </summary>
  /// <param name="format"></param>
  public SystemTextJsonNullableTimeOnlyJsonConverter(string format = "HH:mm:ss")
  {
    Format = format;
  }

  /// <summary>
  ///   时间格式化格式
  /// </summary>
  public string Format { get; }

  /// <summary>
  ///   反序列化
  /// </summary>
  /// <param name="reader"></param>
  /// <param name="typeToConvert"></param>
  /// <param name="options"></param>
  /// <returns></returns>
  public override TimeOnly? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    return TimeOnly.TryParse(reader.GetString(), out var time) ? time : null;
  }

  /// <summary>
  ///   序列化
  /// </summary>
  /// <param name="writer"></param>
  /// <param name="value"></param>
  /// <param name="options"></param>
  public override void Write(Utf8JsonWriter writer, TimeOnly? value, JsonSerializerOptions options)
  {
    if (value == null) writer.WriteNullValue();
    else writer.WriteStringValue(value.Value.ToString(Format));
  }
}
#endif

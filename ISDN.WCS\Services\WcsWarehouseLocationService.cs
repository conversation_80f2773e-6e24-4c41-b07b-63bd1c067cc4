using Mapster;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Data;
using RuoYi.Framework.DependencyInjection;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.Repositories;

namespace ISDN.WCS.Services
{
    /// <summary>
    ///  库位管理 Service
    ///  author zgq
    ///  date   2025-08-13 16:36:43
    /// </summary>
    public class WcsWarehouseLocationService : BaseService<WcsWarehouseLocation, WcsWarehouseLocationDto>, ITransient
    {
        private readonly ILogger<WcsWarehouseLocationService> _logger;
        private readonly WcsWarehouseLocationRepository _wcsWarehouseLocationRepository;

        public WcsWarehouseLocationService(ILogger<WcsWarehouseLocationService> logger,
            WcsWarehouseLocationRepository wcsWarehouseLocationRepository)
        {
            BaseRepo = wcsWarehouseLocationRepository;

            _logger = logger;
            _wcsWarehouseLocationRepository = wcsWarehouseLocationRepository;
        }

        /// <summary>
        /// 查询 库位管理 详情
        /// </summary>
        public async Task<WcsWarehouseLocation> GetAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.LocationId == id);
            return entity;
        }

        /// <summary>
        /// 查询 库位管理 详情
        /// </summary>
        public async Task<WcsWarehouseLocationDto> GetDtoAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.LocationId == id);
            var dto = entity.Adapt<WcsWarehouseLocationDto>();
            // TODO 填充关联表数据
            return dto;
        }
    }
}

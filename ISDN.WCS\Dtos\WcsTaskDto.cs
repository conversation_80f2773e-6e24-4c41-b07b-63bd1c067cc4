using System.Collections.Generic;
using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;

namespace ISDN.WCS.Data.Dtos
{
    /// <summary>
    ///  WCS任务档 对象 wcs_task
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    public class WcsTaskDto : BaseDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [Excel(Name = "任务ID")]
        public long? TaskId { get; set; }


        /// <summary>
        /// 任务号
        /// </summary>
        [Excel(Name = "任务号")]
        public string TaskCode { get; set; }

        /// <summary>
        /// 容器号
        /// </summary>
        [Excel(Name = "容器号")]
        public string BinNo { get; set; }

        /// <summary>
        /// 设备编号
        /// </summary>
        [Excel(Name = "设备编号")]
        public string EquipmentNo { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        [Excel(Name = "任务类型")]
        public int TaskType { get; set; }
        
        /// <summary>
        /// 起始位置
        /// </summary>
        [Excel(Name = "起始位置")]
        public string? StartLocation { get; set; }
        
        /// <summary>
        /// 目标位置
        /// </summary>
        [Excel(Name = "目标位置")]
        public string? EndLocation { get; set; }
        
        /// <summary>
        /// 重试次数
        /// </summary>
        [Excel(Name = "重试次数")]
        public int? Retries { get; set; }
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        [Excel(Name = "最大重试次数")]
        public int? MaxRetries { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        [Excel(Name = "错误消息")]
        public string? ErrMsg { get; set; }
        
        /// <summary>
        /// 任务状态
        /// </summary>
        [Excel(Name = "任务状态")]
        public int? TaskStatus { get; set; }
        
        /// <summary>
        /// 生效
        /// </summary>
        [Excel(Name = "生效")]
        public int? IsVisibleFlag { get; set; }
        
        /// <summary>
        /// 创建人id
        /// </summary>
        [Excel(Name = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id
        /// </summary>
        [Excel(Name = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Excel(Name = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间
        /// </summary>
        [Excel(Name = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
    }
}

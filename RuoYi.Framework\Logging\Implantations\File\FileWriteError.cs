﻿namespace RuoYi.Framework.Logging;

/// <summary>
///   文件写入错误信息上下文
/// </summary>
[SuppressSniffer]
public sealed class FileWriteError
{
  /// <summary>
  ///   构造函数
  /// </summary>
  /// <param name="currentFileName">当前日志文件名</param>
  /// <param name="exception">异常对象</param>
  internal FileWriteError(string currentFileName, Exception exception)
  {
    CurrentFileName = currentFileName;
    Exception = exception;
  }

  /// <summary>
  ///   当前日志文件名
  /// </summary>
  public string CurrentFileName { get; private set; }

  /// <summary>
  ///   引起文件写入异常信息
  /// </summary>
  public Exception Exception { get; private set; }

  /// <summary>
  ///   备用日志文件名
  /// </summary>
  internal string RollbackFileName { get; private set; }

  /// <summary>
  ///   配置日志文件写入错误后新的备用日志文件名
  /// </summary>
  /// <param name="rollbackFileName">备用日志文件名</param>
  public void UseRollbackFileName(string rollbackFileName)
  {
    RollbackFileName = rollbackFileName;
  }
}

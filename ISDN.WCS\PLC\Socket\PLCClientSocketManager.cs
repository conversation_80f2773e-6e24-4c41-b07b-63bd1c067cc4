using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.PLC.SocketService;
using ISDN.WCS.Repositories;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using RuoYi.Framework;
using RuoYi.System.Services;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace ISDN.WCS.PLC.Socket
{
    /// <summary>
    /// PLC客户端Socket管理器
    /// 负责管理多个PLC客户端Socket连接
    /// </summary>
    public class PLCClientSocketManager : BackgroundService
    {
        public static readonly Dictionary<string, PLCClientSocket> _clients = new Dictionary<string, PLCClientSocket>();
        private bool _disposed = false;
        //private readonly WcsDeviceRepository _deviceRepository;
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly PlcToWcs _plcToWcs;
        private const int DEFAULT_RETRY_COUNT = 2;
        private const int RECONNECT_INTERVAL_MS = 2000; // 重连间隔5秒

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceRepository">设备仓库</param>
        //public PLCClientSocketManager(WcsDeviceRepository deviceRepository)
        //{
        //    //_deviceRepository = App.;
        //    _deviceRepository = deviceRepository;
        //    _plcToWcs = new PlcToWcs();
        //    //StartAsync();
        //}

        public PLCClientSocketManager(ISqlSugarClient sqlSugarClient)
        {
            _sqlSugarClient = sqlSugarClient;
            _plcToWcs = new PlcToWcs();
        }
        /// <summary>
        /// 启动服务（实现IHostedService）
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        //public async Task StartAsync(CancellationToken cancellationToken)
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            Console.WriteLine("PLCClientSocketManager服务正在启动...");

            // 从数据库加载所有设备并连接
            await ConnectAllDevicesFromDatabaseAsync();
        }

        /// <summary>
        /// 停止服务（实现IHostedService）
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务</returns>
        public Task StopAsync(CancellationToken cancellationToken)
        {
            Console.WriteLine("PLCClientSocketManager服务正在停止...");

            // 释放所有资源
            Dispose();

            return Task.CompletedTask;
        }

        /// <summary>
        /// 从数据库加载所有设备并连接
        /// </summary>
        /// <returns>任务</returns>
        public async Task ConnectAllDevicesFromDatabaseAsync()
        {
            try
            {
                Console.WriteLine("正在从数据库加载设备信息...");

                // 查询所有设备
                var devices = await _sqlSugarClient.Queryable<WcsDevice>().ToListAsync();// _deviceRepository.GetListAsync(new WcsDeviceDto());

                Console.WriteLine($"共加载到 {devices.Count} 台设备");

                foreach (var device in devices)
                {
                    // 使用设备编号作为clientId
                    string clientId = device.DeviceCode.ToString();
                    string ipAddress = device.Addr;
                    int port = device.Port ?? 0;

                    if (string.IsNullOrEmpty(ipAddress) || port <= 0)
                    {
                        Console.WriteLine($"设备 {device.DeviceCode} 的IP地址或端口无效，跳过连接");
                        continue;
                    }

                    Console.WriteLine($"正在连接设备: 编号={device.DeviceCode}, IP={ipAddress}, 端口={port}");

                    // 连接设备
                    bool b = await AddClientAsync(clientId, ipAddress, port);

                    device.Online = b ? 2 : 3;//1:未知；2:在线；3:离线
                    _sqlSugarClient.Updateable(device).ExecuteCommand();// 保存设备在线状态到数据库
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从数据库加载设备并连接失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加客户端Socket
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <param name="port">端口</param>
        /// <param name="maxRetryCount">最大重试次数（默认3次）</param>
        /// <returns>是否添加成功</returns>
        public async Task<bool> AddClientAsync(string clientId, string ipAddress, int port, int maxRetryCount = DEFAULT_RETRY_COUNT)
        {
            try
            {
                if (_clients.ContainsKey(clientId))
                {
                    return await _clients[clientId].ConnectAsync();
                    //Console.WriteLine($"客户端 {clientId} 已存在");
                    //return false;
                }

                var clientSocket = new PLCClientSocket(clientId, ipAddress, port);
                _clients.Add(clientId, clientSocket);

                // 注册事件处理
                clientSocket.Connected += OnClientConnected;
                clientSocket.Disconnected += OnClientDisconnected;
                clientSocket.DataReceived += OnDataReceived;

                // 连接到服务器
                bool connected = false;
                int retryCount = 0;

                // 尝试重连逻辑
                while (retryCount < maxRetryCount && !connected)
                {
                    if (retryCount > 0)
                    {
                        Console.WriteLine($"客户端 {clientId} 第 {retryCount + 1} 次重连...");
                        await Task.Delay(RECONNECT_INTERVAL_MS);
                    }

                    connected = await clientSocket.ConnectAsync();
                    if (!connected)
                    {
                        retryCount++;
                    }
                }

                if (connected)
                {
                    Console.WriteLine($"客户端 {clientId} 已连接到 {ipAddress}:{port}");
                }
                else
                {
                    Console.WriteLine($"客户端 {clientId} 连接到 {ipAddress}:{port} 失败，已尝试 {maxRetryCount} 次");
                }

                return connected;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加客户端 {clientId} 失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移除客户端Socket
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>是否移除成功</returns>
        public async Task<bool> RemoveClientAsync(string clientId)
        {
            try
            {
                if (!_clients.TryGetValue(clientId, out var clientSocket))
                {
                    Console.WriteLine($"客户端 {clientId} 不存在");
                    return false;
                }

                // 取消事件订阅
                clientSocket.Connected -= OnClientConnected;
                clientSocket.Disconnected -= OnClientDisconnected;
                clientSocket.DataReceived -= OnDataReceived;

                // 断开连接并释放资源
                clientSocket.Disconnect();
                clientSocket.Dispose();

                _clients.Remove(clientId);
                Console.WriteLine($"客户端 {clientId} 已移除");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移除客户端 {clientId} 失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 向指定客户端发送数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">要发送的数据</param>
        /// <returns>是否发送成功</returns>
        public static async Task<bool> SendDataAsync(string clientId, string data)
        {
            try
            {
                if (!_clients.TryGetValue(clientId, out var clientSocket))
                {
                    throw new Exception($"设备 {clientId} 不存在，无法发送数据");
                }

                Console.WriteLine($"设备 {clientId} 发送数据: {data}");
                return await clientSocket.SendDataAsync(data);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
                return false;
            }
        }

        /// <summary>
        /// 向指定客户端发送二进制数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="binaryData">二进制数据</param>
        /// <returns>是否发送成功</returns>
        public async Task<bool> SendBinaryDataAsync(string clientId, string dataType, byte[] binaryData)
        {
            try
            {
                if (!_clients.TryGetValue(clientId, out var clientSocket))
                {
                    Console.WriteLine($"客户端 {clientId} 不存在，无法发送二进制数据");
                    return false;
                }

                return await clientSocket.SendBinaryDataAsync(dataType, binaryData);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"向客户端 {clientId} 发送二进制数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 向所有客户端广播数据
        /// </summary>
        /// <param name="data">要广播的数据</param>
        public async Task BroadcastDataAsync(string data)
        {
            try
            {
                foreach (var clientSocket in _clients.Values)
                {
                    await clientSocket.SendDataAsync(data);
                }
                Console.WriteLine("已向所有客户端广播数据");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"广播数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 向所有客户端广播二进制数据
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="binaryData">二进制数据</param>
        public async Task BroadcastBinaryDataAsync(string dataType, byte[] binaryData)
        {
            try
            {
                foreach (var clientSocket in _clients.Values)
                {
                    await clientSocket.SendBinaryDataAsync(dataType, binaryData);
                }
                Console.WriteLine("已向所有客户端广播二进制数据");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"广播二进制数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取所有客户端信息
        /// </summary>
        /// <returns>客户端信息列表</returns>
        public List<PLCClientSocket.ClientInfo> GetAllClientsInfo()
        {
            try
            {
                return _clients.Values.Select(client => client.GetClientInfo()).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取所有客户端信息失败: {ex.Message}");
                return new List<PLCClientSocket.ClientInfo>();
            }
        }

        /// <summary>
        /// 检查客户端是否已连接
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>是否已连接</returns>
        public bool IsClientConnected(string clientId)
        {
            try
            {
                if (_clients.TryGetValue(clientId, out var clientSocket))
                {
                    return clientSocket.IsConnected;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查客户端 {clientId} 连接状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取已连接的客户端数量
        /// </summary>
        /// <returns>已连接的客户端数量</returns>
        public int GetConnectedClientCount()
        {
            try
            {
                return _clients.Values.Count(client => client.IsConnected);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取已连接客户端数量失败: {ex.Message}");
                return 0;
            }
        }

        #region 事件处理方法

        /// <summary>
        /// 客户端连接事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnClientConnected(object sender, SocketClientConnectedEventArgs e)
        {
            Console.WriteLine($"客户端 {e.ClientId} 已连接到 {e.IpAddress}:{e.Port}");

            // 更新设备在线状态到数据库
            try
            {
                var device = _sqlSugarClient.Queryable<WcsDevice>().First(f => f.DeviceCode == e.ClientId && f.Online != 2);//.GetByNoAsync(e.ClientId);
                if (device != null)
                {
                    device.Online = 2; // 2:在线
                    _sqlSugarClient.Updateable(device).ExecuteCommand();
                    Console.WriteLine($"设备 {e.ClientId} 连接状态已更新到数据库，状态: 在线");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新设备 {e.ClientId} 连接状态到数据库失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 客户端断开连接事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private async void OnClientDisconnected(object sender, SocketClientDisconnectedEventArgs e)
        {
            Console.WriteLine($"客户端 {e.ClientId} 已断开连接");

            // 更新设备离线状态到数据库
            try
            {
                var device = _sqlSugarClient.Queryable<WcsDevice>().First(f => f.DeviceCode == e.ClientId && f.Online != 3);
                if (device != null)
                {
                    device.Online = 3; // 3:离线
                    _sqlSugarClient.Updateable(device).ExecuteCommand();
                    Console.WriteLine($"设备 {e.ClientId} 断开状态已更新到数据库，状态: 离线");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新设备 {e.ClientId} 断开状态到数据库失败: {ex.Message}");
            }

            // 这里可以添加断开后的处理逻辑，例如重新连接或更新状态
        }

        /// <summary>
        /// 数据接收事件处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">事件参数</param>
        private async Task OnDataReceived(object sender, SocketDataReceivedEventArgs e)
        {
            Console.WriteLine($"从客户端 {e.ClientId} 接收到数据: {e.Data}");

            string responseStr = null;
            // 调用PlcToWcs服务的ProcessReceivedData方法处理接收到的数据
            try
            {
                await _plcToWcs.ProcessReceivedData(e.ClientId, e.Data);
            }
            catch (Exception ex)
            {
                responseStr = $"处理来自客户端 {e.ClientId} 的数据时发生错误: {ex.Message}";
            }
            ExtensionsClass.AddLog(e.ClientId, null, e.Data, responseStr);
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    foreach (var clientSocket in _clients.Values)
                    {
                        clientSocket.Disconnect();
                        clientSocket.Dispose();
                    }
                    _clients.Clear();
                }

                _disposed = true;
            }
        }

        #endregion
    }
}

info: 2025-08-28 11:09:59.3574750 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 11:09:59.3600273 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 11:09:59.3614062 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 11:09:59.4459053 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 11:09:59.4473239 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 11:09:59.4485560 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 11:10:00.8553183 +08:00 星期四 L System.Logging.LoggingMonitor[0] #22
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-09e08792b729aa10287eb52ba53d2c19-3c45f988da07d5fc-00
      ┣ 服务线程 ID：               #23
      ┣ 执行耗时：                  75ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 11:10:53.7055059 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 11:10:53.7078749 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 11:10:53.7088625 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 11:10:53.7790948 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 11:10:53.7804298 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 11:10:53.8129142 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 11:10:55.0472490 +08:00 星期四 L System.Logging.LoggingMonitor[0] #22
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-1bc862915163d9fadf88d1c37b6009e8-8b416c8cd55f76d3-00
      ┣ 服务线程 ID：               #23
      ┣ 执行耗时：                  32ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 11:12:48.5911693 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 11:12:48.5931938 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 11:12:48.5940944 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 11:12:48.6710054 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 11:12:48.7037982 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 11:12:48.7343709 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 11:12:50.1494449 +08:00 星期四 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-0a37b8f99ff902986e2552722891aa28-1dd7b6db7b33d0e2-00
      ┣ 服务线程 ID：               #22
      ┣ 执行耗时：                  26ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 11:16:48.9816824 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 11:16:48.9840275 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 11:16:48.9850173 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 11:16:49.0777090 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 11:16:49.0790690 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 11:16:49.0802092 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 11:16:50.6273542 +08:00 星期四 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-536e4bfcec991dc43f1ee9c4f54ea634-523687c2aaa1da16-00
      ┣ 服务线程 ID：               #23
      ┣ 执行耗时：                  33ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 11:17:16.3741683 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 11:17:16.3769318 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 11:17:16.3780835 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 11:17:16.5442937 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 11:17:16.5455976 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 11:17:16.5706274 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 11:17:18.0019264 +08:00 星期四 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-25e20763f87069fe4377800e5c30febd-008e254d58ca5fbc-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  27ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 11:19:12.8735760 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 11:19:12.8758818 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 11:19:12.8769168 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 11:19:12.9525147 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 11:19:12.9844015 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 11:19:12.9855734 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 11:19:14.1568770 +08:00 星期四 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-5ef1a934cbf7d5267f88a18cba003f48-e5b4fd125f7ce0c1-00
      ┣ 服务线程 ID：               #5
      ┣ 执行耗时：                  30ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 13:20:36.9014899 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 13:20:36.9051260 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 13:20:36.9069422 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 13:20:37.0064687 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 13:20:37.0084120 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 13:20:37.0103148 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 13:20:38.7165446 +08:00 星期四 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-71838999ffec8bf12d77f6dc6909fd83-4686efdb019ca7a3-00
      ┣ 服务线程 ID：               #5
      ┣ 执行耗时：                  32ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 13:21:33.9831677 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 13:21:33.9857315 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 13:21:33.9869441 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 13:21:34.0720457 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 13:21:34.1047239 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 13:21:34.1061474 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 13:21:35.6089665 +08:00 星期四 L System.Logging.LoggingMonitor[0] #25
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-3e783592499ec72b55d629bae7873fc3-c972e796980cd225-00
      ┣ 服务线程 ID：               #23
      ┣ 执行耗时：                  29ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 13:25:11.6715816 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 13:25:11.6741464 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 13:25:11.6752788 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 13:25:11.8149483 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 13:25:11.8165461 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 13:25:11.8180081 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 13:25:13.3948271 +08:00 星期四 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-6beb2dd9e8ddc8bf4db45937ebb5993d-7f869d58710154b1-00
      ┣ 服务线程 ID：               #25
      ┣ 执行耗时：                  37ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 13:52:45.0353131 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 13:52:45.0383168 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 13:52:45.0397259 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 13:52:45.1517036 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 13:52:45.1804415 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 13:52:45.1821374 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 13:52:47.4225852 +08:00 星期四 L System.Logging.LoggingMonitor[0] #22
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-4e6f986aab34ce2ddbb1e7f94c9d6bab-0171b3d6f57d1320-00
      ┣ 服务线程 ID：               #23
      ┣ 执行耗时：                  41ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:27:51.9083908 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 14:27:51.9323396 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 14:27:51.9340411 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 14:27:52.0565654 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 14:27:52.0583786 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 14:27:52.0599877 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 14:27:53.8221248 +08:00 星期四 L System.Logging.LoggingMonitor[0] #9
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-e2d248aed40c409615a80b6d24adba3d-b5b8102fa3c1b9f5-00
      ┣ 服务线程 ID：               #10
      ┣ 执行耗时：                  58ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:35:26.1659157 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 14:35:26.1692393 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 14:35:26.1708166 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 14:35:26.2742038 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 14:35:26.2766413 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 14:35:26.2789306 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 14:35:27.7627591 +08:00 星期四 L System.Logging.LoggingMonitor[0] #22
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-c2e91ab611b252396c1a8a5836679499-3e871e8d28debe83-00
      ┣ 服务线程 ID：               #10
      ┣ 执行耗时：                  41ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:35:53.6966454 +08:00 星期四 L System.Logging.LoggingMonitor[0] #12
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.TestController.CreateWcsTaskTest
      ┣ 
      ┣ 控制器名称：                TestController
      ┣ 操作名称：                  CreateWcsTaskTest
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Test; [action]: CreateWcsTaskTest
      ┣ 请求方式：                  POST
      ┣ 请求地址：                  http://localhost:5000/test/CreateWcsTaskTest?BinNo=123&TaskType=1&StartLocation=232&EndLocation=123
      ┣ 来源地址：                  http://localhost:5000/api-doc/index.html?urls.primaryName=Test
      ┣ 请求端源：                  swagger
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-72ce0b9b738e8190de0f8a17f0dd413e-c251c63f1c7a7ab3-00
      ┣ 服务线程 ID：               #9
      ┣ 执行耗时：                  3483ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    text/plain
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Origin：                    http://localhost:5000
      ┣ Referer：                   http://localhost:5000/api-doc/index.html?urls.primaryName=Test
      ┣ Content-Length：            0
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ request-from：              swagger
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            cors
      ┣ Sec-Fetch-Dest：            empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：              
      ┣ 
      ┣ dto (WmsTaskDto)：          {"binNo":"123","taskType":1,"startLocation":"232","endLocation":"123"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<ISDN.WCS.Data.Dtos.ResultDto>
      ┣ 最终类型：                  ISDN.WCS.Data.Dtos.ResultDto
      ┣ 最终返回值：                {"serverTag":null,"result":true,"message":"调度任务下发成功","data":null}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:39:04.3695488 +08:00 星期四 L System.Logging.LoggingMonitor[0] #27
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-f696e209ab75166326860888cb0bfe3a-0c83ca3cdaf4c881-00
      ┣ 服务线程 ID：               #27
      ┣ 执行耗时：                  141ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"53c3a51e-49f7-4484-a4c2-6bda2e097133","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:39:17.8514547 +08:00 星期四 L System.Logging.LoggingMonitor[0] #11
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-5c63551200908014081b659ae97c4b1d-0e49779437785874-00
      ┣ 服务线程 ID：               #11
      ┣ 执行耗时：                  1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"c07fc0a1-a8f8-443c-8058-d01eb30439b3","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:39:19.4247727 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #28
      []X.X.X.X[admin][Error][密码输入错误1次]
info: 2025-08-28 14:39:19.4671315 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #28
      []X.X.X.X[admin][Error][用户不存在/密码错误]
info: 2025-08-28 14:39:21.1877836 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #3
      [*******]X.X.X.X[admin][Error][验证码错误或已失效]
info: 2025-08-28 14:39:21.2235052 +08:00 星期四 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-ccdd3baa37d83b520f0b03e3a0396635-2a50046430d40a23-00
      ┣ 服务线程 ID：               #3
      ┣ 执行耗时：                  0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"78414b9e-e34c-4414-af54-02b908dd72dd","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:39:32.1941547 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #3
      [*******]X.X.X.X[admin][Error][密码输入错误2次]
info: 2025-08-28 14:39:32.2032036 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #27
      [*******]X.X.X.X[admin][Error][用户不存在/密码错误]
info: 2025-08-28 14:39:32.2322184 +08:00 星期四 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-ae2677b75c2de90f1727837478b62c3e-573dbafffbe1d7a4-00
      ┣ 服务线程 ID：               #26
      ┣ 执行耗时：                  1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"4c26d075-9e63-4218-94cb-5ab5378da6c7","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:39:42.8543049 +08:00 星期四 L System.Logging.LoggingMonitor[0] #27
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-a6a7ca104848395379d7f5200d47d206-e63ef2efbca381b3-00
      ┣ 服务线程 ID：               #27
      ┣ 执行耗时：                  1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"aa2528ba-25e6-4516-86ab-2a3494edac96","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:40:52.9070128 +08:00 星期四 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-2dd4cf42a0aad433ecbce3d4f7f4d18a-6e55428b666d039f-00
      ┣ 服务线程 ID：               #3
      ┣ 执行耗时：                  3ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"3d2c7043-0968-445c-bf16-39a85463c1e1","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:41:01.1184632 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #13
      [*******]X.X.X.X[admin][Error][密码输入错误3次]
info: 2025-08-28 14:41:01.2129959 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #29
      []X.X.X.X[admin][Error][用户不存在/密码错误]
info: 2025-08-28 14:41:01.2426022 +08:00 星期四 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-ba304ba43a6fcac0d144460e07343e79-bab971262cdfa7d3-00
      ┣ 服务线程 ID：               #3
      ┣ 执行耗时：                  0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"7bae9a89-2fb5-4ed1-a56f-aa68b2a8aeb0","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:41:11.3656195 +08:00 星期四 L System.Logging.LoggingMonitor[0] #13
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-b030e430574178a6bac0d2853be77899-d2a6827a1a572dc5-00
      ┣ 服务线程 ID：               #13
      ┣ 执行耗时：                  1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"b04d920a-c062-473f-a17d-a3c3673a17cb","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:41:12.0715866 +08:00 星期四 L System.Logging.LoggingMonitor[0] #12
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-08cdd13feec009cdff768be74294633b-f18a25064e25f3ae-00
      ┣ 服务线程 ID：               #12
      ┣ 执行耗时：                  1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"00b5219e-9e25-4b70-bc42-68a7c4e918e6","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:04.9441156 +08:00 星期四 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-d794ffc8003efee357e7b62b57c19f2b-a6e770e763101bb6-00
      ┣ 服务线程 ID：               #26
      ┣ 执行耗时：                  2ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"e24355e8-0a3b-4ed6-9196-fe4b0931d5ea","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:11.6229254 +08:00 星期四 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-22248094e9929ebe3773506e86782254-929085765eb36c0a-00
      ┣ 服务线程 ID：               #5
      ┣ 执行耗时：                  1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"6d272ec5-33c6-4667-8548-d292123f33cc","img":"/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAAyAJYDASIAAhEBAxEB/8QAHQABAAIDAQEBAQAAAAAAAAAAAAcIBQYJBAoDAf/EACoQAAEFAAICAgICAgMBAQAAAAQBAgMFBgAHEhMIERQVFiEJMSIjMiQ0/8QAHAEBAAIDAQEBAAAAAAAAAAAAAAUGAgQHAwgB/8QAJhEAAgMAAQQCAwEBAQEAAAAAAgMBBAUGABESEwcUFSExFiJBJP/aAAwDAQACEQMRAD8A+9bTX02bAhsmUN7fjNLjisYs6JHY2VcA6Eh8lq2pSeOxtoRpY4IZa+jHsrqVCUkCrC/VKxP1z2mz+srktc3bg3IHulGknBnbKopkCo0kA6H+pwLIN6+o2uNiHOCmR0BQ8MrXMT8tNrM5jgIbTT2wtLXTlxgRmGLI0f8AKlhIIjikkYx7YUdCLO/2TKyJPX4uejnMR0PWF70LtbN11Rdp46l2nqigTVYjd5mv1PrFa9BhbmD8ksDSAhNkkeNRbSmv6UaV7iWVbSEZM2ibnJRxteUK5Fxd7TUom8U1tahkbMDIzMWMm2Tu5k5Yz6s/To+m3YODjdy6wEE2nMxp0c/2syNtSxYYhvUKFvQzu8TESq+gV/8AIrIv+7dKz7EKGRnMuuOCiwfHIbF1m5oWOnswqjszNRqrv5TgJhA74IVPtfbdZA+ymCOYNEiOKsczoZyzpElkDxwMaMH5nOtu4eru4KeG+6y3Wc2dZPC+dslLYxTExRRkSByOLrpPVZA+suKUV7TRB3MIikhc1JGOakxmcqydI11yN2ZfacKXnay4p2Xt8GMIM9smyhtAsFNI7WFd06UQBdrM+M9ou7jW6UzMSi4mFm77FFsWABC2JUTbKewXM/sywgJVp1ab4JyoJUSwO8kccccsfUV044446dOOOOOnTjjjjp044446dOOOa3qdjkMNVSXu21Wbx1HE7wludTeVmfqon+t8vhJY2xQgjHeqKSTxdMi+uN7/AK8WOVMTMFiRsIQAYmSMygRGI/skUzEREf8AszMR1gxgKAmNMFrCJI2MKAABj+kRFMCMR/7MzER1snHMJn9LnNbVwXmVv6TTUpX3+Nb5+1Buqsn6Rrl9FhWzkiTfTXscvrmd9I5q/wCnJ95vgSExEwITAoghIZghIZ/cSJRMxMTH7iYntPX6BgwRMCEwOIITAoISGY7wQlEzBRMfuJiZiY/cdOOOOZdZdOY2wpqi2b4WtVW2TPpW+FgCKY3xVFRW+JMUifSoqoqfX0qKqcyXNE7P1dzhuvNlr87kbre6DP5+xsqPGZ6BxFvpbeGB362oEjjR0iIWY6GMmaKOeUYT3kRDkyRNgkwOsq5H1nJS8HEISp4LNRyUxEQYtiV9u8/uS/Uf2ZiOsWW/oKbcljUjVUx5sRDSaK1BLDlYognGUCMyIKEmHPYQEimInlP/AJJrfLiY/XdL9J9ddet7Mi6103afa20Cy9CKd1d1FnQpZ50dah1yGBabsSwYPmaIWORxTa8k42eIMUkO2H2//G50V1dvPhN0LpNPnSzL6KXsmeC1C0+tozBZx+2+wAYZwpaG9rFCnaJG2BZhEhkezy83OV71dQAztj5O4n4+/J8Dsn4Q9rT7bv2t2dx3L8gryxtagIAQ2uNr6VIc1L12Q2qxnX1BK0Omz8mteOM39hNCcFAYwUS5H+JLujs2z6g646VO6A1FR1jnc72NcU3yBntjJMtqbKfsy7spaEKpdlBw4CRzby1qnyx6w57p84XIorHTPgD3tn414ZY4jZx7XDOHXs387S1rlexkcet1L96rRtLXpurMU1LbVYnmmiRrm6qvMmAhLHdcA4b8tblz5qochZyjmlHR1OBa2HnVJRy6i3BrWuS4rauMLiqIIFWK9P7u1oiwsedJs04vsSihBdBrbofXBzTGYDvTs6mkkc57anZX9tvqaFn0qegGWysxLsZ7kX6YXZ2l+yB6RzKCS1kg8+GSs76ziPba6Dsa6gb5tadkSumtgLDEzwewqep0XXnXOmZJN4uieCCfqJYlenrnn+1nitdxzj9j4i48DjsYGvzDiRskibX47yzaq5rv3JCv8XYt2qVNAzMxK8hWYUhMDDBlaSV9dp+QNclirVz+Pb4hEQtuvg5r7i47RBF91KEWLDZiImDvndiCjv4TBME6px9jXgCJHY93ZrLEr61SLuHpXQY9E8pVH9Slm7DA15UkxD4YoJgCpoJXOYg6EJPEvJHAP7iNEhOqLjprWhyN/wCJgDNTSCEu8lciwzjWeziY1Y3R/wBJIQqO+3eStc1rZjexkjHxyMbJHI1zHse1HMexyK1zHtcitc1zVVHNVFRUVUVFReRvY9OdXWREp0mHoALKZWLNb0If8Zu5VjRyRrJd511XbPWJHyelXGL6lkkWPxWR/wB+P+L5Znx5Ud09Nkj4RLOVc4xjV4zJQ0z2dX5Go2iKe0EEZNMY7lPmQdlR6f6TBtz42csaQxPlPhh8Z0RPv2iQEc6jw+ygYjvMFN6zM/qPGC7snxfvO6BvZ+T1zgTmNT/qfT9p3PvmXzan/MS26wrIB19auciJZEp5taxXIjleyjfxW7M7Ik7k+YUFpkN/t4gO1xYIaSs1uUsoMQ39hsWvqQGbLYZkNRHpG1jP0SLC9gDPMeNUGjdKG7+AnX26097q5u3PkNSG3c6ErX1vZMJdSA9g8I7Ixv31Bb3Uo7Wwtd4G3hMqfasjnjiSOOPl31Z8WrHcd392dXZns3ZhWeK7EsK1SV0EtJPeZXP2FkNd6HQWwgNpJJaoRNQ04awU5iR2Ok/YyjyhCEjvrvKbnLeOfirl+/y23JWwqVQzbfCb1krt4woIRQAuGUCe1zbQJgtGnbhleSkFV7xA5f0v8ZcZ+Jdzg3yatfKsmuxvGOM29pl3hHLUrwFo5Pi3Hslv+3sxcEbCZpGWbZzpjzmyZuoiyqzuUX3TXVCit0mG7IzTjJ0HgafnQ7P2TL4IjGOy9xoGSqqv/r0ul/8ALl/0iKvtb3b123/952ioP6c5y6rA7/JsY1jVe9z5dJmKuFrGtRXLL7PUrU8mvc36XlW+nfjfX9EQaiGz6hve25NISMQbd3O0667Mt44BY3DIMC/YZTqUocKeOZxZtev7Jz5IlaPIVKkTCJAM3uN6m9N9Q0my69zossSa7r65wejpsd+mcnqKvsqfBUz46h0NAiMsZwaG+jqtFVQ2gJ1OTfG0F5VaDfkDmOIJ3dzd49kYSikrNjlnF+R0dirVAvWdlX3lcCxtiUjE3L1bMcV1NQWfj6urZFNazxrS4Xw2xo2M/i4bXJmRKxoW8W7l16WgxiQZK3UlWuZamR3cRUqzLxlWe+AZasUEGbE2Bp+0etNDKkFF2Fibgh3in4tbqaQwtrnIjmseLAc8iN6o5F9b42vT7T7an3ze/wDf+uR+OR1X2gK5403X/YYXgnm6GTO6wZI//KeasU+JG/2jfp30iKv1/tfrmJTpTrsRrv49UG4h6s8I1wF/fYiCFElSZFZW5qyraiRqPT7WIivnge1z45InxyPY7qtPR5e6uuzSHhfKaThlidPP19HDW4I/kIqjR5ZXdJfwWfmVriYiJ7RMkPNLFPj62mmzPI8OysoBlK3n09M1lPb9teVrBcuI/sj+OMpie8d5jsUrc5Sn/A+D5cdwdr9u/NAPXWecqthfYT4+dQDawmizec6xo5YRBN+W7JWzT36LemwzXX40lkJIOH6kuhjWyVVdnegq5Tsml835zsr97C1qrFVdi52ts2p/2o/0QX2Tbk7IdiRecTCbMXREMXwklQlWua+vXyC+YBnx1pq+s1PVlrqO19qthWdR4Hr2/rdQvYuhEi9joIWSxVGvpaathdHY6W+Lxs1RR16SNSwOPeEKbo7G1kWF145lhbuRRrN9/p0KCtbEttmJQtl21x923SUpTD/+VWyzPlz2pMK5uAIXUOV8VwrueN7V1MfYxcphXLOcVgkrtvIITUU/G1q1K5t2IY7xpZ+ajRht81QCn2Rq9cm/jXjrP4Tf5ZZ/i31JpdHedL9nZw60t8taWEZy0o8nXVtuawqxVUGCktc1a0bQgLl0A9sTlrNtfNIeaY7836Pucs/ht0HkOrdHp/lZ8gu2+utx8m+/dQFmbPQD6GnHy2LutHIOJVdMYFTzvcTpnvAEziBNd+4mFpQcxS17BQDiLrqZzZ+PKy6+JYdXaj8fp6l3WyKde0q2rOyLxAVKqLEMcmO8LZYJaWGpZuNYlJAczTfj/j2hxrN1c/Qpux5scg09Ohxy1JDb45lXiWVHLsoOZKq6IWy0yrEyCGWTVEyYMmXHHHL51fOnHHHHTqHvkH1zadvdHdr9XUh1fWW+/wAJo8pW2FqpCVoRlzXThQEnKJASUg0T5UfL6B5pfBF8I3L9JzQ/ht0doPjd8buuOl9Vb017fYz+X/n2ufU1agr+RbzUasX8RbEQExfQHejjEe4WL/6oZvX5xeEj7O8c2ItOipNLvH1ysDakfGPL3AolDPl/e3gZR4/yZnv/AGOoYsHOLkC+Tytn5ZWM/BBvtP1RnWLtfQauUd/XLJs1lFDZjzgYkInxKenHHHNfqZ6ccccdOnKnZXpnIdMd/wBt2BTTWyp3q3Z/yIq5KHJED3JVtQ6Sqqaf8cAVQA7quF1ErYjSCHSEUoAsc8hBEMb7Y8xF7RVWmqi6S7EQyuMSFZYkmIFnhnFIhNBOBOCmHPrLSsPHGsam2riRbOpsxRLKtLFOFHIjr3JMdmvRT9WK8aebfz9bMO1LARNvNvVr0VXtUtzK9fQCuVCxaCvaZUVZK0qrZagEnZOPciuYkalEbdtOPyLPZk79WpIzNukc+xBkoyWFg866NfSr1zcgHWKq1MeoDJg5fka9wmIN1ltRI4oSrK+z1rl6CtmRz0uNHpgp6ShqEjY173tPszR4J3IxzBxVILn8Bh5pGeFaLuCpRBKTb4+9rGIsQs20yNrJoxo/JysksLfPaeqrL10LFbE1jM7RETNjSQuwnIkknXLUmKPS2E022v2a3SVzZW0yB1ktBlc28kWQIwvPZqS1u5IbY0ScoOe9uLm8uoK82xqas+tqLS0ANhNO7uchzr2CjjGxju1Kj861p6r8KaGXXupJFi2E5+vov0rCq7Gso1a1clPtimto2cpTG2E+tKtmZNyrqt2s/QXRsKuIpUVakWrzazBaquX28+mqmo3CAWnudDFIlrqabzABLP4b1T1/chVw+kyGb0JteIGPHc2lJXz3PuEGQZDYrVYP2Apj2+xVIGKimT2ytSTxe5FxidUC172SZXb9j5JY1j8RhdYRp6tWxI1qQtqOwYNjXCDPa1GSxVg9e5UdJJFJCS9Z+StxyUdwribmQ+cHORdiFwOpSRGfsjKhgAMNqhNbVWyBjt7QuCyYmfIp7z30V8k3lj6vytxtaZOZpWW/bzi8y8ig861DqBhJfvwKvIRMRMDHaO0ToB3VSo5RL/A7uBjf+kW/qLjCWkio5v2heioiNbWP8meX08bECpG/6VYpWr9M4j9j/Dj/ACAWnyE2fyHJ7zMw2s0TCqOr0OIxEu4CyWFjIR9ZjcwHQG3ezracYdsSmtAyYk14ehdrauOtDi5pfoQ45AbPx2GkmuqryrldFdJ426dV+s7XqRcGCGG2rOjLeRWAkSkfUrkNX0xM/UOtJsk4HkNHD5eFJfJcVNgaVmbSn4l7R4pbB3r9a3Kjj1qjmDYrhJxXc/KsSEtbJiyT/XBTq74ad2bfvTqrY95fN/OdpD4HVD69cd+FbYrsuSxrgTxKMsUe0DpdEMRXWiwPHfYhyQxNGJgij9ivYvZX0dp5JHuGKB7TpYmN8BLD8DK7+NrVja71Wg8Y+L0ZD2eSxDl12FYxyK+e4Ic5Gpvd9ms5qQnVunoKTR1zvLyAvqoG3Cd5J9O8hbCAiBfJP6d9xr9p/S/1zQl6gpK//njNFtev5Wo1sUOZ0ZBVHCjGo2NsGO1cWlxYrEa1Gu/Dzw0kjEa18q+ESxwFbhG3x77Ds5CrVpzyuWdrju/qYW1YcxYrZBcf5GXJeObbgiClE8i2frATXNUFV8+w5vjGfxHBr2adJ+wKbzlsdX5gx3MVySEqroj/AEcWKPIcuqCUgPox6UTHhAz7VkQxJoBTzgAjZAy655ggxT6+wbCw8F5ELJXBmsGnKHYWMr1gJaOSRC2Zj0inmjRsjnFfASMAEMaa+yMHEGgLsZIIBZDyYoWRzmyDCsjGHeVK187oB42QROkWOFjY2tajnYK8smuiWwyGylUth0Jh0M8B84bFYjrwzy7+cIM0wXf1EQeMzFt8Ia2AkJD2H4Sv2SuQ8p8ZCXQLZDt28faIs8e3nEF3jr18ccc9uvPpxxxx06ccccdOnHHHHTpxxxx06ccccdOnHHHHTpxxxx06ccccdOnHHHHTr//Z"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:18.5985504 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #13
      [*******]X.X.X.X[admin][Success][登录成功]
info: 2025-08-28 14:43:18.8291424 +08:00 星期四 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-2e76418bc177c1924a4babdfb7fc629a-f10d957fc0fd72f3-00
      ┣ 服务线程 ID：                  #13
      ┣ 执行耗时：                     3292ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"a-123456","code":"6","uuid":"6d272ec5-33c6-4667-8548-d292123f33cc"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                   {"msg":"操作成功.","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM","code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:19.0264796 +08:00 星期四 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetInfo
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetInfo
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetInfo
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getInfo
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-b0436d0e87fa0df416809d3655cb31ed-ffc47efbe128de0a-00
      ┣ 服务线程 ID：                    #5
      ┣ 执行耗时：                       53ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 
      ┣ login_user_key (string)：        dc13f20e-f86f-4760-a631-66a5b43701ec
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756968198 (2025-09-04 14:43:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"permissions":["*:*:*"],"roles":["admin"],"msg":"操作成功.","user":{"userId":1,"deptId":103,"userName":"admin","nickName":"isdn","email":"<EMAIL>","phonenumber":"15888888888","sex":"0","sexDesc":null,"avatar":"","status":"0","statusDesc":null,"delFlag":"0","loginIp":"*************","loginDate":"2025-08-28T14:28:20","dept":{"deptId":103,"parentId":101,"ancestors":"0,100,101","deptName":"研发部门","orderNum":1,"leader":"若依","phone":"15888888888","email":"<EMAIL>","status":"0","delFlag":"0","parentName":null,"children":[],"deptCheckStrictly":null,"roleId":null,"parentIds":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"","updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"deptName":null,"deptLeader":null,"roles":[{"roleId":1,"roleName":"超级管理员","roleKey":"admin","roleSort":1,"dataScope":"1","dataScopeDesc":null,"menuCheckStrictly":true,"deptCheckStrictly":true,"status":"0","statusDesc":null,"delFlag":"0","flag":false,"menuIds":null,"deptIds":null,"permissions":null,"userId":null,"userName":null,"createBy":"admin","createTime":"2024-11-06T07:07:10","updateBy":"","updateTime":null,"remark":"超级管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"roleIds":null,"postIds":null,"roleId":null,"isAllocated":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"admin","updateTime":"2025-08-19T17:11:13","remark":"管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:19.1686407 +08:00 星期四 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetRouters
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetRouters
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetRouters
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getRouters
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-228059f780a2ec1b238e1c4f59d34d0b-d046b054582d6bc3-00
      ┣ 服务线程 ID：                    #5
      ┣ 执行耗时：                       76ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 
      ┣ login_user_key (string)：        dc13f20e-f86f-4760-a631-66a5b43701ec
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756968198 (2025-09-04 14:43:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"name":"System","path":"/system","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统管理","icon":"system","noCache":false,"link":null},"children":[{"name":"User","path":"user","hidden":false,"redirect":null,"component":"system/user/index","query":"","alwaysShow":false,"meta":{"title":"用户管理","icon":"user","noCache":false,"link":null},"children":null},{"name":"Role","path":"role","hidden":false,"redirect":null,"component":"system/role/index","query":"","alwaysShow":false,"meta":{"title":"角色管理","icon":"peoples","noCache":false,"link":null},"children":null},{"name":"Menu","path":"menu","hidden":false,"redirect":null,"component":"system/menu/index","query":"","alwaysShow":false,"meta":{"title":"菜单管理","icon":"tree-table","noCache":false,"link":null},"children":null},{"name":"Dept","path":"dept","hidden":false,"redirect":null,"component":"system/dept/index","query":"","alwaysShow":false,"meta":{"title":"部门管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Post","path":"post","hidden":false,"redirect":null,"component":"system/post/index","query":"","alwaysShow":false,"meta":{"title":"岗位管理","icon":"post","noCache":false,"link":null},"children":null},{"name":"Dict","path":"dict","hidden":false,"redirect":null,"component":"system/dict/index","query":"","alwaysShow":false,"meta":{"title":"字典管理","icon":"dict","noCache":false,"link":null},"children":null},{"name":"Config","path":"config","hidden":false,"redirect":null,"component":"system/config/index","query":"","alwaysShow":false,"meta":{"title":"参数设置","icon":"edit","noCache":false,"link":null},"children":null},{"name":"Notice","path":"notice","hidden":false,"redirect":null,"component":"system/notice/index","query":"","alwaysShow":false,"meta":{"title":"通知公告","icon":"message","noCache":false,"link":null},"children":null},{"name":"Log","path":"log","hidden":false,"redirect":"noRedirect","component":"ParentView","query":"","alwaysShow":true,"meta":{"title":"日志管理","icon":"log","noCache":false,"link":null},"children":[{"name":"Operlog","path":"operlog","hidden":false,"redirect":null,"component":"monitor/operlog/index","query":"","alwaysShow":false,"meta":{"title":"操作日志","icon":"form","noCache":false,"link":null},"children":null},{"name":"Logininfor","path":"logininfor","hidden":false,"redirect":null,"component":"monitor/logininfor/index","query":"","alwaysShow":false,"meta":{"title":"登录日志","icon":"logininfor","noCache":false,"link":null},"children":null}]}]},{"name":"Monitor","path":"/monitor","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统监控","icon":"monitor","noCache":false,"link":null},"children":[{"name":"Online","path":"online","hidden":false,"redirect":null,"component":"monitor/online/index","query":"","alwaysShow":false,"meta":{"title":"在线用户","icon":"online","noCache":false,"link":null},"children":null},{"name":"Job","path":"job","hidden":false,"redirect":null,"component":"monitor/job/index","query":"","alwaysShow":false,"meta":{"title":"定时任务","icon":"job","noCache":false,"link":null},"children":null},{"name":"Server","path":"server","hidden":false,"redirect":null,"component":"monitor/server/index","query":"","alwaysShow":false,"meta":{"title":"服务监控","icon":"server","noCache":false,"link":null},"children":null},{"name":"Cache","path":"cache","hidden":false,"redirect":null,"component":"monitor/cache/index","query":"","alwaysShow":false,"meta":{"title":"缓存监控","icon":"redis","noCache":false,"link":null},"children":null},{"name":"CacheList","path":"cacheList","hidden":false,"redirect":null,"component":"monitor/cache/list","query":"","alwaysShow":false,"meta":{"title":"缓存列表","icon":"redis-list","noCache":false,"link":null},"children":null}]},{"name":"Tool","path":"/tool","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统工具","icon":"tool","noCache":false,"link":null},"children":[{"name":"Gen","path":"gen","hidden":false,"redirect":null,"component":"tool/gen/index","query":"","alwaysShow":false,"meta":{"title":"代码生成","icon":"code","noCache":false,"link":null},"children":null}]},{"name":"Wcs","path":"/wcs","hidden":false,"redirect":"noRedirect","component":"Layout","query":null,"alwaysShow":true,"meta":{"title":"wcs管理","icon":"international","noCache":false,"link":null},"children":[{"name":"Device","path":"device","hidden":false,"redirect":null,"component":"wcs/device/index","query":null,"alwaysShow":false,"meta":{"title":"设备管理","icon":"shopping","noCache":false,"link":null},"children":null},{"name":"Lane","path":"lane","hidden":false,"redirect":null,"component":"wcs/lane/index","query":null,"alwaysShow":false,"meta":{"title":"巷道管理","icon":"table","noCache":false,"link":null},"children":null},{"name":"Location","path":"location","hidden":false,"redirect":null,"component":"wcs/location/index","query":null,"alwaysShow":false,"meta":{"title":"库位管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Container","path":"container","hidden":false,"redirect":null,"component":"wcs/container/index","query":null,"alwaysShow":false,"meta":{"title":"容器档","icon":"build","noCache":false,"link":null},"children":null},{"name":"Task","path":"task","hidden":false,"redirect":null,"component":"wcs/task/index","query":null,"alwaysShow":false,"meta":{"title":"WCS任务档","icon":"email","noCache":false,"link":null},"children":null}]}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:26.5338955 +08:00 星期四 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/is_visible_flag
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-faae3724c5d5b6c7c3921fe6faee651f-a63c5bdcfaf37bfb-00
      ┣ 服务线程 ID：                    #3
      ┣ 执行耗时：                       6ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 
      ┣ login_user_key (string)：        dc13f20e-f86f-4760-a631-66a5b43701ec
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756968198 (2025-09-04 14:43:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              is_visible_flag
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":109,"dictSort":0,"dictLabel":"是","dictValue":"1","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:36","updateBy":"admin","updateTime":"2025-08-13T16:43:30"},{"dictCode":110,"dictSort":2,"dictLabel":"否","dictValue":"2","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:44","updateBy":"admin","updateTime":"2025-08-13T16:43:35"}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:26.5383916 +08:00 星期四 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/task_type
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-ac017ba26351a651f7e20807dfd7e761-bbf5e62284a98b1b-00
      ┣ 服务线程 ID：                    #26
      ┣ 执行耗时：                       7ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 
      ┣ login_user_key (string)：        dc13f20e-f86f-4760-a631-66a5b43701ec
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756968198 (2025-09-04 14:43:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              task_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":122,"dictSort":1,"dictLabel":"入库","dictValue":"1","dictType":"task_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:54:54","updateBy":null,"updateTime":null},{"dictCode":123,"dictSort":2,"dictLabel":"出库","dictValue":"2","dictType":"task_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:55:03","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:26.5696278 +08:00 星期四 L System.Logging.LoggingMonitor[0] #13
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/task_status
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-7e1cff30e3b907b3089827fc243ebc3b-1369b63e17323528-00
      ┣ 服务线程 ID：                    #13
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 
      ┣ login_user_key (string)：        dc13f20e-f86f-4760-a631-66a5b43701ec
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756968198 (2025-09-04 14:43:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              task_status
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":118,"dictSort":1,"dictLabel":"未执行","dictValue":"1","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:03","updateBy":null,"updateTime":null},{"dictCode":119,"dictSort":2,"dictLabel":"执行中","dictValue":"2","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:18","updateBy":null,"updateTime":null},{"dictCode":120,"dictSort":3,"dictLabel":"执行失败","dictValue":"3","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:29","updateBy":null,"updateTime":null},{"dictCode":121,"dictSort":4,"dictLabel":"执行成功","dictValue":"4","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:38","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:43:26.6342305 +08:00 星期四 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsTaskController.GetWcsTaskPagedList
      ┣ 
      ┣ 控制器名称：                     WcsTaskController
      ┣ 操作名称：                       GetWcsTaskPagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsTask; [action]: GetWcsTaskPagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/task/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-fc8cbb7da29dea13bbe0ab1e2755acdf-6d1eb9bac7afe86e-00
      ┣ 服务线程 ID：                    #5
      ┣ 执行耗时：                       145ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 
      ┣ login_user_key (string)：        dc13f20e-f86f-4760-a631-66a5b43701ec
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756968198 (2025-09-04 14:43:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsTaskDto)：               {"taskId":null,"taskCode":null,"binNo":null,"equipmentNo":null,"taskType":0,"startLocation":null,"endLocation":null,"retries":null,"maxRetries":null,"errMsg":null,"taskStatus":null,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsTaskDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsTaskDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":4,"rows":[{"taskId":1,"taskCode":null,"binNo":null,"equipmentNo":null,"taskType":1,"startLocation":"5675","endLocation":"56756","retries":55,"maxRetries":66,"errMsg":null,"taskStatus":null,"isVisibleFlag":2,"createdBy":1,"updatedBy":1,"createdTime":"2025-08-13T17:29:33","updatedTime":"2025-08-13T17:46:30","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"taskId":2,"taskCode":null,"binNo":"0","equipmentNo":null,"taskType":0,"startLocation":null,"endLocation":null,"retries":null,"maxRetries":null,"errMsg":null,"taskStatus":0,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":"2025-08-27T17:34:53","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"taskId":3,"taskCode":"a112320250828135601","binNo":"123","equipmentNo":"a1","taskType":1,"startLocation":null,"endLocation":null,"retries":null,"maxRetries":null,"errMsg":null,"taskStatus":1,"isVisibleFlag":1,"createdBy":null,"updatedBy":1,"createdTime":"2025-08-28T13:56:01","updatedTime":"2025-08-28T14:29:34","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"taskId":4,"taskCode":"a112320250828143553","binNo":"123","equipmentNo":"a1","taskType":1,"startLocation":"232","endLocation":"123","retries":null,"maxRetries":null,"errMsg":null,"taskStatus":1,"isVisibleFlag":1,"createdBy":null,"updatedBy":null,"createdTime":"2025-08-28T14:35:54","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:47:56.7366866 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-28 14:47:56.7390671 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-28 14:47:56.7401726 +08:00 星期四 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-28 14:47:56.8563112 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-28 14:47:56.8579142 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-28 14:47:56.8862750 +08:00 星期四 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-28 14:47:58.2969809 +08:00 星期四 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-8f949ae8c4cd478224f6cf36610ead1b-0719cdc3ffa7ceed-00
      ┣ 服务线程 ID：               #23
      ┣ 执行耗时：                  28ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:48:46.3112745 +08:00 星期四 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.TestController.CreateWcsTaskTest
      ┣ 
      ┣ 控制器名称：                TestController
      ┣ 操作名称：                  CreateWcsTaskTest
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Test; [action]: CreateWcsTaskTest
      ┣ 请求方式：                  POST
      ┣ 请求地址：                  http://localhost:5000/test/CreateWcsTaskTest?BinNo=1&TaskType=123&StartLocation=1&EndLocation=123
      ┣ 来源地址：                  http://localhost:5000/api-doc/index.html?urls.primaryName=Test
      ┣ 请求端源：                  swagger
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-eec6f76e7f2c5abfa8b4ac2da22e59de-1d0863f8fc03776e-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  2023ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    text/plain
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Origin：                    http://localhost:5000
      ┣ Referer：                   http://localhost:5000/api-doc/index.html?urls.primaryName=Test
      ┣ Content-Length：            0
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ request-from：              swagger
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            cors
      ┣ Sec-Fetch-Dest：            empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：              
      ┣ 
      ┣ dto (WmsTaskDto)：          {"binNo":"1","taskType":123,"startLocation":"1","endLocation":"123"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<ISDN.WCS.Data.Dtos.ResultDto>
      ┣ 最终类型：                  ISDN.WCS.Data.Dtos.ResultDto
      ┣ 最终返回值：                {"serverTag":null,"result":true,"message":"调度任务下发成功","data":null}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 14:49:00.2403832 +08:00 星期四 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.TestController.CreateWcsTaskTest
      ┣ 
      ┣ 控制器名称：                TestController
      ┣ 操作名称：                  CreateWcsTaskTest
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Test; [action]: CreateWcsTaskTest
      ┣ 请求方式：                  POST
      ┣ 请求地址：                  http://localhost:5000/test/CreateWcsTaskTest?BinNo=1&TaskType=123&StartLocation=1&EndLocation=123
      ┣ 来源地址：                  http://localhost:5000/api-doc/index.html?urls.primaryName=Test
      ┣ 请求端源：                  swagger
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-20f88b4c6964a67cc4be4591f824526c-432910f6f6a9c90c-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  1141ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    text/plain
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Origin：                    http://localhost:5000
      ┣ Referer：                   http://localhost:5000/api-doc/index.html?urls.primaryName=Test
      ┣ Content-Length：            0
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ request-from：              swagger
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            cors
      ┣ Sec-Fetch-Dest：            empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：              
      ┣ 
      ┣ dto (WmsTaskDto)：          {"binNo":"1","taskType":123,"startLocation":"1","endLocation":"123"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<ISDN.WCS.Data.Dtos.ResultDto>
      ┣ 最终类型：                  ISDN.WCS.Data.Dtos.ResultDto
      ┣ 最终返回值：                {"serverTag":null,"result":false,"message":"该容器存在未结束的任务！","data":null}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:12.6847403 +08:00 星期四 L System.Logging.LoggingMonitor[0] #38
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Logout
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       Logout
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: Logout
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5000/logout
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-0403cd27a3d4ae1cb993c3b8b82967d1-751625002166bf86-00
      ┣ 服务线程 ID：                    #38
      ┣ 执行耗时：                       1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ 
      ┣ login_user_key (string)：        dc13f20e-f86f-4760-a631-66a5b43701ec
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756968198 (2025-09-04 14:43:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756363398 (2025-08-28 14:43:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6ImRjMTNmMjBlLWY4NmYtNDc2MC1hNjMxLTY2YTViNDM3MDFlYyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5NjgxOTgsImlhdCI6MTc1NjM2MzM5OCwibmJmIjoxNzU2MzYzMzk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.yB0WAaoLL7RSaRDO8k_Q1zVVXNRlxgVZ-AQyX0QZdLM
      ┣ Origin：                         http://localhost:5000
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ Content-Length：                 0
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"退出成功","code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:13.5136413 +08:00 星期四 L System.Logging.LoggingMonitor[0] #38
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-18b542a76ce26d9370e80baf83c51068-c36e5f1e9d3a202d-00
      ┣ 服务线程 ID：               #38
      ┣ 执行耗时：                  107ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; password=D+1zsVcVEZSCfDI01Ww4bU9wgY6aDxJFBh2CFuU0pVSxLJy/OjxJx5rlaSQ+5Hignr7NYhq5wma/i1uv/TqRSQ==; rememberMe=true
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"3c25faf5-c6f6-4472-8db7-1d35c83926be","img":"/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAAyAJYDASIAAhEBAxEB/8QAHQABAAICAwEBAAAAAAAAAAAAAAYIBwkEBQoCAf/EACsQAAAGAgIBBAICAgMAAAAAAAECAwQFBgAHCBESCRMUISIxFUEWURcYYf/EABwBAAEEAwEAAAAAAAAAAAAAAAADBAUHAQIGCP/EAC0RAAIDAAICAQQBAgYDAAAAAAEDAgQFAAYREiEHExQxQSMyCBYiJSZhQmJx/9oADAMBAAIRAxEAPwD38YyP2G11mooMHdpn4euNJOTbwrB5NyDWMZuZZ2i4XaRybp4qi3B27I0XBqiZQp3KpAQRA66iaZ+/AQEAEBAQEAEBAewEB+wEBD6EBD7AQ/eaicDKUBKJnD194iQMoewJj7RB8x9gCY+QPIB8cWnXsLSmwxDoIsFgrvmqcUvKTGLglsoiDSqUoxYISkYGURLwSOfuMg9q2braiO49hd9hUemvpbx/i2VqtkDXncl5HOmX+PbS79ms88lElCF+ORTs6Zyh+RDAE2IcihCKJnKomoUpyHIYDEOQwAYpyGKIlMUxRASmARAQEBARAcxFq5zmuDISmvwGQjOMprMh5iJxBJh7D5HsB5HyONYOSybFwaubEmIauE4ymoyBMQyAJlAyAJj7AeQCR559YxjN+KcYxjDhxjGMOHGMYw4cYxjDhxjGMOHGMjkXcajNz1kq0Laa5L2emjFBb65FzkZIT1VGebLvIMLJENHSshBjMs2zl1FBKN2oyLZuuuz95JJQxZHirkOrzC7CWoYVpcIOXNUymyldiu0RmIyK312qelgHq1LFtgZQnGRwCJDyCCPJHkEEeQSCPI/kEEEfwQQfkcYxjEuZ5ibcqO3XFSSbaZgtTWWwOZVJCbhNyytnhqlIVVWPkyyDdJ/Va5a3qcqpIjElSK7gX8aePGTBdIV/jANE14TnnRkllde8cdK04xE1BQhdQ8xZ61UN2cAEUk1dU7h4na/gKw2FToyjTX9tqrhXxIC04omKiI7RcYofw5rELGbTssh7fbszldTbV7/v7Vilcqsh+v4PyPiXkfHHNXR2KAdDP2b1OtZ9fys8Lz7uXblAELnbzNSjfoWpKJ9lGxXZ9pgixfrOIkPKVu3j9YadqXb+0uc/FrbMtta4NZiaNyl/7A6SPEVjYkweSb02nVrUsVukshMVdm7OyIWJYV6VclrbZU8dFw6EcVjFX/8AT25G2jQnEug03fOpOTIkhCS76At7bUM/ZKq3o8o7XlqxFsZaMdPZZ4RoxVU+K2GIS+C1VbRTZMUGSYBf7kVwk0Ryqm46Z3ayuVoSg6y5r1br7S92Wv1iuvnb1w8XuMbBwT5g1VuCpFyMFpCV/kmDmNZR7R3FrlZpGDWf6Mbq5U69cyuP6c6/tepNQbFRj6jLOle2bOaJZrxXXriLT9soJp3GMrbKZfNUBI1auY4jpNsgvKulHNTLz4dd77lSrZNKnS2c/Qyaj0WLNqxZsVxDUt3dpciixYc6KTCvZZetzVL3ZaMwxa0U/n1Mbq31L67G11JVbM2ev6fXKOj1zbnU29bTpRTs2tztwv5WrXuWbK6skwdWUJe0nWbbQ56zVui19Vjha4lFYNe17lj5luUDuWDziZyyErco+X5Hk2ek3cIcA8DiYyMmqBAKJjiUvQjPGvqMcOHIAJ9uuI0B6/Kf1ptytlL2PX5jYKHGAQe/ro4lEP7y5r+LjJVIEJSOYySACIgi/aN3iQCIAAiCbhNQgCIAAD+P2AB3kVW1jrVwYTuNe0dc4gICZapwCphAwCUwCY8eYejFESj9/YCID9COWsZxHjxXLD/5H8v7A8/+sfwrBA/fwZk/r5/nl4f8eZ5P+9Uv4jAmjqEfvwZMAxxIn48gJiB4PgnyAK+t+e/DVz4ePI7V6AqdeBX0+WNP2Y3gAGJIJNjkMJg8QKcpTd/XX3knT5jcVFm4ukuQmpFUAL5ioldoRT8fv78SOxOP6H6Avf0I9fQ9Zfb63120EDNaFS2wh+hb1aDREP3+hTYlEP2P6/2OShnHR8eT22DFmxJ114M2yDYnX+vFEhA6/wDOsQmXy8fbilPz8mc52vj+ABGFTwf+/J/+cXhPqyvliOwX/gfELedk/Pn5PtKltfHj9D1Hg/snlOJ71F+DtZMck3ya1czOQCmMUJlZ2boxQOUSgyaOBP5EMBy+Hl2QfMOy/eapOFPqVaDrvIX1BJDanJBeUplo3s2mdFt52cscxFf4EzRtKj9SlMJUx28HBpAqyWcMmaMekVNMi6rYE0BOS9O+fSp1nv8A23ctvznJrmjRZW6u4948qmsNzwFYokOeOho2EIlX4J3ruZcx6DhKMTeuyKybsVpFy8cgYhVipJ6huFPpk663FyA5/USV5Gcvqky0XviPpENN0LbsHA2S6s107Mc0zsiSc0GTSsdiIMcQE5FoziEig4d9tDe8Ak9HdG630B3QfqI273135DutdVZqSh0m3KfX3T7bgTnGqxmo06BNsjMkyv8Aje6Wysn+l7K55/7p2baV3r6fwofTi+KyOxdmhTVY+pGZOPYa0eq7cFhwr9RpHM9FeNLw8W/9aRUjGLpRsR3Rm9VnhaZI7hns1GSbpqJpHcM1YMESqLAqZIhjvJ1oBTKERWOQo9GMVJQSgPgbrim9WHhcl0LrY7ZimJTHBV9LUpomJS+XYgZa4F8uzFEgeID+X0PXQiGceJXEKq8QKzbKvVdq7z2s3t061n3cnvW8xt6motZpHkjiMIR7HVmskZRahCfIWaqN3JzuznVBYoG8AtplFa1PGqaVqvlaulsZypxFXRkmtlytwK1ylOVF9G02v6tM1iMnsMow9wR7AC6szZhZz6ztHp+dl3mQkbFGWzp6ZrTDJxjH86tapIseyxFntCqsAzMCD6knWmt6v3p1RzpsxsPKHWdXeOvjey2nbNCpLmK8TIs2U9pjIvziksgqmuRYoCkZA5VwP7QgfM5Ufnvwz2QkRam8kNVyqJwKYFVbGjEJAUwdgY6k2SOImUQ++1BKAf2IZIJBknv2/wB8p0y7XDUOpZuHqdkrLF2/jz7K2K8rEBepCNtLpmdsZ/rWs1q21NBWuN3asfdLK/skLcmisJWQiZ7iWHg9w2tMOeDmOLGgBY/EcM2qsZqekwMtEJuUToKLV6fgYaNna0/Imob40nX5KNkmaggs0doKlKcJCzj5WeqonR1tKpo2K9O85FfGq66qlLQqquUixs9fADLjqjk2p1lQklKrCVsuxtws1a8fW7BS0GW30uqTsUEPtUlO/wA5nHNm5QsNp3PtJZ03szIU1W0urQsOfFzWV3ThUlVnWtPz7WLxS7u2UeUy31e3M0hAqrusWCJn2yZh/QKLxTt2kQR/oDHAcwtyi3yOh9dEfQEWS1bWvMszoWmKCmcvzLlsWfN8WHbGS9xIxYWJOp/MWN6dZq2axbVRJR62cvGYqV+oPE3jGad2Bx/umhdNyb+mxFNtsDbIXXVZp1ktes7ktZYisq2mYqERXXDi51yfo9rr844jHHx5GLa1W0OyMZKzuYxjpp2XpXbLbe1ysVC4Ycw4GmUeaWi+Pv8AxPuPb2pXFVZNEZKGsFifzkPHWi0S6ew0ViPHcPA26sRKEWZOJdhOJFTcp2H9Nfpq/c7XpL0Bka2Z1jLq9ihQbvYnWz3GN5Vaz17Lqs7Pr4NarX1xaTb1vu6le3QxU6MIThs/hU3bnR67p5tazmXtPJuW7NijYrbWebVPFs0rD6ekuzrYJ0rlyVS1XYhTqnWyqy0wYuJrEOldD0dQt5Nt8+0dgTY2W8tb/rplb7B7yq5ZmyMZ/eLOZkUlVkkFDN3T9BdVsBkEPBuZMhUESlBIm9jPHzwusPKeJ2juBhqOL5Sx9gn9gVNhtN/qJ/qOZkas8UsVybHDayHITQ27GtifIOHthUavFrDrKSO8jrArN2wwPDPYfe3BTHLRpKNq9D8w9PK2Nc3ttqLyk4ez9WvEyoXoBQhLHrvfuoq1MHKIgC0vUqZc4gezJpJAf8ku7/xZ4Ls36waGnq3uv5a+xY/UJ5tRN6q0iWb0nq+ZbQaeP+cvMjC2qUaVeya4s1PtWqIfQkqxJh12rq3c2xLMxtTXRmMebtnPWl8Upk57haapz0XoVRAS97TakFRmCucou/p82V4zpK0FjLXK+FxPCKW4ISKC0qVpN+jXFLGDBAJs9fRlFV5NKENJ/KNFJyK6z9NgKBXaqjgFDizy/IesiPIPgkeYnzE+D48g/HkH9g+P1yRB8gHwR5APg/BHkefBH8Efo/8AfO7yD7MpzvYWvLvRWFqsFGfW6rTddZ3KqujsrHV3UtHrsm85CuklEVUJCNVWK6bnRXbreaYAk5bKCVdOcYxNq4OWxTAZLbCa5gSlEmE4mMgJRMZRJiSPaJEh+wQQDzRylvU1DQZKcuamREpQMlsiYTAnAxnEmJIEoSjKP7iQQDzV1q/jr6jmvam/1Q45gaustNFJRhA7Utms7Rbd11yMVEUxCNB7a46DeO0ExMqycXCVubhoqcrZJ2DJq0SStvxe4u604m62LrrXBJN786Tc2C122xOEHtpuVlekTTdTM49QbtUTGIikk1Ys26CTZk0SKAFWdrPXrux2Mh8/r2bnNQ9Qt2HVUMrU26F65oMp1mmBYmrK2532YzC1wnOADWLWtTGSXCERAZfVsnJdWsJF21YpV21KD9TSv6jaFV5gW16cr1h/48WBSlsZAB7VKUprZrXCMWMYyb50fGMYw4cZXXS/GDWmiL9vvY9HUsh7FyNvTfYewyzcq3kI8lgbFkyphX2yMezPGMPGWdAZsuu+OPiiPvAJDCexWMfVtK/Tq6NGrbeinrJRX0q65mKrqKttN6uqxEfDIJuV0WVg/wBrVQmPkcZ2M+las0bliql1rMa9+e9kBJtNtms2m9iJH5hJtV7kTI/uWycT8HjGMYx485WzXBkNc7f25QZtQ7MNp3Q239ZvHCZUmE6zfUqqwd5qjJ8dURe2ur2apy9rkowyaKhafb6+5jAkEYiwniLJ5FbjSKnsCGGv3GDZT0WDxpJNknZVCOI2WjlQcRk3DSLZRCRg5+JcgV3ET0O7YzES8IR3HPWrlMipcMp8WtdrGFtP2XdN0rngqh/hV53vty2U92zcJnRdRtjhpi3uC32IeIKHReQ+xF7bFukjiVZof66699rr+4VX9bQ1cvRhTo1LaqeRW1EXDn00UFXK7WbGYxVqylCGXK1mMlmwLdxd8/kKoJ5NFbexQyjl0MzSz527tqoy3rWcx9QX7brzaj1rydKDK1Zr3wqWK8osFc1ajKQKG3nfGtTIbA3Tsbc0QodzSG9MqGoKRKgmUrO0u61P3Gz362QLoihyy1TcSVkrlThZMSIpuZilWt7GC+gpCJlX9k847Ro0j2jVgwat2TFk3RaMmTRFNs0aNGyZUW7Vq3RKRFu3bokIkiikQiaSZCpplKUoAHIyG2tIal0PWqVerXqUc6kicwyaqWbTTSr/AHmRguE7LoI/IuMUpKm3HPatKoTC4zGPnHMplLGxfZfau37r4QK4MuaFp1yx9qEpsnCuqbvx6i2NaxVRKFMa2cCyWvrhfxCu3GnaHK69WuzVaej9/X2KtlbZ14ZYXkMzYWHZcwo2m/5KOYoA6OhdmCRPgKu0vdaPPJQCeyZW907X4G0RjmEs0JEWKGel8HkROxrKXjHZPsPBywkEXDRcvQiHiqkcPsfr7zt8Y97h2rX71u3OxdiYizp3quXTszTWVXRNGRkUcSlD8dYCx65+fWWw+P6s4zZPzKcvMlSBzvsmkxteddhchqmzg5TS2TgxbYkThOLJGUJRkJRPj1I8DnCjI1jDRsfERbVJjGRTFpGxzJABKg0YsUE2rRqiURESpN26SaSYCIiBCAHY4zm4zmwBECMQBEAAAAAAAeAAB8AAfAA+AOKTnNs5sZOTGMlKbGTkZznOZMpznKRMpSlImUpSJMiSSSTxjGMzzXjGMYcOMYxhw4xjGHDjGMYcOMYxhw4xjGHDjGMYcOMYxhw4xjGHDn//2Q=="}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:18.6603496 +08:00 星期四 L RuoYi.System.Services.SysLogininforService[0] #38
      [*******]X.X.X.X[admin][Success][登录成功]
info: 2025-08-28 15:13:18.7429030 +08:00 星期四 L System.Logging.LoggingMonitor[0] #42
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-d525b3bccfd0acfae16a103e79620c57-9bc22cd2015fea46-00
      ┣ 服务线程 ID：                  #38
      ┣ 执行耗时：                     1914ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"a-123456","code":"7","uuid":"3c25faf5-c6f6-4472-8db7-1d35c83926be"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                   {"msg":"操作成功.","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA","code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:18.8067520 +08:00 星期四 L System.Logging.LoggingMonitor[0] #41
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetInfo
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetInfo
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetInfo
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getInfo
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-206a6c028c17e517c949df96733bce99-3ce5f43016fe80cc-00
      ┣ 服务线程 ID：                    #41
      ┣ 执行耗时：                       8ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"permissions":["*:*:*"],"roles":["admin"],"msg":"操作成功.","user":{"userId":1,"deptId":103,"userName":"admin","nickName":"isdn","email":"<EMAIL>","phonenumber":"15888888888","sex":"0","sexDesc":null,"avatar":"","status":"0","statusDesc":null,"delFlag":"0","loginIp":"*******","loginDate":"2025-08-28T14:43:19","dept":{"deptId":103,"parentId":101,"ancestors":"0,100,101","deptName":"研发部门","orderNum":1,"leader":"若依","phone":"15888888888","email":"<EMAIL>","status":"0","delFlag":"0","parentName":null,"children":[],"deptCheckStrictly":null,"roleId":null,"parentIds":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"","updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"deptName":null,"deptLeader":null,"roles":[{"roleId":1,"roleName":"超级管理员","roleKey":"admin","roleSort":1,"dataScope":"1","dataScopeDesc":null,"menuCheckStrictly":true,"deptCheckStrictly":true,"status":"0","statusDesc":null,"delFlag":"0","flag":false,"menuIds":null,"deptIds":null,"permissions":null,"userId":null,"userName":null,"createBy":"admin","createTime":"2024-11-06T07:07:10","updateBy":"","updateTime":null,"remark":"超级管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"roleIds":null,"postIds":null,"roleId":null,"isAllocated":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"admin","updateTime":"2025-08-19T17:11:13","remark":"管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:18.8965192 +08:00 星期四 L System.Logging.LoggingMonitor[0] #42
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetRouters
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetRouters
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetRouters
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getRouters
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-0f5dc2bd9c7ebba8505279cfe8e9d928-63d8b985f65db494-00
      ┣ 服务线程 ID：                    #42
      ┣ 执行耗时：                       39ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"name":"System","path":"/system","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统管理","icon":"system","noCache":false,"link":null},"children":[{"name":"User","path":"user","hidden":false,"redirect":null,"component":"system/user/index","query":"","alwaysShow":false,"meta":{"title":"用户管理","icon":"user","noCache":false,"link":null},"children":null},{"name":"Role","path":"role","hidden":false,"redirect":null,"component":"system/role/index","query":"","alwaysShow":false,"meta":{"title":"角色管理","icon":"peoples","noCache":false,"link":null},"children":null},{"name":"Menu","path":"menu","hidden":false,"redirect":null,"component":"system/menu/index","query":"","alwaysShow":false,"meta":{"title":"菜单管理","icon":"tree-table","noCache":false,"link":null},"children":null},{"name":"Dept","path":"dept","hidden":false,"redirect":null,"component":"system/dept/index","query":"","alwaysShow":false,"meta":{"title":"部门管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Post","path":"post","hidden":false,"redirect":null,"component":"system/post/index","query":"","alwaysShow":false,"meta":{"title":"岗位管理","icon":"post","noCache":false,"link":null},"children":null},{"name":"Dict","path":"dict","hidden":false,"redirect":null,"component":"system/dict/index","query":"","alwaysShow":false,"meta":{"title":"字典管理","icon":"dict","noCache":false,"link":null},"children":null},{"name":"Config","path":"config","hidden":false,"redirect":null,"component":"system/config/index","query":"","alwaysShow":false,"meta":{"title":"参数设置","icon":"edit","noCache":false,"link":null},"children":null},{"name":"Notice","path":"notice","hidden":false,"redirect":null,"component":"system/notice/index","query":"","alwaysShow":false,"meta":{"title":"通知公告","icon":"message","noCache":false,"link":null},"children":null},{"name":"Log","path":"log","hidden":false,"redirect":"noRedirect","component":"ParentView","query":"","alwaysShow":true,"meta":{"title":"日志管理","icon":"log","noCache":false,"link":null},"children":[{"name":"Operlog","path":"operlog","hidden":false,"redirect":null,"component":"monitor/operlog/index","query":"","alwaysShow":false,"meta":{"title":"操作日志","icon":"form","noCache":false,"link":null},"children":null},{"name":"Logininfor","path":"logininfor","hidden":false,"redirect":null,"component":"monitor/logininfor/index","query":"","alwaysShow":false,"meta":{"title":"登录日志","icon":"logininfor","noCache":false,"link":null},"children":null}]}]},{"name":"Monitor","path":"/monitor","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统监控","icon":"monitor","noCache":false,"link":null},"children":[{"name":"Online","path":"online","hidden":false,"redirect":null,"component":"monitor/online/index","query":"","alwaysShow":false,"meta":{"title":"在线用户","icon":"online","noCache":false,"link":null},"children":null},{"name":"Job","path":"job","hidden":false,"redirect":null,"component":"monitor/job/index","query":"","alwaysShow":false,"meta":{"title":"定时任务","icon":"job","noCache":false,"link":null},"children":null},{"name":"Server","path":"server","hidden":false,"redirect":null,"component":"monitor/server/index","query":"","alwaysShow":false,"meta":{"title":"服务监控","icon":"server","noCache":false,"link":null},"children":null},{"name":"Cache","path":"cache","hidden":false,"redirect":null,"component":"monitor/cache/index","query":"","alwaysShow":false,"meta":{"title":"缓存监控","icon":"redis","noCache":false,"link":null},"children":null},{"name":"CacheList","path":"cacheList","hidden":false,"redirect":null,"component":"monitor/cache/list","query":"","alwaysShow":false,"meta":{"title":"缓存列表","icon":"redis-list","noCache":false,"link":null},"children":null}]},{"name":"Tool","path":"/tool","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统工具","icon":"tool","noCache":false,"link":null},"children":[{"name":"Gen","path":"gen","hidden":false,"redirect":null,"component":"tool/gen/index","query":"","alwaysShow":false,"meta":{"title":"代码生成","icon":"code","noCache":false,"link":null},"children":null}]},{"name":"Wcs","path":"/wcs","hidden":false,"redirect":"noRedirect","component":"Layout","query":null,"alwaysShow":true,"meta":{"title":"wcs管理","icon":"international","noCache":false,"link":null},"children":[{"name":"Device","path":"device","hidden":false,"redirect":null,"component":"wcs/device/index","query":null,"alwaysShow":false,"meta":{"title":"设备管理","icon":"shopping","noCache":false,"link":null},"children":null},{"name":"Lane","path":"lane","hidden":false,"redirect":null,"component":"wcs/lane/index","query":null,"alwaysShow":false,"meta":{"title":"巷道管理","icon":"table","noCache":false,"link":null},"children":null},{"name":"Location","path":"location","hidden":false,"redirect":null,"component":"wcs/location/index","query":null,"alwaysShow":false,"meta":{"title":"库位管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Container","path":"container","hidden":false,"redirect":null,"component":"wcs/container/index","query":null,"alwaysShow":false,"meta":{"title":"容器档","icon":"build","noCache":false,"link":null},"children":null},{"name":"Task","path":"task","hidden":false,"redirect":null,"component":"wcs/task/index","query":null,"alwaysShow":false,"meta":{"title":"WCS任务档","icon":"email","noCache":false,"link":null},"children":null}]}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:22.4849933 +08:00 星期四 L System.Logging.LoggingMonitor[0] #41
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/is_visible_flag
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-c58fa2f31efb88c0fbd52da77b2b22d5-111c9a903c4fae8f-00
      ┣ 服务线程 ID：                    #41
      ┣ 执行耗时：                       2ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              is_visible_flag
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":109,"dictSort":0,"dictLabel":"是","dictValue":"1","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:36","updateBy":"admin","updateTime":"2025-08-13T16:43:30"},{"dictCode":110,"dictSort":2,"dictLabel":"否","dictValue":"2","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:44","updateBy":"admin","updateTime":"2025-08-13T16:43:35"}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:22.5154927 +08:00 星期四 L System.Logging.LoggingMonitor[0] #38
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/task_type
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-f656f748cc5747fee1ae370d6a609911-d821c99dc689820a-00
      ┣ 服务线程 ID：                    #38
      ┣ 执行耗时：                       10ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              task_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":122,"dictSort":1,"dictLabel":"入库","dictValue":"1","dictType":"task_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:54:54","updateBy":null,"updateTime":null},{"dictCode":123,"dictSort":2,"dictLabel":"出库","dictValue":"2","dictType":"task_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:55:03","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:22.5659520 +08:00 星期四 L System.Logging.LoggingMonitor[0] #40
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/task_status
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-df8fdf5ce59fb0a43318e20180cf0e4e-18d84c441ab9baa2-00
      ┣ 服务线程 ID：                    #40
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              task_status
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":118,"dictSort":1,"dictLabel":"未执行","dictValue":"1","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:03","updateBy":null,"updateTime":null},{"dictCode":119,"dictSort":2,"dictLabel":"执行中","dictValue":"2","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:18","updateBy":null,"updateTime":null},{"dictCode":120,"dictSort":3,"dictLabel":"执行失败","dictValue":"3","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:29","updateBy":null,"updateTime":null},{"dictCode":121,"dictSort":4,"dictLabel":"执行成功","dictValue":"4","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:38","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:13:22.5783226 +08:00 星期四 L System.Logging.LoggingMonitor[0] #42
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsTaskController.GetWcsTaskPagedList
      ┣ 
      ┣ 控制器名称：                     WcsTaskController
      ┣ 操作名称：                       GetWcsTaskPagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsTask; [action]: GetWcsTaskPagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/task/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-05c14ad7a658172912cf0a29fc293842-b9785cbb97be6690-00
      ┣ 服务线程 ID：                    #42
      ┣ 执行耗时：                       170ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsTaskDto)：               {"taskId":null,"taskCode":null,"binNo":null,"equipmentNo":null,"taskType":0,"startLocation":null,"endLocation":null,"retries":null,"maxRetries":null,"errMsg":null,"taskStatus":null,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsTaskDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsTaskDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":5,"rows":[{"taskId":1,"taskCode":null,"binNo":null,"equipmentNo":null,"taskType":1,"startLocation":"5675","endLocation":"56756","retries":55,"maxRetries":66,"errMsg":null,"taskStatus":null,"isVisibleFlag":2,"createdBy":1,"updatedBy":1,"createdTime":"2025-08-13T17:29:33","updatedTime":"2025-08-13T17:46:30","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"taskId":2,"taskCode":null,"binNo":"0","equipmentNo":null,"taskType":0,"startLocation":null,"endLocation":null,"retries":null,"maxRetries":null,"errMsg":null,"taskStatus":0,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":"2025-08-27T17:34:53","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"taskId":3,"taskCode":"a112320250828135601","binNo":"123","equipmentNo":"a1","taskType":1,"startLocation":null,"endLocation":null,"retries":null,"maxRetries":null,"errMsg":null,"taskStatus":1,"isVisibleFlag":1,"createdBy":null,"updatedBy":1,"createdTime":"2025-08-28T13:56:01","updatedTime":"2025-08-28T14:29:34","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"taskId":4,"taskCode":"a112320250828143553","binNo":"123","equipmentNo":"a1","taskType":1,"startLocation":"232","endLocation":"123","retries":null,"maxRetries":null,"errMsg":null,"taskStatus":1,"isVisibleFlag":1,"createdBy":null,"updatedBy":null,"createdTime":"2025-08-28T14:35:54","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"taskId":5,"taskCode":"a1120250828144846","binNo":"1","equipmentNo":"a1","taskType":123,"startLocation":"1","endLocation":"123","retries":null,"maxRetries":null,"errMsg":null,"taskStatus":1,"isVisibleFlag":1,"createdBy":null,"updatedBy":null,"createdTime":"2025-08-28T14:48:46","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:14:08.8435175 +08:00 星期四 L System.Logging.LoggingMonitor[0] #29
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_type
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-d528cbcef164afcec792784cb880faff-5734d293c395a415-00
      ┣ 服务线程 ID：                    #29
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":107,"dictSort":0,"dictLabel":"堆垛机","dictValue":"1","dictType":"device_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:30:45","updateBy":null,"updateTime":null},{"dictCode":108,"dictSort":2,"dictLabel":"输送线","dictValue":"2","dictType":"device_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:30:56","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:14:09.0276407 +08:00 星期四 L System.Logging.LoggingMonitor[0] #41
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_online
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-f9640cba47f7afb481b0fff3df5fadc6-bf96625474d21d04-00
      ┣ 服务线程 ID：                    #41
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_online
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":100,"dictSort":1,"dictLabel":"未知","dictValue":"1","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:36","updateBy":"admin","updateTime":"2025-08-06T10:26:09"},{"dictCode":101,"dictSort":2,"dictLabel":"在线","dictValue":"2","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:47","updateBy":"admin","updateTime":"2025-08-06T10:26:05"},{"dictCode":102,"dictSort":3,"dictLabel":"离线","dictValue":"3","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:57","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:14:09.1098324 +08:00 星期四 L System.Logging.LoggingMonitor[0] #42
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_run_status
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-95225b62ace224fabdc41a13ebf30c0a-13e607b362a1e3c8-00
      ┣ 服务线程 ID：                    #42
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_run_status
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":103,"dictSort":1,"dictLabel":"未知","dictValue":"1","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:26:58","updateBy":null,"updateTime":null},{"dictCode":104,"dictSort":2,"dictLabel":"运行","dictValue":"2","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:10","updateBy":null,"updateTime":null},{"dictCode":105,"dictSort":3,"dictLabel":"停止","dictValue":"3","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:23","updateBy":"admin","updateTime":"2025-08-06T10:27:28"},{"dictCode":106,"dictSort":4,"dictLabel":"报警","dictValue":"4","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:41","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-28 15:14:09.1629311 +08:00 星期四 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsDeviceController.GetWcsDevicePagedList
      ┣ 
      ┣ 控制器名称：                     WcsDeviceController
      ┣ 操作名称：                       GetWcsDevicePagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsDevice; [action]: GetWcsDevicePagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/device/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-8492a414c491fb5d5f4cc315ddbd2973-9549e6829461e8b9-00
      ┣ 服务线程 ID：                    #12
      ┣ 执行耗时：                       408ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ 
      ┣ login_user_key (string)：        87e870e2-ae89-4525-9169-ae73d6527838
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756969998 (2025-09-04 15:13:18:0000(+08:00) 星期四 L)
      ┣ iat (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ nbf (integer64)：                1756365198 (2025-08-28 15:13:18:0000(+08:00) 星期四 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=VtGeJFZ1mH7XFTI8J8f4DBNCozhnPm2aaOmVqSUtu1qKOSY2BviCokTeQP1lCjt73rGs0euLpGdc4T2+pj5mmA==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg3ZTg3MGUyLWFlODktNDUyNS05MTY5LWFlNzNkNjUyNzgzOCIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY5Njk5OTgsImlhdCI6MTc1NjM2NTE5OCwibmJmIjoxNzU2MzY1MTk4LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.ivwWG8968oPsKaFCz9MH0b1eCbGgF3TK2PY9_RPiSjA
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsDeviceDto)：             {"deviceCode":null,"deviceName":null,"deviceType":null,"addr":null,"port":null,"online":null,"runStatus":null,"deviceId":null,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsDeviceDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsDeviceDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":3,"rows":[{"deviceCode":"a1","deviceName":"b2","deviceType":1,"addr":"127.0.0.1","port":60001,"online":2,"runStatus":2,"deviceId":1,"isVisibleFlag":1,"createdBy":0,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-28T14:47:57","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"234","deviceName":"234","deviceType":1,"addr":"127.0.0.1","port":60002,"online":3,"runStatus":null,"deviceId":2,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-28T14:48:13","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"23","deviceName":"23","deviceType":1,"addr":"127.0.0.1","port":60003,"online":3,"runStatus":null,"deviceId":3,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-28T14:48:29","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━

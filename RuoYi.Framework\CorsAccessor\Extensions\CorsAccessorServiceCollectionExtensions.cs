using Microsoft.AspNetCore.Cors.Infrastructure;
using RuoYi.Framework;
using RuoYi.Framework.CorsAccessor;

namespace Microsoft.Extensions.DependencyInjection;

/// <summary>
///   跨域访问服务拓展类
/// </summary>
[SuppressSniffer]
public static class CorsAccessorServiceCollectionExtensions
{
  /// <summary>
  ///   配置跨域
  /// </summary>
  /// <param name="services">服务集合</param>
  /// <param name="corsOptionsHandler"></param>
  /// <param name="corsPolicyBuilderHandler"></param>
  /// <returns>服务集合</returns>
  public static IServiceCollection AddCorsAccessor(this IServiceCollection services,
    Action<CorsOptions> corsOptionsHandler = default, Action<CorsPolicyBuilder> corsPolicyBuilderHandler = default)
  {
    // 添加跨域配置选项
    services.AddConfigurableOptions<CorsAccessorSettingsOptions>();

    // 获取选项
    var corsAccessorSettings = App.GetConfig<CorsAccessorSettingsOptions>("CorsAccessorSettings", true);

    // 添加跨域服务
    services.AddCors(options =>
    {
      // 添加策略跨域
      options.AddPolicy(corsAccessorSettings.PolicyName, builder =>
      {
        // 设置跨域策略
        Penetrates.SetCorsPolicy(builder, corsAccessorSettings);

        // 添加自定义配置
        corsPolicyBuilderHandler?.Invoke(builder);
      });

      // 添加自定义配置
      corsOptionsHandler?.Invoke(options);
    });

    return services;
  }
}

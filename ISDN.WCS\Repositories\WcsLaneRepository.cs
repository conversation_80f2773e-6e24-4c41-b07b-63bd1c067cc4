using RuoYi.Data;
using RuoYi.Common.Data;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using SqlSugar;

namespace ISDN.WCS.Repositories
{
    /// <summary>
    ///  巷道管理 Repository
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:42
    /// </summary>
public class WcsLaneRepository : BaseRepository<WcsLane, WcsLaneDto>
    {
    public WcsLaneRepository(ISqlSugarRepository<WcsLane> sqlSugarRepository)
        {
        Repo = sqlSugarRepository;
        }

        /// <summary>
        /// 构造条件查询器
        /// </summary>
        /// <param name="dto">domain</param>
        /// <returns></returns>
        public override ISugarQueryable<WcsLane> Queryable(WcsLaneDto dto)
            {
            return Repo.AsQueryable()
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.LaneName), (t) => t.LaneName.Contains(dto.LaneName))
                            .WhereIF(dto.LaneType > 0, (t) => t.LaneType == dto.LaneType)
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.LaneCode), (t) => t.LaneCode == dto.LaneCode)
;
            }

            /// <summary>
            /// 构造条件查询器
            /// </summary>
            /// <param name="dto">domain</param>
            /// <returns></returns>
            public override ISugarQueryable<WcsLaneDto> DtoQueryable(WcsLaneDto dto)
                {
                return Repo.AsQueryable()
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.LaneName), (t) => t.LaneName.Contains(dto.LaneName))
                            .WhereIF(dto.LaneType > 0, (t) => t.LaneType == dto.LaneType)
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.LaneCode), (t) => t.LaneCode == dto.LaneCode)
                .Select((t) => new WcsLaneDto
                {
                     LaneId = t.LaneId
                }, true);
        }
    }
}

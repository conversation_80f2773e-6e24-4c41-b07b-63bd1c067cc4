{"Version": 1, "ManifestType": "Publish", "Endpoints": [{"Route": "images/logo.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8993"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI=\""}, {"Name": "Last-Modified", "Value": "Wed, 27 Aug 2025 03:47:46 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI="}]}, {"Route": "images/logo.txuyb3nd63.png", "AssetFile": "images/logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8993"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI=\""}, {"Name": "Last-Modified", "Value": "Wed, 27 Aug 2025 03:47:46 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "txuyb3nd63"}, {"Name": "integrity", "Value": "sha256-36cHNDEi8hqHRmkj3LA71rO5LsRAr6MgJPeYEkBaYbI="}, {"Name": "label", "Value": "images/logo.png"}]}]}
using Mapster;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Data;
using RuoYi.Framework.DependencyInjection;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.Repositories;

namespace ISDN.WCS.Services
{
    /// <summary>
    ///  设备管理 Service
    ///  author zgq
    ///  date   2025-08-13 16:36:40
    /// </summary>
    public class WcsDeviceService : BaseService<WcsDevice, WcsDeviceDto>, ITransient
    {
        private readonly ILogger<WcsDeviceService> _logger;
        private readonly WcsDeviceRepository _wcsDeviceRepository;

        public WcsDeviceService(ILogger<WcsDeviceService> logger,
            WcsDeviceRepository wcsDeviceRepository)
        {
            BaseRepo = wcsDeviceRepository;

            _logger = logger;
            _wcsDeviceRepository = wcsDeviceRepository;
        }

        /// <summary>
        /// 查询 设备管理 详情
        /// </summary>
        public async Task<WcsDevice> GetAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.DeviceId == id);
            return entity;
        }

        /// <summary>
        /// 查询 设备管理 详情
        /// </summary>
        public async Task<WcsDeviceDto> GetDtoAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.DeviceId == id);
            var dto = entity.Adapt<WcsDeviceDto>();
            // TODO 填充关联表数据
            return dto;
        }
    }
}

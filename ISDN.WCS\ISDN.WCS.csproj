﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>

      <OutputType>Library</OutputType>
      <SccProjectName>SAK</SccProjectName>
      <SccLocalPath>SAK</SccLocalPath>
      <SccAuxPath>SAK</SccAuxPath>
      <SccProvider>SAK</SccProvider>
      <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
      
  </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\RuoYi.Common\RuoYi.Common.csproj" />
        <ProjectReference Include="..\RuoYi.Data\RuoYi.Data.csproj" />
        <ProjectReference Include="..\RuoYi.Framework\RuoYi.Framework.csproj" />
        <ProjectReference Include="..\RuoYi.System\RuoYi.System.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="PLC\SocketServer\" />
    </ItemGroup>

</Project>

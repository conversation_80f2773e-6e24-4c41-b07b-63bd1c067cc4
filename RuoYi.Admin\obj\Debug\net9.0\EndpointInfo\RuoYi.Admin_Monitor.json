{"openapi": "3.0.4", "info": {"title": "Monitor", "version": "1.0.0"}, "paths": {"/monitor/cache": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "缓存监控", "operationId": "monitor-cache-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/cache/getNames": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "取缓存信息", "operationId": "monitor-cache-getNames-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/cache/getKeys/{cacheName}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "取缓存 key", "operationId": "monitor-cache-getKeys-cacheName-Get", "parameters": [{"name": "cacheName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/cache/getValue/{cacheName}/{cacheKey}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "取缓存 值", "operationId": "monitor-cache-getValue-cacheName--cacheKey-Get", "parameters": [{"name": "cacheName", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "cache<PERSON>ey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/cache/clearCacheName/{cacheName}": {"delete": {"tags": ["<PERSON><PERSON>"], "summary": "按名字删除缓存", "operationId": "monitor-cache-clearCacheName-cacheName-Delete", "parameters": [{"name": "cacheName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/cache/clearCacheKey/{cacheKey}": {"delete": {"tags": ["<PERSON><PERSON>"], "summary": "按key删除缓存", "operationId": "monitor-cache-clearCacheKey-cacheKey-Delete", "parameters": [{"name": "cache<PERSON>ey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/cache/clearCacheAll": {"delete": {"tags": ["<PERSON><PERSON>"], "summary": "删除全部缓存", "operationId": "monitor-cache-clearCacheAll-Delete", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/server": {"get": {"tags": ["Server"], "summary": "服务器监控", "operationId": "monitor-server-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/logininfor/list": {"get": {"tags": ["SysLogininfor"], "summary": "查询系统访问记录列表", "operationId": "monitor-logininfor-list-Get", "parameters": [{"name": "InfoId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>pad<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "LoginLocation", "in": "query", "schema": {"type": "string"}}, {"name": "Browser", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Msg", "in": "query", "schema": {"type": "string"}}, {"name": "LoginTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysLogininforDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysLogininforDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysLogininforDto"}}}}}}}, "/monitor/logininfor": {"get": {"tags": ["SysLogininfor"], "summary": "获取 系统访问记录 详细信息", "operationId": "monitor-logininfor-Get", "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["SysLogininfor"], "summary": "新增 系统访问记录", "operationId": "monitor-logininfor-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysLogininfor"], "summary": "修改 系统访问记录", "operationId": "monitor-logininfor-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysLogininforDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/logininfor/{id}": {"get": {"tags": ["SysLogininfor"], "summary": "获取 系统访问记录 详细信息", "operationId": "monitor-logininfor-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/logininfor/{ids}": {"delete": {"tags": ["SysLogininfor"], "summary": "删除 系统访问记录", "operationId": "monitor-logininfor-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/logininfor/import": {"post": {"tags": ["SysLogininfor"], "summary": "导入 系统访问记录", "operationId": "monitor-logininfor-import-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/monitor/logininfor/export": {"post": {"tags": ["SysLogininfor"], "summary": "导出 系统访问记录", "operationId": "monitor-logininfor-export-Post", "parameters": [{"name": "InfoId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>pad<PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "LoginLocation", "in": "query", "schema": {"type": "string"}}, {"name": "Browser", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Msg", "in": "query", "schema": {"type": "string"}}, {"name": "LoginTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/monitor/operlog/list": {"get": {"tags": ["SysOperLog"], "operationId": "monitor-operlog-list-Get", "parameters": [{"name": "OperId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Title", "in": "query", "schema": {"type": "string"}}, {"name": "BusinessType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "BusinessTypeDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "RequestMethod", "in": "query", "schema": {"type": "string"}}, {"name": "OperatorType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OperName", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "schema": {"type": "string"}}, {"name": "OperUrl", "in": "query", "schema": {"type": "string"}}, {"name": "OperIp", "in": "query", "schema": {"type": "string"}}, {"name": "OperLocation", "in": "query", "schema": {"type": "string"}}, {"name": "OperParam", "in": "query", "schema": {"type": "string"}}, {"name": "JsonResult", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Error<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "OperTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CostTime", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysOperLogDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysOperLogDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysOperLogDto"}}}}}}}, "/monitor/operlog/{ids}": {"delete": {"tags": ["SysOperLog"], "summary": "删除 操作日志记录", "operationId": "monitor-operlog-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/operlog/clean": {"delete": {"tags": ["SysOperLog"], "summary": "删除 操作日志记录", "operationId": "monitor-operlog-clean-Delete", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/operlog/export": {"post": {"tags": ["SysOperLog"], "operationId": "monitor-operlog-export-Post", "parameters": [{"name": "OperId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "Title", "in": "query", "schema": {"type": "string"}}, {"name": "BusinessType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "BusinessTypeDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "RequestMethod", "in": "query", "schema": {"type": "string"}}, {"name": "OperatorType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OperName", "in": "query", "schema": {"type": "string"}}, {"name": "DeptName", "in": "query", "schema": {"type": "string"}}, {"name": "OperUrl", "in": "query", "schema": {"type": "string"}}, {"name": "OperIp", "in": "query", "schema": {"type": "string"}}, {"name": "OperLocation", "in": "query", "schema": {"type": "string"}}, {"name": "OperParam", "in": "query", "schema": {"type": "string"}}, {"name": "JsonResult", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Error<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "OperTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CostTime", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/monitor/online/list": {"get": {"tags": ["SysUserOnline"], "summary": "查询操作日志记录列表", "operationId": "monitor-online-list-Get", "parameters": [{"name": "ipaddr", "in": "query", "schema": {"type": "string"}}, {"name": "userName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserOnline"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserOnline"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysUserOnline"}}}}}}}, "/monitor/online/{tokenId}": {"delete": {"tags": ["SysUserOnline"], "summary": "强退用户", "operationId": "monitor-online-tokenId-Delete", "parameters": [{"name": "tokenId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/job/list": {"get": {"tags": ["SysJob"], "operationId": "monitor-job-list-Get", "parameters": [{"name": "JobId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "JobName", "in": "query", "required": true, "schema": {"maxLength": 64, "type": "string"}}, {"name": "JobGroup", "in": "query", "schema": {"type": "string"}}, {"name": "InvokeTarget", "in": "query", "required": true, "schema": {"maxLength": 500, "type": "string"}}, {"name": "CronExpression", "in": "query", "required": true, "schema": {"maxLength": 255, "type": "string"}}, {"name": "MisfirePolicy", "in": "query", "schema": {"type": "string"}}, {"name": "MisfirePolicyDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Concurrent", "in": "query", "schema": {"type": "string"}}, {"name": "ConcurrentDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysJobDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysJobDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysJobDto"}}}}}}}, "/monitor/job": {"get": {"tags": ["SysJob"], "operationId": "monitor-job-Get", "parameters": [{"name": "jobId", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["SysJob"], "operationId": "monitor-job-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysJob"], "operationId": "monitor-job-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/job/{jobId}": {"get": {"tags": ["SysJob"], "operationId": "monitor-job-jobId-Get", "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/job/changeStatus": {"put": {"tags": ["SysJob"], "operationId": "monitor-job-changeStatus-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/job/run": {"put": {"tags": ["SysJob"], "operationId": "monitor-job-run-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysJobDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/job/{jobIds}": {"delete": {"tags": ["SysJob"], "operationId": "monitor-job-jobIds-Delete", "parameters": [{"name": "jobIds", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/job/export": {"post": {"tags": ["SysJob"], "operationId": "monitor-job-export-Post", "parameters": [{"name": "JobId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "JobName", "in": "query", "required": true, "schema": {"maxLength": 64, "type": "string"}}, {"name": "JobGroup", "in": "query", "schema": {"type": "string"}}, {"name": "InvokeTarget", "in": "query", "required": true, "schema": {"maxLength": 500, "type": "string"}}, {"name": "CronExpression", "in": "query", "required": true, "schema": {"maxLength": 255, "type": "string"}}, {"name": "MisfirePolicy", "in": "query", "schema": {"type": "string"}}, {"name": "MisfirePolicyDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Concurrent", "in": "query", "schema": {"type": "string"}}, {"name": "ConcurrentDesc", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "StatusDesc", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/monitor/jobLog/list": {"get": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-list-Get", "parameters": [{"name": "JobLogId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "JobName", "in": "query", "schema": {"type": "string"}}, {"name": "JobGroup", "in": "query", "schema": {"type": "string"}}, {"name": "InvokeTarget", "in": "query", "schema": {"type": "string"}}, {"name": "JobMessage", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "ExceptionInfo", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysJobLogDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysJobLogDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_SysJobLogDto"}}}}}}}, "/monitor/jobLog": {"get": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-Get", "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysJobLogDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/jobLog/{id}": {"get": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/jobLog/{ids}": {"delete": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/jobLog/clean": {"delete": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-clean-Delete", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/monitor/jobLog/export": {"post": {"tags": ["SysJobLog"], "operationId": "monitor-jobLog-export-Post", "parameters": [{"name": "JobLogId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "JobName", "in": "query", "schema": {"type": "string"}}, {"name": "JobGroup", "in": "query", "schema": {"type": "string"}}, {"name": "InvokeTarget", "in": "query", "schema": {"type": "string"}}, {"name": "JobMessage", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "ExceptionInfo", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"QueryParam": {"type": "object", "properties": {"beginTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "dataScopeSql": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SqlSugarPagedList_SysJobDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysJobDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysJobLogDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysJobLogDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysLogininforDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysLogininforDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysOperLogDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysOperLogDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_SysUserOnline": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/SysUserOnline"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SysJobDto": {"required": ["cronExpression", "invoke<PERSON><PERSON><PERSON>", "job<PERSON>ame"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "jobId": {"type": "integer", "format": "int64"}, "jobName": {"maxLength": 64, "minLength": 1, "type": "string"}, "jobGroup": {"type": "string", "nullable": true}, "invokeTarget": {"maxLength": 500, "minLength": 1, "type": "string"}, "cronExpression": {"maxLength": 255, "minLength": 1, "type": "string"}, "misfirePolicy": {"type": "string", "nullable": true}, "misfirePolicyDesc": {"type": "string", "nullable": true}, "concurrent": {"type": "string", "nullable": true}, "concurrentDesc": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusDesc": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysJobLogDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "jobLogId": {"type": "integer", "format": "int64"}, "jobName": {"type": "string", "nullable": true}, "jobGroup": {"type": "string", "nullable": true}, "invokeTarget": {"type": "string", "nullable": true}, "jobMessage": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "exceptionInfo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysLogininforDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "infoId": {"type": "integer", "format": "int64"}, "userName": {"type": "string", "nullable": true}, "ipaddr": {"type": "string", "nullable": true}, "loginLocation": {"type": "string", "nullable": true}, "browser": {"type": "string", "nullable": true}, "os": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "msg": {"type": "string", "nullable": true}, "loginTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SysOperLogDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "operId": {"type": "integer", "format": "int64"}, "title": {"type": "string", "nullable": true}, "businessType": {"type": "integer", "format": "int32", "nullable": true}, "businessTypeDesc": {"type": "string", "nullable": true}, "method": {"type": "string", "nullable": true}, "requestMethod": {"type": "string", "nullable": true}, "operatorType": {"type": "integer", "format": "int32", "nullable": true}, "operName": {"type": "string", "nullable": true}, "deptName": {"type": "string", "nullable": true}, "operUrl": {"type": "string", "nullable": true}, "operIp": {"type": "string", "nullable": true}, "operLocation": {"type": "string", "nullable": true}, "operParam": {"type": "string", "nullable": true}, "jsonResult": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "errorMsg": {"type": "string", "nullable": true}, "operTime": {"type": "string", "format": "date-time", "nullable": true}, "costTime": {"type": "integer", "format": "int64", "nullable": true}}, "additionalProperties": false}, "SysUserOnline": {"type": "object", "properties": {"tokenId": {"type": "string", "nullable": true}, "deptName": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "ipaddr": {"type": "string", "nullable": true}, "loginLocation": {"type": "string", "nullable": true}, "browser": {"type": "string", "nullable": true}, "os": {"type": "string", "nullable": true}, "loginTime": {"type": "integer", "format": "int64"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}
@{
    var moduleName = Model.moduleName;
    var businessName = Model.businessName;
    var BusinessName = Model.BusinessName;
    var dictType = "";
    var attrName = "";
    var comment = "";
    var dictsNoSymbol = "";
    int parentheseIndex = -1;
    var subClassName = Model.SubClassName;
    var subclassName = Model.subClassName;
}
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
@foreach(var column in Model.Columns) {
  @if(column.Is_Query()) {
      dictType = column.DictType ?? "";
      attrName = column.NetFieldLower();
      parentheseIndex = column.ColumnComment?.IndexOf("（") ?? -1;
      comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;

     if(column.HtmlType == "input") {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-input
          v-model="queryParams.@(attrName)"
          placeholder="请输入@(comment)"
          clearable
          @@keyup.enter="handleQuery"
        />
      </el-form-item>
     } else if((column.HtmlType == "select" || column.HtmlType == "radio") && "" != dictType) {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-select v-model="queryParams.@(attrName)" placeholder="请选择@(comment)" clearable>
          <el-option
            v-for="dict in @(dictType)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
     } else if((column.HtmlType == "select" || column.HtmlType == "radio") && "" != dictType) {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-select v-model="queryParams.@(attrName)" placeholder="请选择@(comment)" clearable>
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
    } else if(column.HtmlType == "datetime" && column.QueryType != "BETWEEN") {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-date-picker clearable
          v-model="queryParams.@(attrName)"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择@(comment)">
        </el-date-picker>
      </el-form-item>
    } else if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
      <el-form-item label="@(comment)">
        <el-date-picker
          v-model="daterange@(attrName)"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    }
  }
}
      <el-form-item>
        <el-button type="primary" icon="Search" @@click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @@click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus"
          @@click="handleAdd"
          v-hasPermi="['@(moduleName):@(businessName):add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort"
          @@click="toggleExpandAll">展开/折叠</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @@queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-if="refreshTable" v-loading="loading" :data="@(businessName)List" row-key="@(Model.TreeCode)}"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
@foreach(var column in Model.Columns) {
    attrName = column.NetFieldLower();
    dictType = column.DictType ?? "";
    parentheseIndex = column.ColumnComment?.IndexOf("（") ?? -1;
    comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
    if(column.Is_Pk()) {

    } else if (column.Is_List() && column.HtmlType == "datetime") {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.@(attrName), '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
    } else if (column.Is_List() && column.HtmlType == "imageUpload") {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.@(attrName)" :width="50" :height="50"/>
        </template>
      </el-table-column>
    } else if (column.Is_List() && "" != dictType) {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)">
        <template #default="scope">
        @if(column.HtmlType == "checkbox") {
          <dict-tag :options="@(dictType)" :value="scope.row.@(attrName) ? scope.row.@(attrName).split(',') : []"/>
        } else {
          <dict-tag :options="@(dictType)" :value="scope.row.@(attrName)" />
        }
        </template>
      </el-table-column>
    } else if (column.Is_List() && "" != attrName) {
     @if(Model.Columns.IndexOf(column) ==  1) {
      <el-table-column label="@(comment)" prop="@(attrName)" />
     } else {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" />
     }
    }
}
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button link type="primary" icon="Edit"
            @@click="handleUpdate(scope.row)"
            v-hasPermi="['@(moduleName):@(businessName):edit']"
          >修改</el-button>
          <el-button link type="primary" icon="Plus"
            @@click="handleAdd(scope.row)"
            v-hasPermi="['@(moduleName):@(businessName):add']"
          >新增</el-button>
          <el-button link type="primary" icon="Delete"
            @@click="handleDelete(scope.row)"
            v-hasPermi="['@(moduleName):@(businessName):remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改@(Model.FunctionName)对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="${businessName}Ref" :model="form" :rules="rules" label-width="80px">
@foreach(var column in Model.Columns) {
  attrName = column.NetFieldLower();
  if(column.Is_Insert() && !column.Is_Pk()) {
    if(column.IsUsableColumn() || !column.IsSuperColumn()) {
      parentheseIndex = column.ColumnComment.IndexOf("（");
      comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
      dictType = column.DictType ?? "";
      var selectVal = column.NetType.Contains("int") || column.NetType.Contains("long") ? "parseInt(dict.value)" : "dict.value";
      var radioLabel = column.NetType.Contains("int") || column.NetType.Contains("long") ? "parseInt(dict.value)" : "dict.value";
      @if ("" != Model.TreeParentCode && column.NetField == Model.TreeParentCode) {
        <el-form-item label="@(comment)" prop="@(Model.treeParentCode)">
            <el-tree-select
                v-model="form.@(Model.treeParentCode)"
                :data="@(businessName)Options"
                :props="{ value: '@(Model.TreeCode)', label: '@(Model.treeName)', children: 'children' }"
                value-key="@(Model.TreeCode)"
                placeholder="请选择@(comment)"
                check-strictly
            />
        </el-form-item>
      } else if(column.HtmlType == "input") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-input v-model="form.@(attrName)" placeholder="请输入@(comment)" />
        </el-form-item>
      } else if(column.HtmlType == "imageUpload") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <image-upload v-model="form.@(attrName)"/>
        </el-form-item>
      } else if(column.HtmlType == "fileUpload") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <file-upload v-model="form.@(attrName)"/>
        </el-form-item>
      } else if(column.HtmlType == "editor") {
        <el-form-item label="@(comment)">
          <editor v-model="form.@(attrName)" :min-height="192"/>
        </el-form-item>
      } else if(column.HtmlType == "select" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-select v-model="form.@(attrName)" placeholder="请选择@(comment)">
            <el-option
              v-for="dict in @(dictType)"
              :key="dict.value"
              :label="dict.label"
              :value = "@(selectVal)"
            ></el-option>
          </el-select>
        </el-form-item>
      } else if(column.HtmlType == "select" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-select v-model="form.@(attrName)" placeholder="请选择@(comment)">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
      } else if(column.HtmlType == "checkbox" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-checkbox-group v-model="form.@(attrName)">
            <el-checkbox
              v-for="dict in @(dictType)"
              :key="dict.value"
              :label="dict.value">
              {{dict.label}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      } else if(column.HtmlType == "checkbox" &&  "" == dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-checkbox-group v-model="form.@(attrName)">
            <el-checkbox>请选择字典生成</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      } else if(column.HtmlType == "radio" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-radio-group v-model="form.@(attrName)">
            <el-radio
              v-for="dict in @(dictType)"
              :key="dict.value"
              :label="@(radioLabel)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      } else if(column.HtmlType == "radio" && "" == dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-radio-group v-model="form.@(attrName)">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
      } else if(column.HtmlType == "datetime") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-date-picker clearable
            v-model="form.@(attrName)"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择@(comment)">
          </el-date-picker>
        </el-form-item>
      } else if(column.HtmlType == "textarea") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-input v-model="form.@(attrName)" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      }
    }
  }
}
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @@click="submitForm">确 定</el-button>
        <el-button @@click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="@(BusinessName)">

import { list@(BusinessName), get@(BusinessName), del@(BusinessName), add@(BusinessName), update@(BusinessName) } from "@@/api/@(moduleName)/@(businessName)";

const { proxy } = getCurrentInstance();
@if ( Model.Dicts != "") {
dictsNoSymbol = Model.Dicts.Replace("'","");
@:const { @(dictsNoSymbol) } = proxy.useDict( @(Model.Dicts));
}

const @(businessName)List = ref([]);
const @(businessName)Options = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const isExpandAll = ref(true);
const refreshTable = ref(true);

@foreach (var column in Model.Columns) {
if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
attrName = column.NetFieldLower();
@:// @(comment)时间范围
@:daterange@(attrName): [],
}
}

const data = reactive({
    form: {},
    queryParams: {
        @foreach (var column in Model.Columns) {
        if(column.Is_Query()) {
        @:@(column.NetFieldLower()): null,
        }
        }
    },
    rules: {
        @foreach (var column in Model.Columns) {
        @if(column.Is_Query()) {
        parentheseIndex = column.ColumnComment.IndexOf("（");
        comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
        var trigger = column.HtmlType == "select" || column.HtmlType == "radio" ? "change" : "blur";
        @:@column.NetFieldLower(): [
            @:  { required: true, message: "@(comment)不能为空", trigger: "@(trigger)" }
            @:],
        }
        }
    }
});

const { queryParams, form, rules } = toRefs(data);



/** 查询@(Model.FunctionName)列表 */
function getList() {
    loading.value = true;
    @foreach (var column in Model.Columns) {
    if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
    @:queryParams.value.params = {};
    break;
    }
    }
    @foreach (var column in Model.Columns) {
    if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
    attrName = column.NetFieldLower();
    @:if (null != daterange@(attrName) && '' != daterange@(attrName)) {
        @:queryParams.value.params["begin@(attrName)"] =daterange@(attrName).value[0];
        @:queryParams.value.params["end@(attrName)"] = daterange@(attrName).value[1];
        @:}
    }
    }
    list@(BusinessName)(queryParams.value).then(response => {
        @(businessName)List.value = proxy.handleTree(response.data, "@(Model.TreeCode)", "@(Model.TreeParentCode)");
        loading.value = false;
    });
}

/** 转换@(Model.FunctionName)数据结构 */
function normalizer(node) {
    if (node.children && !node.children.length) {
        delete node.children;
    }
    return {
        id: node.@(Model.TreeCode),
        label: node.@(Model.TreeName),
        children: node.children
    };
}

/** 查询@(Model.FunctionName)下拉树结构 */
function getTreeselect() {
    list@(BusinessName)().then(response => {
        @(businessName)Options.value  =[];
        const data = { @(Model.TreeCode): 0, @(Model.TreeName): '顶级节点', children: [] };
        data.children = proxy.handleTree(response.data, "@(Model.TreeCode)", "@(Model.TreeParentCode)");
        @(businessName)Options.value.push(data);
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        @foreach (var column in Model.Columns) {
        var comma = Model.Columns.IndexOf(column) != Model.Columns.Count - 1 ? "," : "";
        if(column.HtmlType == "checkbox") {
        @:@column.NetFieldLower(): []@(comma)
        } else {
        @:@column.NetFieldLower(): null@(comma)
        }
        }
    };
    proxy.resetForm("@(businessName)Ref");
}

/** 搜索按钮操作 */
function handleQuery() {
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    @foreach (var column in Model.Columns) {
    if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
    attrName = column.NetFieldLower();
    @:daterange@(attrName).value = [];
    }
    }
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
    reset();
    getTreeselect();
    if (row != null && row.@(Model.TreeCode)) {
        form.value.@(Model.TreeParentCode) = row.@(Model.TreeCode);
    } else {
        form.value.@(Model.TreeParentCode) = 0;
    }
    open.value = true;
    title.value = "添加@(Model.FunctionName)";
}

/** 展开/折叠操作 */
function toggleExpandAll() {
    refreshTable.value = false;
    isExpandAll.value = !isExpandAll.value;
    nextTick(() => {
        refreshTable.value = true;
    });
}

/** 修改按钮操作 */
async function handleUpdate(row) {
    reset();
    await getTreeselect();
    if (row != null) {
        form.value.@(Model.TreeParentCode) = row.@(Model.TreeCode);
    }
    get@(BusinessName)(@(Model.PkColumn.NetFieldLower())).then(response => {
        form.value = response.data;
        @foreach (var column in Model.Columns) {
        if(column.HtmlType == "checkbox") {
        @:form.value.@(column.NetFieldLower()) = form.value.@(attrName).split(",");
        }
        }
        open.value = true;
        title.value = "修改@(Model.FunctionName)";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["@(businessName)Ref"].validate(valid => {
        if (valid) {
            @foreach (var column in Model.Columns) {
            if(column.HtmlType == "checkbox") {
            @:form.value.@(column.NetFieldLower()) = form.value.@(attrName).join(",");
            }
            }
            if (form.value.@(Model.PkColumn.NetFieldLower()) != null) {
                update@(BusinessName)(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                add@(BusinessName)(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    proxy.$modal.confirm('是否确认删除@(Model.FunctionName)编号为"' + row.@(Model.PkColumn.NetFieldLower()) + '"的数据项？').then(function() {
        return del@(BusinessName)(row.@(Model.PkColumn.NetFieldLower()));
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
}

getList();

</script>

info: 2025-09-03 15:10:57.2092337 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-09-03 15:10:57.2122796 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-09-03 15:10:57.2135908 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-09-03 15:10:57.3632907 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-09-03 15:10:57.3647838 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-09-03 15:10:57.4006251 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-09-03 15:10:58.6649182 +08:00 星期三 L System.Logging.LoggingMonitor[0] #9
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-fe34c06850c47b344f859dede43c0b76-e6ce67093d428aec-00
      ┣ 服务线程 ID：               #9
      ┣ 执行耗时：                  32ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:29.0711757 +08:00 星期三 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetInfo
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetInfo
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetInfo
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getInfo
      ┣ 来源地址：                       http://localhost:10081/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-8faedfd07f95fa249a64fc51d132da8c-d6c92af1a1606f91-00
      ┣ 服务线程 ID：                    #3
      ┣ 执行耗时：                       1870ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ 
      ┣ login_user_key (string)：        80eb074b-f7f8-4797-a0da-66dd987e4e67
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757420750 (2025-09-09 20:25:50:0000(+08:00) 星期二 L)
      ┣ iat (integer64)：                1756815950 (2025-09-02 20:25:50:0000(+08:00) 星期二 L)
      ┣ nbf (integer64)：                1756815950 (2025-09-02 20:25:50:0000(+08:00) 星期二 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ Referer：                        http://localhost:10081/
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"授权失败","code":401}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:29.2199576 +08:00 星期三 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Logout
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       Logout
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: Logout
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5000/logout
      ┣ 来源地址：                       http://localhost:10081/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-31f6f4452346c3e01f5bbe1dd9e0558e-7d0901cc5a431b41-00
      ┣ 服务线程 ID：                    #26
      ┣ 执行耗时：                       76ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ 
      ┣ login_user_key (string)：        80eb074b-f7f8-4797-a0da-66dd987e4e67
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757420750 (2025-09-09 20:25:50:0000(+08:00) 星期二 L)
      ┣ iat (integer64)：                1756815950 (2025-09-02 20:25:50:0000(+08:00) 星期二 L)
      ┣ nbf (integer64)：                1756815950 (2025-09-02 20:25:50:0000(+08:00) 星期二 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjgwZWIwNzRiLWY3ZjgtNDc5Ny1hMGRhLTY2ZGQ5ODdlNGU2NyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0MjA3NTAsImlhdCI6MTc1NjgxNTk1MCwibmJmIjoxNzU2ODE1OTUwLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.bBtr_-mRIlGulAtvjS7bv-5H4vBk3qH-xdcQa-BtG8I
      ┣ Origin：                         http://localhost:5000
      ┣ Referer：                        http://localhost:10081/
      ┣ Content-Length：                 0
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"退出成功","code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:29.4788724 +08:00 星期三 L System.Logging.LoggingMonitor[0] #13
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-a6f336646e31220052ad378da5589dc1-0d7ab60c72ddcfcd-00
      ┣ 服务线程 ID：               #13
      ┣ 执行耗时：                  64ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=cFkUBJpYJZVgyUABf6sloErfnjCII5USoGHioKo3Q1DgbMYaBpZBurIjTVUCCfIHGB2TGqnGISw3fHB2eHENAw==
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"ef642762-7911-4418-919b-20397c945950","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:34.1045838 +08:00 星期三 L RuoYi.System.Services.SysLogininforService[0] #26
      [*******]X.X.X.X[admin][Success][登录成功]
info: 2025-09-03 15:13:34.2002475 +08:00 星期三 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-f6e21d8f6f8af6babbd8082713af0aba-7a8ad9fa874f5740-00
      ┣ 服务线程 ID：                  #13
      ┣ 执行耗时：                     1941ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"a-123456","code":"8","uuid":"ef642762-7911-4418-919b-20397c945950"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                   {"msg":"操作成功.","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0","code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:34.2532023 +08:00 星期三 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetInfo
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetInfo
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetInfo
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getInfo
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-92c28cbf2bee4c7af64d2b8db4e69fff-62985ddb0bf92ae6-00
      ┣ 服务线程 ID：                    #26
      ┣ 执行耗时：                       7ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"permissions":["*:*:*"],"roles":["admin"],"msg":"操作成功.","user":{"userId":1,"deptId":103,"userName":"admin","nickName":"isdn","email":"<EMAIL>","phonenumber":"15888888888","sex":"0","sexDesc":null,"avatar":"","status":"0","statusDesc":null,"delFlag":"0","loginIp":"*************","loginDate":"2025-09-03T15:08:04","dept":{"deptId":103,"parentId":101,"ancestors":"0,100,101","deptName":"研发部门","orderNum":1,"leader":"若依","phone":"15888888888","email":"<EMAIL>","status":"0","delFlag":"0","parentName":null,"children":[],"deptCheckStrictly":null,"roleId":null,"parentIds":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"","updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"deptName":null,"deptLeader":null,"roles":[{"roleId":1,"roleName":"超级管理员","roleKey":"admin","roleSort":1,"dataScope":"1","dataScopeDesc":null,"menuCheckStrictly":true,"deptCheckStrictly":true,"status":"0","statusDesc":null,"delFlag":"0","flag":false,"menuIds":null,"deptIds":null,"permissions":null,"userId":null,"userName":null,"createBy":"admin","createTime":"2024-11-06T07:07:10","updateBy":"","updateTime":null,"remark":"超级管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"roleIds":null,"postIds":null,"roleId":null,"isAllocated":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"admin","updateTime":"2025-08-19T17:11:13","remark":"管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:34.3275490 +08:00 星期三 L System.Logging.LoggingMonitor[0] #12
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetRouters
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetRouters
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetRouters
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getRouters
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-9c76481d848719fd068d43bd4a1f25c0-ff3a58f9bb6d3458-00
      ┣ 服务线程 ID：                    #12
      ┣ 执行耗时：                       31ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"name":"System","path":"/system","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统管理","icon":"system","noCache":false,"link":null},"children":[{"name":"User","path":"user","hidden":false,"redirect":null,"component":"system/user/index","query":"","alwaysShow":false,"meta":{"title":"用户管理","icon":"user","noCache":false,"link":null},"children":null},{"name":"Role","path":"role","hidden":false,"redirect":null,"component":"system/role/index","query":"","alwaysShow":false,"meta":{"title":"角色管理","icon":"peoples","noCache":false,"link":null},"children":null},{"name":"Menu","path":"menu","hidden":false,"redirect":null,"component":"system/menu/index","query":"","alwaysShow":false,"meta":{"title":"菜单管理","icon":"tree-table","noCache":false,"link":null},"children":null},{"name":"Dept","path":"dept","hidden":false,"redirect":null,"component":"system/dept/index","query":"","alwaysShow":false,"meta":{"title":"部门管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Post","path":"post","hidden":false,"redirect":null,"component":"system/post/index","query":"","alwaysShow":false,"meta":{"title":"岗位管理","icon":"post","noCache":false,"link":null},"children":null},{"name":"Dict","path":"dict","hidden":false,"redirect":null,"component":"system/dict/index","query":"","alwaysShow":false,"meta":{"title":"字典管理","icon":"dict","noCache":false,"link":null},"children":null},{"name":"Config","path":"config","hidden":false,"redirect":null,"component":"system/config/index","query":"","alwaysShow":false,"meta":{"title":"参数设置","icon":"edit","noCache":false,"link":null},"children":null},{"name":"Notice","path":"notice","hidden":false,"redirect":null,"component":"system/notice/index","query":"","alwaysShow":false,"meta":{"title":"通知公告","icon":"message","noCache":false,"link":null},"children":null},{"name":"Log","path":"log","hidden":false,"redirect":"noRedirect","component":"ParentView","query":"","alwaysShow":true,"meta":{"title":"日志管理","icon":"log","noCache":false,"link":null},"children":[{"name":"Operlog","path":"operlog","hidden":false,"redirect":null,"component":"monitor/operlog/index","query":"","alwaysShow":false,"meta":{"title":"操作日志","icon":"form","noCache":false,"link":null},"children":null},{"name":"Logininfor","path":"logininfor","hidden":false,"redirect":null,"component":"monitor/logininfor/index","query":"","alwaysShow":false,"meta":{"title":"登录日志","icon":"logininfor","noCache":false,"link":null},"children":null}]}]},{"name":"Monitor","path":"/monitor","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统监控","icon":"monitor","noCache":false,"link":null},"children":[{"name":"Online","path":"online","hidden":false,"redirect":null,"component":"monitor/online/index","query":"","alwaysShow":false,"meta":{"title":"在线用户","icon":"online","noCache":false,"link":null},"children":null},{"name":"Job","path":"job","hidden":false,"redirect":null,"component":"monitor/job/index","query":"","alwaysShow":false,"meta":{"title":"定时任务","icon":"job","noCache":false,"link":null},"children":null},{"name":"Server","path":"server","hidden":false,"redirect":null,"component":"monitor/server/index","query":"","alwaysShow":false,"meta":{"title":"服务监控","icon":"server","noCache":false,"link":null},"children":null},{"name":"Cache","path":"cache","hidden":false,"redirect":null,"component":"monitor/cache/index","query":"","alwaysShow":false,"meta":{"title":"缓存监控","icon":"redis","noCache":false,"link":null},"children":null},{"name":"CacheList","path":"cacheList","hidden":false,"redirect":null,"component":"monitor/cache/list","query":"","alwaysShow":false,"meta":{"title":"缓存列表","icon":"redis-list","noCache":false,"link":null},"children":null}]},{"name":"Tool","path":"/tool","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统工具","icon":"tool","noCache":false,"link":null},"children":[{"name":"Gen","path":"gen","hidden":false,"redirect":null,"component":"tool/gen/index","query":"","alwaysShow":false,"meta":{"title":"代码生成","icon":"code","noCache":false,"link":null},"children":null}]},{"name":"Wcs","path":"/wcs","hidden":false,"redirect":"noRedirect","component":"Layout","query":null,"alwaysShow":true,"meta":{"title":"wcs管理","icon":"international","noCache":false,"link":null},"children":[{"name":"Device","path":"device","hidden":false,"redirect":null,"component":"wcs/device/index","query":null,"alwaysShow":false,"meta":{"title":"设备管理","icon":"shopping","noCache":false,"link":null},"children":null},{"name":"Lane","path":"lane","hidden":false,"redirect":null,"component":"wcs/lane/index","query":null,"alwaysShow":false,"meta":{"title":"巷道管理","icon":"table","noCache":false,"link":null},"children":null},{"name":"Location","path":"location","hidden":false,"redirect":null,"component":"wcs/location/index","query":null,"alwaysShow":false,"meta":{"title":"库位管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Container","path":"container","hidden":false,"redirect":null,"component":"wcs/container/index","query":null,"alwaysShow":false,"meta":{"title":"容器档","icon":"build","noCache":false,"link":null},"children":null},{"name":"Task","path":"task","hidden":false,"redirect":null,"component":"wcs/task/index","query":null,"alwaysShow":false,"meta":{"title":"WCS任务档","icon":"email","noCache":false,"link":null},"children":null}]}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:38.5096464 +08:00 星期三 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_online
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-afe2990012d6acc6dade00242d4d2aac-b25cedaf0324ef45-00
      ┣ 服务线程 ID：                    #26
      ┣ 执行耗时：                       1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_online
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":100,"dictSort":1,"dictLabel":"未知","dictValue":"1","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:36","updateBy":"admin","updateTime":"2025-08-06T10:26:09"},{"dictCode":101,"dictSort":2,"dictLabel":"在线","dictValue":"2","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:47","updateBy":"admin","updateTime":"2025-08-06T10:26:05"},{"dictCode":102,"dictSort":3,"dictLabel":"离线","dictValue":"3","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:57","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:38.5404027 +08:00 星期三 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/is_visible_flag
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-e231e154d34a46193ccce9ef36f23659-379f42a617993f84-00
      ┣ 服务线程 ID：                    #3
      ┣ 执行耗时：                       3ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              is_visible_flag
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":109,"dictSort":0,"dictLabel":"是","dictValue":"1","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:36","updateBy":"admin","updateTime":"2025-08-13T16:43:30"},{"dictCode":110,"dictSort":2,"dictLabel":"否","dictValue":"2","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:44","updateBy":"admin","updateTime":"2025-08-13T16:43:35"}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:38.5753602 +08:00 星期三 L System.Logging.LoggingMonitor[0] #22
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_type
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-2e45de4e8161ff7d7031c45759a7df82-97052934bdc6ed7a-00
      ┣ 服务线程 ID：                    #22
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":107,"dictSort":0,"dictLabel":"堆垛机","dictValue":"1","dictType":"device_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:30:45","updateBy":null,"updateTime":null},{"dictCode":108,"dictSort":2,"dictLabel":"输送线","dictValue":"2","dictType":"device_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:30:56","updateBy":null,"updateTime":null},{"dictCode":124,"dictSort":3,"dictLabel":"摄像机","dictValue":"3","dictType":"device_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-09-02T09:42:12","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:38.6347391 +08:00 星期三 L System.Logging.LoggingMonitor[0] #13
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_run_status
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-9880554b26b6c93ae30d78207c0b4a7b-08f40b24c74dabdf-00
      ┣ 服务线程 ID：                    #13
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_run_status
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":103,"dictSort":1,"dictLabel":"空闲","dictValue":"1","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:26:58","updateBy":"admin","updateTime":"2025-08-28T16:32:59"},{"dictCode":104,"dictSort":2,"dictLabel":"运行","dictValue":"2","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:10","updateBy":null,"updateTime":null},{"dictCode":105,"dictSort":3,"dictLabel":"故障","dictValue":"3","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:23","updateBy":"admin","updateTime":"2025-08-28T16:33:13"},{"dictCode":106,"dictSort":4,"dictLabel":"报警","dictValue":"4","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:41","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:38.6819801 +08:00 星期三 L System.Logging.LoggingMonitor[0] #12
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsDeviceController.GetWcsDevicePagedList
      ┣ 
      ┣ 控制器名称：                     WcsDeviceController
      ┣ 操作名称：                       GetWcsDevicePagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsDevice; [action]: GetWcsDevicePagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/device/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-52d160bfeb70666b380347143e3946a3-f113fef0a3bb02cc-00
      ┣ 服务线程 ID：                    #12
      ┣ 执行耗时：                       198ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsDeviceDto)：             {"deviceCode":null,"deviceName":null,"deviceType":null,"addr":null,"port":null,"online":null,"runStatus":null,"deviceId":null,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsDeviceDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsDeviceDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":6,"rows":[{"deviceCode":"D01","deviceName":"堆垛机1","deviceType":1,"addr":"*************","port":60001,"online":3,"runStatus":2,"deviceId":1,"isVisibleFlag":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-09-03T15:06:57","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"D02","deviceName":"堆垛机2","deviceType":1,"addr":"127.0.0.1","port":60002,"online":3,"runStatus":null,"deviceId":2,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-09-02T14:49:32","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"D03","deviceName":"堆垛机3","deviceType":1,"addr":"127.0.0.1","port":60003,"online":3,"runStatus":null,"deviceId":3,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-09-02T14:49:48","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"S01","deviceName":"接驳点1摄像机","deviceType":3,"addr":"127.0.0.1","port":60004,"online":3,"runStatus":null,"deviceId":4,"isVisibleFlag":1,"createdBy":1,"updatedBy":1,"createdTime":"2025-09-02T09:43:30","updatedTime":"2025-09-02T17:30:57","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"S02","deviceName":"接驳点2摄像机","deviceType":3,"addr":null,"port":null,"online":null,"runStatus":null,"deviceId":5,"isVisibleFlag":1,"createdBy":1,"updatedBy":null,"createdTime":"2025-09-02T09:44:09","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"S03","deviceName":"接驳点3摄像机","deviceType":3,"addr":null,"port":null,"online":null,"runStatus":null,"deviceId":6,"isVisibleFlag":1,"createdBy":1,"updatedBy":null,"createdTime":"2025-09-02T09:44:26","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-09-03 15:13:41.0766133 +08:00 星期三 L System.Logging.LoggingMonitor[0] #13
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsDeviceController.Online
      ┣ 
      ┣ 控制器名称：                     WcsDeviceController
      ┣ 操作名称：                       Online
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsDevice; [action]: Online
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/device/Online/4
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-ea64f3211ef19bb68df5e1ba6f154ca8-63ad9cb9894da05c-00
      ┣ 服务线程 ID：                    #10
      ┣ 执行耗时：                       24ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ 
      ┣ login_user_key (string)：        77f870a8-c6b0-4952-8f36-48fd72e59af3
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1757488414 (2025-09-10 15:13:34:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756883614 (2025-09-03 15:13:34:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=K5TLGxYDybZ9DmGkkcSkmzepgPccoFrbkTxmLEYxjh40SCddr1d026lLlHzGfAm/7NTwvqtod3Kwmpa3HBsjxg==; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6Ijc3Zjg3MGE4LWM2YjAtNDk1Mi04ZjM2LTQ4ZmQ3MmU1OWFmMyIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTc0ODg0MTQsImlhdCI6MTc1Njg4MzYxNCwibmJmIjoxNzU2ODgzNjE0LCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.1M7cOzsHMpSnTo9gsXvYa2qHXuyD2LazAIQzk2dZMw0
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ deviceId (Int64)：               4
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"设备连接成功","code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━

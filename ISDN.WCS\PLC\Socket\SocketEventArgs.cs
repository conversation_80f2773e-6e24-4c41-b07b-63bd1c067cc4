using System;

namespace ISDN.WCS.PLC.Socket;

/// <summary>
/// Socket数据接收事件参数
/// </summary>
public class SocketDataReceivedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 接收到的数据
    /// </summary>
    public string Data { get; set; }
}

/// <summary>
/// Socket客户端连接事件参数
/// </summary>
public class SocketClientConnectedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 客户端IP地址
    /// </summary>
    public string IpAddress { get; set; }

    /// <summary>
    /// 客户端端口号
    /// </summary>
    public int Port { get; set; }
}

/// <summary>
/// Socket客户端断开连接事件参数
/// </summary>
public class SocketClientDisconnectedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }
}
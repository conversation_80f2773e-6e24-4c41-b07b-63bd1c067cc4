using ISDN.WCS.Data.Dtos;
using ISDN.WCS.PLC.Dto;
using ISDN.WCS.PLC.SocketService;

//using ISDN.Data.Dtos;
using ISDN.WCS.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Enums;
using RuoYi.Common.Utils;
using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;
using RuoYi.Framework;
using RuoYi.Framework.Extensions;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace ISDN.WCS.Controllers
{
    /// <summary>
    /// WCS任务档
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    [ApiDescriptionSettings("Test")]
    [Route("test")]
    public class TestController : ControllerBase
    {
        private readonly ILogger<TestController> _logger;

        public TestController(ILogger<TestController> logger,
            WcsTaskService wcsTaskService)
        {
            _logger = logger;
        }

        [HttpPost("CreateWcsTaskTest")]
        //[AppAuthorize("wcs:task:CreateWcsTaskTest")]
        public async Task<ResultDto> CreateWcsTaskTest(WmsTaskDto dto)
        {
            WcsToPlc wcsToPlc = new WcsToPlc();
            var result = await wcsToPlc.DispatchTaskAsync(dto);

            return result;
        }
    }

}

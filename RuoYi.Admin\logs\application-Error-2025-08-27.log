fail: 2025-08-27 14:06:15.5710183 +08:00 星期三 L System.Logging.LoggingMonitor[0] #29
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-714ce50fb17c0f4f02c511d20866ab51-dbfaf07aa7ecb665-00
      ┣ 服务线程 ID：                  #31
      ┣ 执行耗时：                     3320ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"admin123","code":"0","uuid":"cb89ab36-fda6-4463-9c24-db1b3618fcd0"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     
      ┣ 最终返回值：                   null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                         RuoYi.Framework.Exceptions.ServiceException
      ┣ 消息：                         用户不存在/密码错误
      ┣ 错误堆栈：                     at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method49(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method49(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-27 14:06:15.6142336 +08:00 星期三 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #29
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method49(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++

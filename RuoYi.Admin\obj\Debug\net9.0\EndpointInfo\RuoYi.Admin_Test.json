{"openapi": "3.0.4", "info": {"title": "Test", "version": "1.0.0"}, "paths": {"/test/CreateWcsTaskTest": {"post": {"tags": ["Test"], "operationId": "test-CreateWcsTaskTest-Post", "parameters": [{"name": "BinNo", "in": "query", "schema": {"type": "string"}}, {"name": "TaskType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartLocation", "in": "query", "schema": {"type": "string"}}, {"name": "EndLocation", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResultDto"}}}}}}}}, "components": {"schemas": {"ResultDto": {"type": "object", "properties": {"serverTag": {"type": "string", "nullable": true}, "result": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"additionalProperties": false, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}
using Mapster;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Data;
using RuoYi.Framework.DependencyInjection;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.Repositories;
using System.Data;
using System.Threading.Tasks;

namespace ISDN.WCS.Services
{
    /// <summary>
    ///  WCS任务档 Service
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    public class WcsTaskService : BaseService<WcsTask, WcsTaskDto>, ITransient
    {
        private readonly ILogger<WcsTaskService> _logger;
        private readonly WcsTaskRepository _wcsTaskRepository;

        public WcsTaskService(ILogger<WcsTaskService> logger,
            WcsTaskRepository wcsTaskRepository)
        {
            BaseRepo = wcsTaskRepository;

            _logger = logger;
            _wcsTaskRepository = wcsTaskRepository;
        }

        /// <summary>
        /// 查询 WCS任务档 详情
        /// </summary>
        public async Task<WcsTask> GetAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.TaskId == id);
            return entity;
        }

        /// <summary>
        /// 查询 WCS任务档 详情
        /// </summary>
        public async Task<WcsTaskDto> GetDtoAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.TaskId == id);
            var dto = entity.Adapt<WcsTaskDto>();
            // TODO 填充关联表数据
            return dto;
        }


        /// <summary>
        /// 查询 WCS任务档 详情
        /// </summary>
        public  async Task<DataTable> GetTaskStepList(long id)
        {
            var list=await _wcsTaskRepository.Repo.Context.Ado.GetDataTableAsync("select * from wcs_task_step where task_id = @id", new { id });

            // TODO 填充关联表数据
            return list;
        }
    }
}

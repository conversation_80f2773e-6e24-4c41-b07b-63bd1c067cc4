fail: 2025-08-28 11:19:48.7367187 +08:00 星期四 L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #14
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - ISDN.WCS.Controllers.WcsTaskController.CreateWcsTaskTest (ISDN.WCS). See inner exception
       ---> System.Reflection.AmbiguousMatchException: Ambiguous match found for 'ISDN.WCS.Controllers.WmsTaskDto Int32 BinNo'.
         at System.RuntimeType.GetPropertyImpl(String name, BindingFlags bindingAttr, Binder binder, Type returnType, Type[] types, ParameterModifier[] modifiers)
         at System.Linq.Enumerable.ListWhereIterator`1.MoveNext()
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRespository, Func`3 parameterGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRespository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
         --- End of inner exception stack trace ---
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 11:20:04.8627105 +08:00 星期四 L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #13
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - ISDN.WCS.Controllers.WcsTaskController.GetWcsTaskPagedList (ISDN.WCS). See inner exception
       ---> System.Reflection.AmbiguousMatchException: Ambiguous match found for 'ISDN.WCS.Data.Dtos.WcsTaskDto Int32 BinNo'.
         at System.RuntimeType.GetPropertyImpl(String name, BindingFlags bindingAttr, Binder binder, Type returnType, Type[] types, ParameterModifier[] modifiers)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRespository, Func`3 parameterGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRespository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
         --- End of inner exception stack trace ---
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 11:20:23.6257194 +08:00 星期四 L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #14
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Failed to generate Operation for action - ISDN.WCS.Controllers.WcsTaskController.GetWcsTaskPagedList (ISDN.WCS). See inner exception
       ---> System.Reflection.AmbiguousMatchException: Ambiguous match found for 'ISDN.WCS.Data.Dtos.WcsTaskDto Int32 BinNo'.
         at System.RuntimeType.GetPropertyImpl(String name, BindingFlags bindingAttr, Binder binder, Type returnType, Type[] types, ParameterModifier[] modifiers)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRespository, Func`3 parameterGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateParametersAsync(ApiDescription apiDescription, SchemaRepository schemaRespository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
         --- End of inner exception stack trace ---
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository, Func`3 parametersGenerator, Func`3 bodyGenerator, Func`3 applyFilters)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationAsync(OpenApiDocument document, ApiDescription apiDescription, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperationsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository, Func`4 operationsGenerator)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePathsAsync(OpenApiDocument document, IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:33:59.7450945 +08:00 星期四 L Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware[1] #29
      An unhandled exception has occurred while executing the request.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.ArgumentOutOfRangeException: Token 2007ff9 is not valid in the scope of module System.ModuleHandle. (Parameter 'typeToken')
         at System.ModuleHandle.ResolveTypeHandle(Int32 typeToken, RuntimeTypeHandle[] typeInstantiationContext, RuntimeTypeHandle[] methodInstantiationContext)
         at System.Reflection.CustomAttribute.FilterCustomAttributeRecord(MetadataToken caCtorToken, MetadataImport& scope, RuntimeModule decoratedModule, MetadataToken decoratedToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1& derivedAttributes, RuntimeType& attributeType, IRuntimeMethodInfo& ctorWithParameters, Boolean& isVarArg)
         at System.Reflection.CustomAttribute.AddCustomAttributes(ListBuilder`1& attributes, RuntimeModule decoratedModule, Int32 decoratedMetadataToken, RuntimeType attributeFilterType, Boolean mustBeInheritable, ListBuilder`1 derivedAttributes)
         at System.Attribute.InternalGetCustomAttributes(PropertyInfo element, Type type, Boolean inherit)
         at Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.DefaultModelMetadataProvider.CreateSinglePropertyDetails(ModelMetadataIdentity propertyKey, PropertyHelper propertyHelper)
         at Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.DefaultModelMetadataProvider.CreatePropertyDetails(ModelMetadataIdentity key)
         at Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.DefaultModelMetadataProvider.GetMetadataForProperties(Type modelType)
         at Microsoft.AspNetCore.Mvc.ModelBinding.Metadata.DefaultModelMetadata.get_Properties()
         at Microsoft.AspNetCore.Mvc.ApiExplorer.DefaultApiDescriptionProvider.PseudoModelBindingVisitor.Visit(ApiParameterDescriptionContext bindingContext, BindingSource ambientSource, String containerName)
         at Microsoft.AspNetCore.Mvc.ApiExplorer.DefaultApiDescriptionProvider.GetParameters(ApiParameterContext context)
         at Microsoft.AspNetCore.Mvc.ApiExplorer.DefaultApiDescriptionProvider.CreateApiDescription(ControllerActionDescriptor action, String httpMethod, String groupName)
         at Microsoft.AspNetCore.Mvc.ApiExplorer.DefaultApiDescriptionProvider.OnProvidersExecuting(ApiDescriptionProviderContext context)
         at Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionGroupCollectionProvider.GetCollection(ActionDescriptorCollection actionDescriptors)
         at Microsoft.AspNetCore.Mvc.ApiExplorer.ApiDescriptionGroupCollectionProvider.get_ApiDescriptionGroups()
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutPaths(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
         at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
         at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
         at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:39:17.7421957 +08:00 星期四 L System.Logging.LoggingMonitor[0] #11
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-728b545491312a40270177989f695808-04ff60d3a03b86e2-00
      ┣ 服务线程 ID：                  #28
      ┣ 执行耗时：                     10874ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"admin123","code":"9","uuid":"53c3a51e-49f7-4484-a4c2-6bda2e097133"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     
      ┣ 最终返回值：                   null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                         RuoYi.Framework.Exceptions.ServiceException
      ┣ 消息：                         用户不存在/密码错误
      ┣ 错误堆栈：                     at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:39:17.7465070 +08:00 星期四 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #11
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:39:21.1910224 +08:00 星期四 L System.Logging.LoggingMonitor[0] #28
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-ac8cbf51ac82776f003ca1f99687b5cc-c9397b42a3b8c969-00
      ┣ 服务线程 ID：                  #28
      ┣ 执行耗时：                     1499ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"admin123","code":"9","uuid":"c07fc0a1-a8f8-443c-8058-d01eb30439b3"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     
      ┣ 最终返回值：                   null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                         RuoYi.Framework.Exceptions.ServiceException
      ┣ 消息：                         验证码错误或已失效
      ┣ 错误堆栈：                     at RuoYi.System.Services.SysLoginService.ValidateCaptcha(String username, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 110
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 50
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 验证码错误或已失效
         at RuoYi.System.Services.SysLoginService.ValidateCaptcha(String username, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 110
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 50
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:39:21.1939462 +08:00 星期四 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #28
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 验证码错误或已失效
         at RuoYi.System.Services.SysLoginService.ValidateCaptcha(String username, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 110
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 50
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:39:32.1993976 +08:00 星期四 L System.Logging.LoggingMonitor[0] #28
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-141e73aa90dc7ddb1e369e8992722a18-684a8d7be2836304-00
      ┣ 服务线程 ID：                  #28
      ┣ 执行耗时：                     6470ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"admin123","code":"6","uuid":"78414b9e-e34c-4414-af54-02b908dd72dd"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     
      ┣ 最终返回值：                   null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                         RuoYi.Framework.Exceptions.ServiceException
      ┣ 消息：                         用户不存在/密码错误
      ┣ 错误堆栈：                     at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:39:32.2022269 +08:00 星期四 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #28
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:41:01.1872267 +08:00 星期四 L System.Logging.LoggingMonitor[0] #3
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-80a605d67aad92ebfaedb6ef6f7a1c2f-56e2a61f5e5a4cd4-00
      ┣ 服务线程 ID：                  #3
      ┣ 执行耗时：                     4913ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"admin123","code":"1","uuid":"3d2c7043-0968-445c-bf16-39a85463c1e1"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     
      ┣ 最终返回值：                   null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                         RuoYi.Framework.Exceptions.ServiceException
      ┣ 消息：                         用户不存在/密码错误
      ┣ 错误堆栈：                     at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-08-28 14:41:01.1934916 +08:00 星期四 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #3
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      RuoYi.Framework.Exceptions.ServiceException: 用户不存在/密码错误
         at RuoYi.System.Services.SysPasswordService.Validate(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysPasswordService.cs:line 65
         at RuoYi.System.Services.SysLoginService.CheckLoginUser(String username, String password, SysUserDto user) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 88
         at RuoYi.System.Services.SysLoginService.LoginAsync(String username, String password, String code, String uuid) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysLoginService.cs:line 55
         at RuoYi.Admin.SysLoginController.Login(LoginBody loginBody) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Controllers\SysLoginController.cs:line 45
         at lambda_method95(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++

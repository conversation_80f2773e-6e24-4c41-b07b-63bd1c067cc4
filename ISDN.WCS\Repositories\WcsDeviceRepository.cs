using RuoYi.Data;
using RuoYi.Common.Data;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using SqlSugar;

namespace ISDN.WCS.Repositories
{
    /// <summary>
    ///  设备管理 Repository
    ///  author zgq
    ///  date   2025-08-13 16:36:40
    /// </summary>
public class WcsDeviceRepository : BaseRepository<WcsDevice, WcsDeviceDto>
    {
    public WcsDeviceRepository(ISqlSugarRepository<WcsDevice> sqlSugarRepository)
        {
        Repo = sqlSugarRepository;
        }

        /// <summary>
        /// 构造条件查询器
        /// </summary>
        /// <param name="dto">domain</param>
        /// <returns></returns>
        public override ISugarQueryable<WcsDevice> Queryable(WcsDeviceDto dto)
            {
            return Repo.AsQueryable()
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.DeviceCode), (t) => t.DeviceCode.Contains(dto.DeviceCode))
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.DeviceName), (t) => t.DeviceName.Contains(dto.DeviceName))
                            .WhereIF(dto.DeviceType > 0, (t) => t.DeviceType == dto.DeviceType)
                            .WhereIF(dto.Online > 0, (t) => t.Online == dto.Online)
                            .WhereIF(dto.RunStatus > 0, (t) => t.RunStatus == dto.RunStatus)
                            .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
;
            }

            /// <summary>
            /// 构造条件查询器
            /// </summary>
            /// <param name="dto">domain</param>
            /// <returns></returns>
            public override ISugarQueryable<WcsDeviceDto> DtoQueryable(WcsDeviceDto dto)
                {
                return Repo.AsQueryable()
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.DeviceCode), (t) => t.DeviceCode.Contains(dto.DeviceCode))
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.DeviceName), (t) => t.DeviceName.Contains(dto.DeviceName))
                            .WhereIF(dto.DeviceType > 0, (t) => t.DeviceType == dto.DeviceType)
                            .WhereIF(dto.Online > 0, (t) => t.Online == dto.Online)
                            .WhereIF(dto.RunStatus > 0, (t) => t.RunStatus == dto.RunStatus)
                            .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
                .Select((t) => new WcsDeviceDto
                {
                     DeviceId = t.DeviceId
                }, true);
        }
    }
}

{"openapi": "3.0.4", "info": {"title": "Generator", "version": "1.0.0"}, "paths": {"/tool/Gen/list": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-list-Get", "parameters": [{"name": "TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "TableName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "TableComment", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTableName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTableFkName", "in": "query", "schema": {"type": "string"}}, {"name": "ClassName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "TplWebType", "in": "query", "schema": {"type": "string"}}, {"name": "TplCategory", "in": "query", "schema": {"type": "string"}}, {"name": "PackageName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "ModuleName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "BusinessName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "FunctionName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "FunctionAuthor", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "GenType", "in": "query", "schema": {"type": "string"}}, {"name": "GenPath", "in": "query", "schema": {"type": "string"}}, {"name": "Options", "in": "query", "schema": {"type": "string"}}, {"name": "TreeCode", "in": "query", "schema": {"type": "string"}}, {"name": "TreeParentCode", "in": "query", "schema": {"type": "string"}}, {"name": "TreeName", "in": "query", "schema": {"type": "string"}}, {"name": "ParentMenuId", "in": "query", "schema": {"type": "string"}}, {"name": "ParentMenuName", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.ColumnId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "PkColumn.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "PkColumn.ColumnName", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.ColumnComment", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.ColumnType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.NetType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.NetField", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsPk", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsIncrement", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsRequired", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsInsert", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsEdit", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsList", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsQuery", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.QueryType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.HtmlType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.DictType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.Sort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PkColumn.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.TableName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.TableComment", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTableName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTableFkName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.ClassName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.TplWebType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TplCategory", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PackageName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.ModuleName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.BusinessName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.FunctionName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.FunctionAuthor", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.GenType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.GenPath", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.Options", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TreeCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TreeParentCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TreeName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.ParentMenuId", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.ParentMenuName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.ColumnId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.PkColumn.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.PkColumn.ColumnName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.ColumnComment", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.ColumnType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.NetType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.NetField", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsPk", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsIncrement", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsRequired", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsInsert", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsEdit", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsList", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsQuery", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.QueryType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.HtmlType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.DictType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.Sort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SubTable.PkColumn.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.SubTable.TableName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TableComment", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.SubTableName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.SubTableFkName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ClassName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TplWebType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TplCategory", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PackageName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ModuleName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.BusinessName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.FunctionName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.FunctionAuthor", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.GenType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.GenPath", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.Options", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TreeCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TreeParentCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TreeName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ParentMenuId", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ParentMenuName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.ColumnId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.SubTable.PkColumn.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.SubTable.PkColumn.ColumnName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.ColumnComment", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.ColumnType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.NetType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.NetField", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsPk", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsIncrement", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsRequired", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsInsert", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsEdit", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsList", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsQuery", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.QueryType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.HtmlType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.DictType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.Sort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SubTable.SubTable.PkColumn.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.SubTable", "in": "query", "schema": {"$ref": "#/components/schemas/GenTableDto"}}, {"name": "SubTable.SubTable.Columns", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumnDto"}}}, {"name": "SubTable.SubTable.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.Columns", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumnDto"}}}, {"name": "SubTable.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "Columns", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumnDto"}}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTable"}}}}}}}, "/tool/Gen/{tableId}": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-tableId-Get", "parameters": [{"name": "tableId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/tool/Gen/db/list": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-db-list-Get", "parameters": [{"name": "TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "TableName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "TableComment", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTableName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTableFkName", "in": "query", "schema": {"type": "string"}}, {"name": "ClassName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "TplWebType", "in": "query", "schema": {"type": "string"}}, {"name": "TplCategory", "in": "query", "schema": {"type": "string"}}, {"name": "PackageName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "ModuleName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "BusinessName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "FunctionName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "FunctionAuthor", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "GenType", "in": "query", "schema": {"type": "string"}}, {"name": "GenPath", "in": "query", "schema": {"type": "string"}}, {"name": "Options", "in": "query", "schema": {"type": "string"}}, {"name": "TreeCode", "in": "query", "schema": {"type": "string"}}, {"name": "TreeParentCode", "in": "query", "schema": {"type": "string"}}, {"name": "TreeName", "in": "query", "schema": {"type": "string"}}, {"name": "ParentMenuId", "in": "query", "schema": {"type": "string"}}, {"name": "ParentMenuName", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.ColumnId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "PkColumn.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "PkColumn.ColumnName", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.ColumnComment", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.ColumnType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.NetType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.NetField", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsPk", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsIncrement", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsRequired", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsInsert", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsEdit", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsList", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.IsQuery", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.QueryType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.HtmlType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.DictType", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.Sort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PkColumn.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "PkColumn.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PkColumn.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.TableName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.TableComment", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTableName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTableFkName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.ClassName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.TplWebType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TplCategory", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PackageName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.ModuleName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.BusinessName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.FunctionName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.FunctionAuthor", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.GenType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.GenPath", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.Options", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TreeCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TreeParentCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.TreeName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.ParentMenuId", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.ParentMenuName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.ColumnId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.PkColumn.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.PkColumn.ColumnName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.ColumnComment", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.ColumnType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.NetType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.NetField", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsPk", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsIncrement", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsRequired", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsInsert", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsEdit", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsList", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.IsQuery", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.QueryType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.HtmlType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.DictType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.Sort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SubTable.PkColumn.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.PkColumn.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.PkColumn.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.SubTable.TableName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TableComment", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.SubTableName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.SubTableFkName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ClassName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TplWebType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TplCategory", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PackageName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ModuleName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.BusinessName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.FunctionName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.FunctionAuthor", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "SubTable.SubTable.GenType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.GenPath", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.Options", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TreeCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TreeParentCode", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.TreeName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ParentMenuId", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.ParentMenuName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.ColumnId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.SubTable.PkColumn.TableId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "SubTable.SubTable.PkColumn.ColumnName", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.ColumnComment", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.ColumnType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.NetType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.NetField", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsPk", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsIncrement", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsRequired", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsInsert", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsEdit", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsList", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.IsQuery", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.QueryType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.HtmlType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.DictType", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.Sort", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SubTable.SubTable.PkColumn.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.PkColumn.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.PkColumn.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.SubTable", "in": "query", "schema": {"$ref": "#/components/schemas/GenTableDto"}}, {"name": "SubTable.SubTable.Columns", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumnDto"}}}, {"name": "SubTable.SubTable.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.SubTable.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.SubTable.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.Columns", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumnDto"}}}, {"name": "SubTable.CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.Remark", "in": "query", "schema": {"type": "string"}}, {"name": "SubTable.Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SubTable.Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}, {"name": "Columns", "in": "query", "schema": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumnDto"}}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTable"}}}}}}}, "/tool/Gen/column/{tableId}": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-column-tableId-Get", "parameters": [{"name": "tableId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTableColumn"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTableColumn"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_GenTableColumn"}}}}}}}, "/tool/Gen/importTable": {"post": {"tags": ["Gen"], "operationId": "tool-Gen-importTable-Post", "parameters": [{"name": "tables", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/tool/Gen": {"put": {"tags": ["Gen"], "operationId": "tool-Gen-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GenTableDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GenTableDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GenTableDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GenTableDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/tool/Gen/{tableIds}": {"delete": {"tags": ["Gen"], "operationId": "tool-Gen-tableIds-Delete", "parameters": [{"name": "tableIds", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/tool/Gen/preview/{tableId}": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-preview-tableId-Get", "parameters": [{"name": "tableId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/tool/Gen/genCode/{tableName}": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-genCode-tableName-Get", "parameters": [{"name": "tableName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/tool/Gen/batchGenCode": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-batchGenCode-Get", "parameters": [{"name": "tables", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/tool/Gen/synchDb/{tableName}": {"get": {"tags": ["Gen"], "operationId": "tool-Gen-synchDb-tableName-Get", "parameters": [{"name": "tableName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}}, "components": {"schemas": {"GenTable": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "tableId": {"type": "integer", "format": "int64"}, "tableName": {"type": "string", "nullable": true}, "tableComment": {"type": "string", "nullable": true}, "subTableName": {"type": "string", "nullable": true}, "subTableFkName": {"type": "string", "nullable": true}, "className": {"type": "string", "nullable": true}, "tplWebType": {"type": "string", "nullable": true}, "tplCategory": {"type": "string", "nullable": true}, "packageName": {"type": "string", "nullable": true}, "moduleName": {"type": "string", "nullable": true}, "businessName": {"type": "string", "nullable": true}, "functionName": {"type": "string", "nullable": true}, "functionAuthor": {"type": "string", "nullable": true}, "genType": {"type": "string", "nullable": true}, "genPath": {"type": "string", "nullable": true}, "options": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "columns": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumn"}, "nullable": true}, "pkColumn": {"$ref": "#/components/schemas/GenTableColumn"}, "subTable": {"$ref": "#/components/schemas/GenTable"}}, "additionalProperties": false}, "GenTableColumn": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "columnId": {"type": "integer", "format": "int64"}, "tableId": {"type": "integer", "format": "int64", "nullable": true}, "columnName": {"type": "string", "nullable": true}, "columnComment": {"type": "string", "nullable": true}, "columnType": {"type": "string", "nullable": true}, "netType": {"type": "string", "nullable": true}, "netField": {"type": "string", "nullable": true}, "isPk": {"type": "string", "nullable": true}, "isIncrement": {"type": "string", "nullable": true}, "isRequired": {"type": "string", "nullable": true}, "isInsert": {"type": "string", "nullable": true}, "isEdit": {"type": "string", "nullable": true}, "isList": {"type": "string", "nullable": true}, "isQuery": {"type": "string", "nullable": true}, "queryType": {"type": "string", "nullable": true}, "htmlType": {"type": "string", "nullable": true}, "dictType": {"type": "string", "nullable": true}, "sort": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GenTableColumnDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "columnId": {"type": "integer", "format": "int64"}, "tableId": {"type": "integer", "format": "int64", "nullable": true}, "columnName": {"type": "string", "nullable": true}, "columnComment": {"type": "string", "nullable": true}, "columnType": {"type": "string", "nullable": true}, "netType": {"type": "string", "nullable": true}, "netField": {"type": "string", "nullable": true}, "isPk": {"type": "string", "nullable": true}, "isIncrement": {"type": "string", "nullable": true}, "isRequired": {"type": "string", "nullable": true}, "isInsert": {"type": "string", "nullable": true}, "isEdit": {"type": "string", "nullable": true}, "isList": {"type": "string", "nullable": true}, "isQuery": {"type": "string", "nullable": true}, "queryType": {"type": "string", "nullable": true}, "htmlType": {"type": "string", "nullable": true}, "dictType": {"type": "string", "nullable": true}, "sort": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "GenTableDto": {"required": ["businessName", "className", "function<PERSON><PERSON>or", "functionName", "moduleName", "packageName", "tableComment", "tableName"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "tableId": {"type": "integer", "format": "int64"}, "tableName": {"minLength": 1, "type": "string"}, "tableComment": {"minLength": 1, "type": "string"}, "subTableName": {"type": "string", "nullable": true}, "subTableFkName": {"type": "string", "nullable": true}, "className": {"minLength": 1, "type": "string"}, "tplWebType": {"type": "string", "nullable": true}, "tplCategory": {"type": "string", "nullable": true}, "packageName": {"minLength": 1, "type": "string"}, "moduleName": {"minLength": 1, "type": "string"}, "businessName": {"minLength": 1, "type": "string"}, "functionName": {"minLength": 1, "type": "string"}, "functionAuthor": {"minLength": 1, "type": "string"}, "genType": {"type": "string", "nullable": true}, "genPath": {"type": "string", "nullable": true}, "options": {"type": "string", "nullable": true}, "treeCode": {"type": "string", "nullable": true}, "treeParentCode": {"type": "string", "nullable": true}, "treeName": {"type": "string", "nullable": true}, "parentMenuId": {"type": "string", "nullable": true}, "parentMenuName": {"type": "string", "nullable": true}, "pkColumn": {"$ref": "#/components/schemas/GenTableColumnDto"}, "subTable": {"$ref": "#/components/schemas/GenTableDto"}, "columns": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumnDto"}, "nullable": true}}, "additionalProperties": false}, "QueryParam": {"type": "object", "properties": {"beginTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "dataScopeSql": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SqlSugarPagedList_GenTable": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/GenTable"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_GenTableColumn": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/GenTableColumn"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}
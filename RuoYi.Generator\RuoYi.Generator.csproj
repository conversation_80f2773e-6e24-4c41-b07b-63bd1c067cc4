<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="Services\IGenTableService.cs"/>
    </ItemGroup>

    <ItemGroup>
        <None Remove="gensettings.json"/>
        <None Update="StaticFiles\Vm\Vue\Vue3\index-tree.vue.cshtml">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Vue\Vue3\index.vue.cshtml">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Vue\Vue2\index-tree.vue.cshtml">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Vue\Vue2\index.vue.cshtml">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Content Include="gensettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="RazorEngineCore" Version="2024.4.1" />
        <PackageReference Include="SharpZipLib" Version="1.4.2" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\RuoYi.Common\RuoYi.Common.csproj" />
        <ProjectReference Include="..\RuoYi.Data\RuoYi.Data.csproj" />
        <ProjectReference Include="..\RuoYi.Framework\RuoYi.Framework.csproj" />
        <ProjectReference Include="..\RuoYi.System\RuoYi.System.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="StaticFiles\Vm\Js\api.js.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Net\Controller.cs.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Net\Dto.cs.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Net\SubDto.cs.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Net\SubEntity.cs.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Net\Entity.cs.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Net\Repository.cs.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Net\Service.cs.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Sql\mysql.sql.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="StaticFiles\Vm\Sql\sqlserver.sql.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>

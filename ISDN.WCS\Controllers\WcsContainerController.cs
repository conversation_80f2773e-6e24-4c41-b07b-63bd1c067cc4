using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Enums;
using RuoYi.Common.Utils;
using RuoYi.Framework;
using RuoYi.Framework.Extensions;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
//using ISDN.Data.Dtos;
using ISDN.WCS.Services;
using ISDN.WCS.Data.Dtos;

namespace ISDN.WCS.Controllers
{
    /// <summary>
    /// 容器档
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    [ApiDescriptionSettings("Wcs")]
    [Route("wcs/container")]
    public class WcsContainerController : ControllerBase
    {
        private readonly ILogger<WcsContainerController> _logger;
        private readonly WcsContainerService _wcsContainerService;

        public WcsContainerController(ILogger<WcsContainerController> logger,
            WcsContainerService wcsContainerService)
        {
            _logger = logger;
            _wcsContainerService = wcsContainerService;
        }

        /// <summary>
        /// 查询容器档列表
        /// </summary>
        [HttpGet("list")]
        [AppAuthorize("wcs:container:list")]
        public async Task<SqlSugarPagedList<WcsContainerDto>> GetWcsContainerPagedList([FromQuery] WcsContainerDto dto)
        {
           return await _wcsContainerService.GetDtoPagedListAsync(dto);
        }

        /// <summary>
        /// 获取 容器档 详细信息
        /// </summary>
        [HttpGet("")]
        [HttpGet("{id}")]
        [AppAuthorize("wcs:container:query")]
        public async Task<AjaxResult> Get(long id)
        {
            var data = await _wcsContainerService.GetDtoAsync(id);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 新增 容器档
        /// </summary>
        [HttpPost("")]
        [AppAuthorize("wcs:container:add")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "容器档", BusinessType = BusinessType.INSERT)]
        public async Task<AjaxResult> Add([FromBody] WcsContainerDto dto)
        {
            var data = await _wcsContainerService.InsertAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 修改 容器档
        /// </summary>
        [HttpPut("")]
        [AppAuthorize("wcs:container:edit")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "容器档", BusinessType = BusinessType.UPDATE)]
        public async Task<AjaxResult> Edit([FromBody] WcsContainerDto dto)
        {
            var data = await _wcsContainerService.UpdateAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 删除 容器档
        /// </summary>
        [HttpDelete("{ids}")]
        [AppAuthorize("wcs:container:remove")]
        [RuoYi.System.Log(Title = "容器档", BusinessType = BusinessType.DELETE)]
        public async Task<AjaxResult> Remove(string ids)
        {
            var idList = ids.SplitToList<long>();
            var data = await _wcsContainerService.DeleteAsync(idList);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 导入 容器档
        /// </summary>
        [HttpPost("import")]
        [AppAuthorize("wcs:container:import")]
        [RuoYi.System.Log(Title = "容器档", BusinessType = BusinessType.IMPORT)]
        public async Task Import([Required] IFormFile file)
        {
            var stream = new MemoryStream();
            file.CopyTo(stream);
            var list = await ExcelUtils.ImportAsync<WcsContainerDto>(stream);
            await _wcsContainerService.ImportDtoBatchAsync(list);
        }

        /// <summary>
        /// 导出 容器档
        /// </summary>
        [HttpPost("export")]
        [AppAuthorize("wcs:container:export")]
        [RuoYi.System.Log(Title = "容器档", BusinessType = BusinessType.EXPORT)]
        public async Task Export(WcsContainerDto dto)
        {
            var list = await _wcsContainerService.GetDtoListAsync(dto);
            await ExcelUtils.ExportAsync(App.HttpContext.Response, list);
        }
    }
}

using ISDN.WCS.PLC.SocketService;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace ISDN.WCS.PLC.Socket;

/// <summary>
/// PLC服务端Socket
/// 用于监听PLC的连接请求和接收数据
/// </summary>
public class PLCServerSocket : IDisposable
{
    private TcpListener _tcpListener;
    private bool _isRunning;
    private int _port;
    private Dictionary<string, ClientInfo> _clients;
    private object _lock = new object();
    private Timer _heartbeatTimer;
    private const int HEARTBEAT_INTERVAL_MS = 30000; // 心跳检测间隔，30秒
    private const int CLIENT_TIMEOUT_MS = 60000; // 客户端超时时间，60秒

    private readonly PlcToWcs _plcToWcs;

    /// <summary>
    /// 客户端连接事件
    /// </summary>
    public event EventHandler<SocketClientConnectedEventArgs> ClientConnected;

    /// <summary>
    /// 客户端断开连接事件
    /// </summary>
    public event EventHandler<SocketClientDisconnectedEventArgs> ClientDisconnected;

    /// <summary>
    /// 客户端信息类
    /// 存储客户端连接信息及最后活动时间
    /// </summary>
    private class ClientInfo
    {
        public TcpClient Client { get; set; }
        public string IpAddress { get; set; }
        public int Port { get; set; }
        public DateTime LastActiveTime { get; set; }
        public bool IsHeartbeatEnabled { get; set; } = true;
    }

    ///// <summary>
    ///// 数据处理委托
    ///// </summary>
    ///// <param name="clientId">客户端ID</param>
    ///// <param name="data">数据</param>
    //public delegate void DataHandler(string clientId, string data);

    ///// <summary>
    ///// 数据处理器字典
    ///// 用于根据数据类型路由到不同的处理方法
    ///// </summary>
    //public Dictionary<string, DataHandler> DataHandlers { get; private set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="port">监听端口号</param>
    /// <param name="plcToWcs">PLC到WCS的数据处理器</param>
    public PLCServerSocket(int port, PlcToWcs plcToWcs = null)
    {
        _port = port;
        _clients = new Dictionary<string, ClientInfo>();
        _tcpListener = new TcpListener(IPAddress.Any, port);
        _isRunning = false;
        _plcToWcs = plcToWcs;
    }

    /// <summary>
    /// 启动服务端Socket
    /// </summary>
    public void Start()
    {
        try
        {
            if (!_isRunning)
            {
                _tcpListener.Start();
                _isRunning = true;
                Console.WriteLine($"PLC服务端Socket已启动，监听端口: {_port}");

                // 启动异步接受客户端连接
                Task.Run(() => AcceptClientsAsync());

                // 启动心跳检测定时器
                _heartbeatTimer = new Timer(CheckHeartbeat, null, HEARTBEAT_INTERVAL_MS, HEARTBEAT_INTERVAL_MS);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"启动PLC服务端Socket失败: {ex.Message}");
            _isRunning = false;
        }
    }

    /// <summary>
    /// 停止服务端Socket
    /// </summary>
    public void Stop()
    {
        try
        {
            if (_isRunning)
            {
                _isRunning = false;
                // 关闭所有客户端连接
                lock (_lock)
                {
                    foreach (var client in _clients.Values)
                    {
                        if (client.Client != null && client.Client.Connected)
                        {
                            client.Client.Close();
                        }
                    }
                    _clients.Clear();
                }
                _tcpListener.Stop();
                Console.WriteLine("PLC服务端Socket已停止");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"停止PLC服务端Socket失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 异步接受客户端连接
    /// </summary>
    private async Task AcceptClientsAsync()
    {
        while (_isRunning)
        {
            try
            {
                TcpClient client = await _tcpListener.AcceptTcpClientAsync();
                string clientId = GetClientId(client);
                IPEndPoint endPoint = client.Client.RemoteEndPoint as IPEndPoint;

                lock (_lock)
                {
                    _clients[clientId] = new ClientInfo
                    {
                        Client = client,
                        IpAddress = endPoint?.Address.ToString(),
                        Port = endPoint?.Port ?? 0,
                        LastActiveTime = DateTime.Now
                    };
                }

                // 触发客户端连接事件
                OnClientConnected(clientId, client);

                // 开始接收该客户端的数据
                Task.Run(() => ReceiveDataAsync(client, clientId));
            }
            catch (Exception ex)
            {
                if (_isRunning)
                {
                    Console.WriteLine($"接受客户端连接失败: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// 异步接收客户端数据
    /// </summary>
    /// <param name="client">客户端TcpClient</param>
    /// <param name="clientId">客户端ID</param>
    private async Task ReceiveDataAsync(TcpClient client, string clientId)
    {
        try
        {
            NetworkStream stream = client.GetStream();
            byte[] buffer = new byte[4096];

            while (_isRunning && client.Connected)
            {
                int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                if (bytesRead == 0)
                {
                    // 客户端断开连接
                    break;
                }

                string data = Encoding.UTF8.GetString(buffer, 0, bytesRead);

                // 更新客户端最后活动时间
                lock (_lock)
                {
                    if (_clients.TryGetValue(clientId, out ClientInfo clientInfo))
                    {
                        clientInfo.LastActiveTime = DateTime.Now;
                    }
                }

                // 处理数据
                if (_plcToWcs != null)
                {
                    await _plcToWcs.ProcessReceivedData(clientId, data);
                }
                else
                {
                    Console.WriteLine("未设置数据处理器，接收到的数据: " + data);
                }
            }
        }
        catch (Exception ex)
        {
            if (_isRunning)
            {
                Console.WriteLine($"接收客户端数据失败: {ex.Message}");
            }
        }
        finally
        {
            // 客户端断开连接，清理资源
            DisconnectClient(clientId);
        }
    }

    /// <summary>
    /// 向指定客户端发送数据
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="data">要发送的数据</param>
    /// <returns>是否发送成功</returns>
    public bool SendData(string clientId, string data)
    {
        try
        {
            ClientInfo clientInfo;
            lock (_lock)
            {
                if (!_clients.TryGetValue(clientId, out clientInfo) || !clientInfo.Client.Connected)
                {
                    Console.WriteLine($"客户端 {clientId} 未连接或已断开");
                    return false;
                }
            }

            byte[] bytes = Encoding.UTF8.GetBytes(data);
            clientInfo.Client.GetStream().Write(bytes, 0, bytes.Length);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"向客户端 {clientId} 发送数据失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 向所有连接的客户端广播数据
    /// </summary>
    /// <param name="data">要广播的数据</param>
    public void BroadcastData(string data)
    {
        List<string> disconnectedClients = new List<string>();
        byte[] bytes = Encoding.UTF8.GetBytes(data);

        lock (_lock)
        {
            foreach (var kvp in _clients)
            {
                string clientId = kvp.Key;
                ClientInfo clientInfo = kvp.Value;

                try
                {
                    if (clientInfo.Client.Connected)
                    {
                        clientInfo.Client.GetStream().Write(bytes, 0, bytes.Length);
                    }
                    else
                    {
                        disconnectedClients.Add(clientId);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"向客户端 {clientId} 广播数据失败: {ex.Message}");
                    disconnectedClients.Add(clientId);
                }
            }

            // 移除断开连接的客户端
            foreach (string clientId in disconnectedClients)
            {
                _clients.Remove(clientId);
                // 触发客户端断开连接事件
                OnClientDisconnected(clientId);
            }
        }
    }

    /// <summary>
    /// 断开指定客户端的连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    public void DisconnectClient(string clientId)
    {
        try
        {
            lock (_lock)
            {
                if (_clients.TryGetValue(clientId, out ClientInfo clientInfo))
                {
                    if (clientInfo.Client.Connected)
                    {
                        clientInfo.Client.Close();
                    }
                    _clients.Remove(clientId);
                    // 触发客户端断开连接事件
                    OnClientDisconnected(clientId);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"断开客户端 {clientId} 连接失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取客户端ID
    /// </summary>
    /// <param name="client">TcpClient</param>
    /// <returns>客户端ID</returns>
    private string GetClientId(TcpClient client)
    {
        IPEndPoint endPoint = client.Client.RemoteEndPoint as IPEndPoint;
        return endPoint?.Address.ToString() + ":" + endPoint?.Port.ToString() ?? "unknown";
    }



    /// <summary>
    /// 检查客户端心跳，清理超时客户端
    /// </summary>
    /// <param name="state">状态</param>
    private void CheckHeartbeat(object state)
    {
        if (!_isRunning) return;

        List<string> timeoutClients = new List<string>();
        DateTime now = DateTime.Now;

        lock (_lock)
        {
            foreach (var kvp in _clients)
            {
                string clientId = kvp.Key;
                ClientInfo clientInfo = kvp.Value;

                // 检查客户端是否启用心跳检测且已超时
                if (clientInfo.IsHeartbeatEnabled &&
                    (now - clientInfo.LastActiveTime).TotalMilliseconds > CLIENT_TIMEOUT_MS)
                {
                    timeoutClients.Add(clientId);
                }
            }

            // 断开所有超时的客户端连接
            foreach (string clientId in timeoutClients)
            {
                try
                {
                    ClientInfo clientInfo = _clients[clientId];
                    if (clientInfo.Client.Connected)
                    {
                        clientInfo.Client.Close();
                    }
                    _clients.Remove(clientId);
                    // 触发客户端断开连接事件
                    OnClientDisconnected(clientId);
                    Console.WriteLine($"客户端 {clientId} 因超时被断开连接");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"断开超时客户端 {clientId} 失败: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// 发送心跳包到指定客户端
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否发送成功</returns>
    public bool SendHeartbeat(string clientId)
    {
        return SendData(clientId, "HEARTBEAT");
    }

    /// <summary>
    /// 启用/禁用客户端心跳检测
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="enabled">是否启用</param>
    public void SetHeartbeatEnabled(string clientId, bool enabled)
    {
        lock (_lock)
        {
            if (_clients.TryGetValue(clientId, out ClientInfo clientInfo))
            {
                clientInfo.IsHeartbeatEnabled = enabled;
            }
        }
    }

    /// <summary>
    /// 获取所有连接的客户端数量
    /// </summary>
    /// <returns>客户端数量</returns>
    public int GetConnectedClientCount()
    {
        lock (_lock)
        {
            return _clients.Count;
        }
    }

    /// <summary>
    /// 获取所有客户端ID列表
    /// </summary>
    /// <returns>客户端ID列表</returns>
    public List<string> GetAllClientIds()
    {
        lock (_lock)
        {
            return new List<string>(_clients.Keys);
        }
    }

    /// <summary>
    /// 检查客户端是否连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否连接</returns>
    public bool IsClientConnected(string clientId)
    {
        lock (_lock)
        {
            return _clients.TryGetValue(clientId, out ClientInfo clientInfo) && clientInfo.Client.Connected;
        }
    }

    /// <summary>
    /// 获取客户端最后活动时间
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>最后活动时间</returns>
    public DateTime? GetClientLastActiveTime(string clientId)
    {
        lock (_lock)
        {
            if (_clients.TryGetValue(clientId, out ClientInfo clientInfo))
            {
                return clientInfo.LastActiveTime;
            }
            return null;
        }
    }


    /// <summary>
    /// 触发客户端连接事件
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="client">TcpClient</param>
    protected virtual void OnClientConnected(string clientId, TcpClient client)
    {
        IPEndPoint endPoint = client.Client.RemoteEndPoint as IPEndPoint;
        ClientConnected?.Invoke(this, new SocketClientConnectedEventArgs
        {
            ClientId = clientId,
            IpAddress = endPoint?.Address.ToString(),
            Port = endPoint?.Port ?? 0
        });
        Console.WriteLine($"客户端 {clientId} 已连接");
    }

    /// <summary>
    /// 触发客户端断开连接事件
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    protected virtual void OnClientDisconnected(string clientId)
    {
        ClientDisconnected?.Invoke(this, new SocketClientDisconnectedEventArgs { ClientId = clientId });
        Console.WriteLine($"客户端 {clientId} 已断开连接");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Stop();
        _tcpListener = null;
        _clients = null;
    }
}

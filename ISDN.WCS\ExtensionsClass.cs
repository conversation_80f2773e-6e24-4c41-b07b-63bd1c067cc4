﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components;
using RuoYi.Common.Enums;
using RuoYi.Data.Entities;
using RuoYi.Data.Models;
using RuoYi.Framework;
using RuoYi.Framework.Utils;
using RuoYi.System.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ISDN.WCS
{
    public static class ExtensionsClass
    {
        public static Exception GetInnerException(this Exception exception)
        {
            if (exception.InnerException == null)
                return exception;
            return GetInnerException(exception.InnerException);
        }

        public static void AddLog(string deviceCode, string title,  string requestStr, string responseStr)
        {
            // *========数据库日志=========*//
            var operLog = new SysOperLog
            {
                //Status = e != null ? BusinessStatus.FAIL.ToInt() : BusinessStatus.SUCCESS.ToInt(),
                //ErrorMsg = e != null ? StringUtils.Substring(e.Message, 0, 2000) : null,

                // 请求的地址
                //OperIp = remoteIPv4,
                //OperUrl = requestUrl,
                OperName = $"Socket【{deviceCode}】",
                OperTime = DateTime.Now,

                // 远程查询操作地点
                OperLocation = "0.0.0.0",

                //// 设置方法名称
                //Method = methodFullName,
                //// 设置请求方式
                //RequestMethod = httpMethod,

                //// 处理设置注解上的参数
                //// 设置action动作
                //BusinessType = BusinessType.ToInt(),
                // 设置标题
                Title = title,

                //// 设置操作人类别
                //OperatorType = OperatorType.ToInt(),

                // 是否需要保存request，参数和值
                OperParam = requestStr,

                // 是否需要保存response，参数和值
                JsonResult = responseStr,
                
                //// 设置消耗时间
                //CostTime = timeOperation.ElapsedMilliseconds
            };
            // 保存数据库
            _ = Task.Factory.StartNew(async () =>
            {
                var sysOperLogService = App.GetService<SysOperLogService>();
                await sysOperLogService.InsertAsync(operLog);
            });
        }
    }
}

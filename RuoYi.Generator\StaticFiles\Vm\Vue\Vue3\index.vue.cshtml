@{
    var moduleName = Model.moduleName;
    var businessName = Model.businessName;
    var BusinessName = Model.BusinessName;
    var dictType = "";
    var attrName = "";
    var comment = "";
    var dictsNoSymbol = "";
    int parentheseIndex = -1;
    var subClassName = Model.SubClassName;
    var subclassName = Model.subClassName;
}
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
@foreach(var column in Model.Columns) {
  @if(column.Is_Query()) {
      dictType = column.DictType ?? "";
      attrName = column.NetFieldLower();
      parentheseIndex = column.ColumnComment?.IndexOf("（") ?? -1;
      comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;

     if(column.HtmlType == "input") {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-input
          v-model="queryParams.@(attrName)"
          placeholder="请输入@(comment)"
          clearable
          @@keyup.enter="handleQuery"
        />
      </el-form-item>
     } else if((column.HtmlType == "select" || column.HtmlType == "radio") && "" != dictType) {
      <el-form-item label="@(comment)" prop="@(attrName)">
                            <el-select v-model="queryParams.@(attrName)" placeholder="请选择@(comment)" clearable style="width: 240px;">
            <el-option
                v-for="dict in @(dictType)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
        </el-select>
      </el-form-item>
     } else if((column.HtmlType == "select" || column.HtmlType == "radio") && "" != dictType) {
      <el-form-item label="@(comment)" prop="@(attrName)">
                            <el-select v-model="queryParams.@(attrName)" placeholder="请选择@(comment)" clearable style="width: 240px;">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
    } else if(column.HtmlType == "datetime" && column.QueryType != "BETWEEN") {
      <el-form-item label="@(comment)" prop="@(attrName)">
        <el-date-picker clearable
          v-model="queryParams.@(attrName)"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择@(comment)">
        </el-date-picker>
      </el-form-item>
    } else if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
      <el-form-item label="@(comment)" style="width: 308px">
        <el-date-picker
          v-model="daterange@(attrName)"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
    }
  }
}
      <el-form-item>
        <el-button type="primary" icon="Search" @@click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @@click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
          <el-button
              type="primary"
              plain
              icon="Plus"
              @@click="handleAdd"
              v-hasPermi="['@(moduleName):@(businessName):add']"
          >新增</el-button>
      </el-col>
      <el-col :span="1.5">
          <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @@click="handleUpdate"
              v-hasPermi="['@(moduleName):@(businessName):edit']"
          >修改</el-button>
      </el-col>
      <el-col :span="1.5">
          <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @@click="handleDelete"
              v-hasPermi="['@(moduleName):@(businessName):remove']"
          >删除</el-button>
      </el-col>
      <el-col :span="1.5">
          <el-button
              type="warning"
              plain
              icon="Download"
              @@click="handleExport"
              v-hasPermi="['@(moduleName):@(businessName):export']"
          >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @@queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="@(businessName)List" @@selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
@foreach(var column in Model.Columns) {
    attrName = column.NetFieldLower();
    dictType = column.DictType ?? "";
    parentheseIndex = column.ColumnComment?.IndexOf("（") ?? -1;
    comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
    if(column.Is_Pk()) {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" />
    } else if (column.Is_List() && column.HtmlType == "datetime") {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" width="180">
          <template #default="scope">
              <span>{{ parseTime(scope.row.@(attrName), '{y}-{m}-{d}') }}</span>
          </template>
      </el-table-column>
    } else if (column.Is_List() && column.HtmlType == "imageUpload") {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" width="100">
          <template #default="scope">
              <image-preview :src="scope.row.@(attrName)" :width="50" :height="50"/>
          </template>
      </el-table-column>
    } else if (column.Is_List() && "" != dictType) {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)">
        <template #default="scope">
        @if(column.HtmlType == "checkbox") {
          <dict-tag :options="@(dictType)" :value="scope.row.@(attrName) ? scope.row.@(attrName).split(',') : []"/>
        } else {
          <dict-tag :options="@(dictType)" :value="scope.row.@(attrName)" />
        }
        </template>
      </el-table-column>
    } else if (column.Is_List() && "" != attrName) {
      <el-table-column label="@(comment)" align="center" prop="@(attrName)" />
    }
}
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @@click="handleUpdate(scope.row)" v-hasPermi="['@(moduleName):@(businessName):edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @@click="handleDelete(scope.row)" v-hasPermi="['@(moduleName):@(businessName):remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @@pagination="getList"
    />

    <!-- 添加或修改@(Model.FunctionName)对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="@(businessName)Ref" :model="form" :rules="rules" label-width="80px">
@foreach(var column in Model.Columns) {
  attrName = column.NetFieldLower();
  if(column.Is_Insert() && !column.Is_Pk()) {
    if(column.IsUsableColumn() || !column.IsSuperColumn()) {
      parentheseIndex = column.ColumnComment.IndexOf("（");
      comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
      dictType = column.DictType ?? "";
      var selectVal = column.NetType.Contains("int") || column.NetType.Contains("long") ? "parseInt(dict.value)" : "dict.value";
      var radioLabel = column.NetType.Contains("int") || column.NetType.Contains("long") ? "parseInt(dict.value)" : "dict.value";
      if(column.HtmlType == "input") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-input v-model="form.@(attrName)" placeholder="请输入@(comment)" />
        </el-form-item>
      } else if(column.HtmlType == "imageUpload") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <image-upload v-model="form.@(attrName)"/>
        </el-form-item>
      } else if(column.HtmlType == "fileUpload") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <file-upload v-model="form.@(attrName)"/>
        </el-form-item>
      } else if(column.HtmlType == "editor") {
        <el-form-item label="@(comment)">
          <editor v-model="form.@(attrName)" :min-height="192"/>
        </el-form-item>
      } else if(column.HtmlType == "select" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-select v-model="form.@(attrName)" placeholder="请选择@(comment)">
            <el-option
               v-for="dict in @(dictType)"
               :key="dict.value"
               :label="dict.label"
               :value = "@(selectVal)"
            ></el-option>
          </el-select>
        </el-form-item>
      } else if(column.HtmlType == "select" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-select v-model="form.@(attrName)" placeholder="请选择@(comment)">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
      } else if(column.HtmlType == "checkbox" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-checkbox-group v-model="form.@(attrName)">
            <el-checkbox
              v-for="dict in @(dictType)"
              :key="dict.value"
              :label="dict.value">
              {{dict.label}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      } else if(column.HtmlType == "checkbox" &&  "" == dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-checkbox-group v-model="form.@(attrName)">
            <el-checkbox>请选择字典生成</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      } else if(column.HtmlType == "radio" && "" != dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-radio-group v-model="form.@(attrName)">
            <el-radio
              v-for="dict in @(dictType)"
              :key="dict.value"
              :label="@(radioLabel)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      } else if(column.HtmlType == "radio" && "" == dictType) {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-radio-group v-model="form.@(attrName)">
            <el-radio label="1">请选择字典生成</el-radio>
          </el-radio-group>
        </el-form-item>
      } else if(column.HtmlType == "datetime") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-date-picker clearable
            v-model="form.@(attrName)"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择@(comment)">
          </el-date-picker>
        </el-form-item>
      } else if(column.HtmlType == "textarea") {
        <el-form-item label="@(comment)" prop="@(attrName)">
          <el-input v-model="form.@(attrName)" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      }
    }
  }
}
@if(Model.Table.IsSub()) {
        <el-divider content-position="center">@(Model.SubTable?.FunctionName)信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="Plus" @@click="handleAdd@(subClassName)">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="Delete" @@click="handleDelete@(subClassName)">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="@(subclassName)List" :row-class-name="row@(subClassName)Index" @@selection-change="handle@(subClassName)SelectionChange" ref="@(subclassName)">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
  @foreach(var column in Model.SubTable.Columns) {
    attrName = column.NetFieldLower();
    parentheseIndex = column.ColumnComment.IndexOf("（");
    comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;

    if(column.Is_Pk() || attrName == Model.SubTableFkName) {
    } else if(column.Is_List() && column.HtmlType == "input") {
          <el-table-column label="@(comment)" prop="@(attrName)" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.@(attrName)" placeholder="请输入@(comment)" />
            </template>
          </el-table-column>
    } else if(column.Is_List() && column.HtmlType == "datetime") {
          <el-table-column label="@(comment)" prop="@(attrName)" width="240">
            <template #default="scope">
              <el-date-picker clearable
                v-model="scope.row.@(attrName)"
                type="date" value-format="yyyy-MM-dd"
                placeholder="请选择@(comment)" />
            </template>
          </el-table-column>
    } else if(column.Is_List() && (column.HtmlType == "select" || column.HtmlType == "radio") && "" != column.DictType) {
          <el-table-column label="@(comment)" prop="@(attrName)" width="150">
            <template #default="scope">
              <el-select v-model="scope.row.@(attrName)" placeholder="请选择@(comment)">
                <el-option v-for="dict in @(column.DictType)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
    } else if(column.Is_List() && (column.HtmlType == "select" || column.HtmlType == "radio") && "" == column.DictType) {
          <el-table-column label="@(comment)" prop="@(attrName)" width="150">
            <template #default="scope">
              <el-select v-model="scope.row.@(attrName)" placeholder="请选择@(comment)">
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </template>
          </el-table-column>
    }
  }
        </el-table>
}
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @@click="submitForm">确 定</el-button>
        <el-button @@click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="@(BusinessName)">
import { list@(BusinessName), get@(BusinessName), del@(BusinessName), add@(BusinessName), update@(BusinessName) } from "@@/api/@(moduleName)/@(businessName)";

const { proxy } = getCurrentInstance();

@if ( Model.Dicts != "") {
dictsNoSymbol = Model.Dicts.Replace("'","");
@:const { @(dictsNoSymbol) } = proxy.useDict( @(Model.Dicts));
}
const @(businessName)List = ref([]);
@if(Model.Table.IsSub()) {
@:const @(subclassName)List = ref([]);
}
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
@if(Model.Table.IsSub()) {
@:const checked@subClassName) = ref([]);
}
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

@foreach (var column in Model.Columns) {
if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
attrName = column.NetFieldLower();
@:// @(comment)时间范围
@:const daterange@(attrName) = ref([]);
}
}

const data = reactive({
    form: {},
    queryParams: {
        pageNum: 1,
        pageSize: 10,
@foreach (var column in Model.Columns) {
    if(column.Is_Query()) {
       @:@(column.NetFieldLower()): null,
        }
    }
    },
    rules: {
@foreach (var column in Model.Columns) {
  @if(column.Is_Required()) {
    parentheseIndex = column.ColumnComment.IndexOf("（");
    comment = parentheseIndex != -1 ? column.ColumnComment.Substring(0, parentheseIndex) : column.ColumnComment;
    var trigger = column.HtmlType == "select" || column.HtmlType == "radio" ? "change" : "blur";
        @:@column.NetFieldLower(): [
            @:  { required: true, message: "@(comment)不能为空", trigger: "@(trigger)" }
            @:],
  }
}
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询@(Model.FunctionName)列表 */
function getList() {
    loading.value = true;
    @foreach (var column in Model.Columns) {
    if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
    @:queryParams.value.params = {};
    break;
    }
    }
    @foreach (var column in Model.Columns) {
    if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
    attrName = column.NetFieldLower();
    @:if (null != daterange@(attrName) && '' != daterange@(attrName)) {
        @:queryParams.value.params["begin@(attrName)"] = daterange@(attrName).value[0];
        @:queryParams.value.params["end@(attrName)"] = daterange@(attrName).value[1];
        @:}
    }
    }
    list@(BusinessName)(queryParams.value).then(response => {
        @(businessName)List.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        @foreach (var column in Model.Columns) {
        var comma = Model.Columns.IndexOf(column) != Model.Columns.Count - 1 ? "," : "";
        if(column.HtmlType == "checkbox") {
        @:@column.NetFieldLower(): []@(comma)
        } else {
        @:@column.NetFieldLower(): null@(comma)
        }
        }
    };
    @if(Model.Table.IsSub()) {
    @:@(subclassName)List.value = [];
    }
    proxy.resetForm("@(businessName)Ref");
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    @foreach (var column in Model.Columns) {
    if(column.HtmlType == "datetime" && column.QueryType == "BETWEEN") {
    attrName = column.NetFieldLower();
    @:daterange@(attrName).value = [];
    }
    }
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.@(Model.PkColumn.NetFieldLower()))
    single.value = selection.length!==1
    multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加@(Model.FunctionName)";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _@(Model.PkColumn.NetFieldLower()) = row.@(Model.PkColumn.NetFieldLower()) || ids.value;
    get@(BusinessName)(_@(Model.PkColumn.NetFieldLower())).then(response => {
        form.value = response.data;
        @foreach (var column in Model.Columns) {
        if(column.HtmlType == "checkbox") {
        @:form.value.@(column.NetFieldLower()) = form.value.@(attrName).split(",");
        }
        }
        @if(Model.Table.IsSub()) {
        @:@(subclassName)List.value = response.data.@(subclassName)List;
        }
        open.value = true;
        title.value = "修改@(Model.FunctionName)";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["@(businessName)Ref"].validate(valid => {
        if (valid) {
            @foreach (var column in Model.Columns) {
            if(column.HtmlType == "checkbox") {
            @:form.value.@(column.NetFieldLower()) = form.value.@(attrName).join(",");
            }
            }
            @if(Model.Table.IsSub()) {
            @:form.value.@(subclassName)List = @(subclassName)List.value;
            }
            if (form.value.@(Model.PkColumn.NetFieldLower()) != null) {
                update@(BusinessName)(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                add@(BusinessName)(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _@(Model.PkColumn.NetFieldLower())s = row.@(Model.PkColumn.NetFieldLower()) || ids.value;
        proxy.$modal.confirm('是否确认删除@(Model.FunctionName)编号为"' + _@(Model.PkColumn.NetFieldLower())s + '"的数据项？').then(function() {
        return del@(BusinessName)(_@(Model.PkColumn.NetFieldLower())s);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
}


@if(Model.Table.IsSub()) {
@:/** @(Model.SubTable.FunctionName)序号 */
@:function row@(subClassName)Index({ row, rowIndex }) {
    @:  row.index = rowIndex + 1;
    @:}

@:/** @(Model.SubTable.FunctionName)添加按钮操作 */
@:function handleAdd@(subClassName)() {
    @:  let obj = {};
    @foreach(var column in Model.SubTable.Columns) {
    if(column.Is_Pk() || column.NetField == Model.SubTableFkName) {
    } else if(column.Is_List() && "" != column.NetField) {
    @:  obj.@(column.NetFieldLower()) = "";
    }
    }
    @:  @(subclassName)List.value.push(obj);
    @:}

@:/** @(Model.SubTable.FunctionName)删除按钮操作 */
@:function handleDelete@(subClassName)() {
    @:  if (checked@(subClassName).value.length == 0) {
        @:    proxy.$modal.msgError("请先选择要删除的@(Model.SubTable.FunctionName)数据");
        @:  } else {
        @:    const @(subclassName)s = @(subclassName)List.value;
        @:    const checked@(subClassName)s = checked@(subClassName).value;
        @:    @(subclassName)List.value = @(subclassName)s.filter(function(item) {
            @:      return checked@(subClassName)s.indexOf(item.index) == -1
            @:    });
        @:  }
    @:}

/** 复选框选中数据 */
@:function handle@(subClassName)SelectionChange(selection) {
    @:  checked@(subClassName).value = selection.map(item => item.index)
    @:}
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('@(moduleName)/@(businessName)/export', {
        ...queryParams.value
    }, `@(businessName)_${new Date().getTime()}.xlsx`)
}

getList();
</script>

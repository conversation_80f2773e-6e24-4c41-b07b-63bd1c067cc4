﻿using Microsoft.Extensions.Logging;

namespace RuoYi.Framework.Logging;

/// <summary>
///   空日志记录器
/// </summary>
/// <remarks>https://docs.microsoft.com/zh-cn/dotnet/core/extensions/custom-logging-provider</remarks>
[SuppressSniffer]
public sealed class EmptyLogger : ILogger
{
  /// <summary>
  ///   开始逻辑操作范围
  /// </summary>
  /// <typeparam name="TState">标识符类型参数</typeparam>
  /// <param name="state">要写入的项/对象</param>
  /// <returns>
  ///   <see cref="IDisposable" />
  /// </returns>
  public IDisposable BeginScope<TState>(TState state) where TState : notnull
  {
    return default;
  }

  /// <summary>
  ///   检查是否已启用给定日志级别
  /// </summary>
  /// <param name="logLevel">日志级别</param>
  /// <returns>
  ///   <see cref="bool" />
  /// </returns>
  public bool IsEnabled(LogLevel logLevel)
  {
    return false;
  }

  /// <summary>
  ///   写入日志项
  /// </summary>
  /// <typeparam name="TState">标识符类型参数</typeparam>
  /// <param name="logLevel">日志级别</param>
  /// <param name="eventId">事件 Id</param>
  /// <param name="state">要写入的项/对象</param>
  /// <param name="exception">异常对象</param>
  /// <param name="formatter">日志格式化器</param>
  /// <exception cref="ArgumentNullException"></exception>
  public void Log<TState>(LogLevel logLevel
    , EventId eventId
    , TState state
    , Exception? exception
    , Func<TState, Exception, string> formatter)
  {
    // 判断日志级别是否有效
    if (!IsEnabled(logLevel)) return;
  }
}

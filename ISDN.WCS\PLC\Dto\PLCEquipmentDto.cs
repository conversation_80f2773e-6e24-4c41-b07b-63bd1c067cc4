namespace ISDN.WCS.PLC.Dto;

/// <summary>
/// PLC设备信息DTO
/// </summary>
public class PLCEquipmentDto
{
    /// <summary>
    /// 设备号
    /// </summary>
    public int Equipment { get; set; }

    /// <summary>
    /// X轴坐标
    /// </summary>
    public int X { get; set; }

    /// <summary>
    /// Y轴坐标
    /// </summary>
    public int Y { get; set; }

    /// <summary>
    /// Z轴坐标
    /// </summary>
    public int Z { get; set; }

   
    /// <summary>
    /// 运行状态
    /// 1: 空闲
    /// 2: 运行
    /// 3: 故障
    /// </summary>
    public byte RunningState { get; set; }
}
using Microsoft.Extensions.DependencyInjection;
using SqlSugar;
using SqlSugar.Extensions;
using System;
using System.Threading;

namespace RuoYi.Framework;

public static class SqlSugarSetup
{
    private static readonly object _lock = new object();
    private static volatile SqlSugarClient _sqlSugarClient;
    private static readonly int MaxRetryCount = 3;
    private static readonly int RetryDelayMs = 1000;

    public static void AddSqlSugarClient(this IServiceCollection services)
    {
        // 注册 SqlSugar 客户端
        services.AddScoped<ISqlSugarClient>(u =>
        {
            SqlSugarClient sqlSugarClient = GetSqlSugarClient();
            return sqlSugarClient;
        });

        // 注册非泛型仓储
        services.AddScoped<ISqlSugarRepository, SqlSugarRepository>();

        // 注册 SqlSugar 仓储
        services.AddScoped(typeof(ISqlSugarRepository<>), typeof(SqlSugarRepository<>));
    }

    public static SqlSugarClient GetSqlSugarClient()
    {
        if (_sqlSugarClient == null)
        {
            lock (_lock)
            {
                if (_sqlSugarClient == null)
                {
                    _sqlSugarClient = CreateSqlSugarClientWithRetry();
                }
            }
        }
        return _sqlSugarClient;
    }

    private static SqlSugarClient CreateSqlSugarClientWithRetry()
    {
        var connectionConfigs = App.GetConfig<ConnectionConfig[]>("ConnectionConfigs");

        // 优化连接字符串配置
        foreach (var config in connectionConfigs)
        {
            config.ConnectionString = OptimizeConnectionString(config.ConnectionString);
        }

        Exception lastException = null;

        for (int attempt = 1; attempt <= MaxRetryCount; attempt++)
        {
            try
            {
                var sqlSugarClient = new SqlSugarClient(connectionConfigs.ToList(), db =>
                {
                    ConfigureDatabase(db, connectionConfigs);
                });

                // 测试连接
                sqlSugarClient.Ado.CheckConnection();
                Console.WriteLine($"数据库连接成功建立 (尝试次数: {attempt})");
                return sqlSugarClient;
            }
            catch (Exception ex)
            {
                lastException = ex;
                Console.WriteLine($"数据库连接失败 (尝试 {attempt}/{MaxRetryCount}): {ex.Message}");

                if (attempt < MaxRetryCount)
                {
                    Thread.Sleep(RetryDelayMs * attempt); // 递增延迟
                }
            }
        }

        throw new InvalidOperationException($"经过 {MaxRetryCount} 次尝试后仍无法建立数据库连接", lastException);
    }

    private static string OptimizeConnectionString(string originalConnectionString)
    {
        // 如果连接字符串已经包含优化参数，则不重复添加
        if (originalConnectionString.Contains("ConnectionLifeTime") ||
            originalConnectionString.Contains("ConnectionIdleTimeout"))
        {
            return originalConnectionString;
        }

        // 添加连接池优化参数
        var optimizedParams = ";ConnectionLifeTime=300;ConnectionIdleTimeout=180;ConnectionReset=true;Keepalive=30;";

        // 移除末尾的分号（如果存在）然后添加优化参数
        var connectionString = originalConnectionString.TrimEnd(';') + optimizedParams;

        return connectionString;
    }

    private static void ConfigureDatabase(SqlSugarClient db, ConnectionConfig[] connectionConfigs)
    {
        foreach (var conn in connectionConfigs)
        {
            // SQL执行前
            db.Aop.OnLogExecuting = (sql, pars) =>
            {
                var fmtSql = SqlProfiler.ParameterFormat(sql, pars);
                Console.WriteLine("\n" + fmtSql);
                App.PrintToMiniProfiler("SqlSugar", "Info", fmtSql);
            };

            db.Aop.OnLogExecuted = (sql, pars) =>
            {
                // 可以记录执行时间用于性能监控
                // Console.WriteLine("time:" + db.Ado.SqlExecutionTime.ToString());
            };

            // 增强的错误处理
            db.Aop.OnError = exp =>
            {
                var errorMsg = $"[SQL Error]: {exp.Sql} - [Parameters]: {exp.Parametres.ObjToString()} - [Error]: {exp.Message}";
                Console.WriteLine(errorMsg);

                // 如果是连接相关错误，标记需要重新创建连接
                if (IsConnectionError(exp))
                {
                    Console.WriteLine("检测到连接错误，将在下次访问时重新创建连接");
                    ResetConnection();
                }
            };

            // Sql执行完后会进该事件
            db.Aop.OnDiffLogEvent = it =>
            {
                var beforeData = it.BeforeData;
                var afterData = it.AfterData;
                var sql = it.Sql;
                var parameter = it.Parameters;
                var data = it.BusinessData;
                var time = it.Time;
                var diffType = it.DiffType;
            };
        }
    }

    private static bool IsConnectionError(Exception ex)
    {
        var errorMessage = ex.Message.ToLower();
        return errorMessage.Contains("packet received out-of-order") ||
               errorMessage.Contains("connection") ||
               errorMessage.Contains("timeout") ||
               errorMessage.Contains("unable to connect") ||
               errorMessage.Contains("lost connection");
    }

    private static void ResetConnection()
    {
        lock (_lock)
        {
            if (_sqlSugarClient != null)
            {
                try
                {
                    _sqlSugarClient.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"释放旧连接时出错: {ex.Message}");
                }
                finally
                {
                    _sqlSugarClient = null;
                }
            }
        }
    }

    public static void AddSqlSugarScope(this IServiceCollection services)
    {
        var connectionConfigs = App.GetConfig<ConnectionConfig[]>("ConnectionConfigs");

        // 注册 SqlSugar
        services.AddSingleton<ISqlSugarClient>(u =>
        {
            // SqlSugarScope 必须使用单例
            var sqlSugarScope = new SqlSugarScope(connectionConfigs.ToList(), db =>
        {
              foreach (var conn in connectionConfigs)
              {
                  var configId = conn.ConfigId;

                // SQL执行前
                  db.GetConnectionScope(configId).Aop.OnLogExecuting = (sql, pars) =>
              {
                  var fmtSql = SqlProfiler.ParameterFormat(sql, pars);
                  Console.WriteLine("\n" + fmtSql); //输出sql
                  App.PrintToMiniProfiler("SqlSugar", "Info", fmtSql);
              };

                  db.GetConnectionScope(configId).Aop.OnLogExecuted = (sql, pars) => //SQL执行完
              {
                  Console.Write("time:" + db.Ado.SqlExecutionTime); //输出SQL执行时间
              };
                  db.GetConnectionScope(configId).Aop.OnError = exp => //SQL报错
              {
                  //exp.sql 这样可以拿到错误SQL
              };
                //db.Aop.OnExecutingChangeSql = (sql, pars) => //可以修改SQL和参数的值
                //{
                //    return new KeyValuePair<string, SugarParameter[]>(sql, pars);s
                //};

                // Sql执行完后会进该事件，该事件可以拿到更改前记录和更改后记录，执行时间等参数
                  db.GetConnectionScope(configId).Aop.OnDiffLogEvent = it =>
              {
                  var beforeData = it.BeforeData; // 操作前记录  包含： 字段描述 列名 值 表名 表描述
                  var afterData = it.AfterData; // 操作后记录   包含： 字段描述 列名 值  表名 表描述
                  var sql = it.Sql;
                  var parameter = it.Parameters;
                  var data = it.BusinessData; // 这边会显示你传进来的对象
                  var time = it.Time;
                  var diffType = it.DiffType; //enum insert 、update and delete
              };
              }
          });

            return sqlSugarScope;
        });

        // 注册非泛型仓储
        services.AddScoped<ISqlSugarRepository, SqlSugarRepository>();

        // 注册 SqlSugar 仓储
        services.AddScoped(typeof(ISqlSugarRepository<>), typeof(SqlSugarRepository<>));
    }
}

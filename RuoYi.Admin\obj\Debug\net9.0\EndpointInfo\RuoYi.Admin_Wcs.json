{"openapi": "3.0.4", "info": {"title": "Wcs", "version": "1.0.0"}, "paths": {"/wcs/container/list": {"get": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-list-Get", "parameters": [{"name": "ContainerId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ContainerName", "in": "query", "schema": {"type": "string"}}, {"name": "ContainerCode", "in": "query", "schema": {"type": "string"}}, {"name": "LocationId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LocationCode", "in": "query", "schema": {"type": "string"}}, {"name": "CurrentAdrr", "in": "query", "schema": {"type": "string"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsContainerDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsContainerDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsContainerDto"}}}}}}}, "/wcs/container": {"get": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-Get", "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsContainerDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/container/{id}": {"get": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/container/{ids}": {"delete": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/container/import": {"post": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-import-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/wcs/container/export": {"post": {"tags": ["WcsC<PERSON>r"], "operationId": "wcs-container-export-Post", "parameters": [{"name": "ContainerId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ContainerName", "in": "query", "schema": {"type": "string"}}, {"name": "ContainerCode", "in": "query", "schema": {"type": "string"}}, {"name": "LocationId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LocationCode", "in": "query", "schema": {"type": "string"}}, {"name": "CurrentAdrr", "in": "query", "schema": {"type": "string"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/wcs/device/list": {"get": {"tags": ["WcsDevice"], "operationId": "wcs-device-list-Get", "parameters": [{"name": "DeviceCode", "in": "query", "schema": {"type": "string"}}, {"name": "DeviceName", "in": "query", "schema": {"type": "string"}}, {"name": "DeviceType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Addr", "in": "query", "schema": {"type": "string"}}, {"name": "Port", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Online", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "RunStatus", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DeviceId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsDeviceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsDeviceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsDeviceDto"}}}}}}}, "/wcs/device/ListAll": {"get": {"tags": ["WcsDevice"], "operationId": "wcs-device-ListAll-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/device": {"get": {"tags": ["WcsDevice"], "operationId": "wcs-device-Get", "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["WcsDevice"], "operationId": "wcs-device-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["WcsDevice"], "operationId": "wcs-device-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsDeviceDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/device/{id}": {"get": {"tags": ["WcsDevice"], "operationId": "wcs-device-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/device/{ids}": {"delete": {"tags": ["WcsDevice"], "operationId": "wcs-device-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/device/import": {"post": {"tags": ["WcsDevice"], "operationId": "wcs-device-import-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/wcs/device/export": {"post": {"tags": ["WcsDevice"], "operationId": "wcs-device-export-Post", "parameters": [{"name": "DeviceCode", "in": "query", "schema": {"type": "string"}}, {"name": "DeviceName", "in": "query", "schema": {"type": "string"}}, {"name": "DeviceType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Addr", "in": "query", "schema": {"type": "string"}}, {"name": "Port", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Online", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "RunStatus", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DeviceId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/wcs/device/online/{deviceId}": {"get": {"tags": ["WcsDevice"], "operationId": "wcs-device-online-deviceId-Get", "parameters": [{"name": "deviceId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/lane/list": {"get": {"tags": ["WcsLane"], "operationId": "wcs-lane-list-Get", "parameters": [{"name": "LaneName", "in": "query", "schema": {"type": "string"}}, {"name": "LaneId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LaneType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "LaneCode", "in": "query", "schema": {"type": "string"}}, {"name": "DeviceId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsLaneDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsLaneDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsLaneDto"}}}}}}}, "/wcs/lane/ListAll": {"get": {"tags": ["WcsLane"], "operationId": "wcs-lane-ListAll-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/lane": {"get": {"tags": ["WcsLane"], "operationId": "wcs-lane-Get", "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["WcsLane"], "operationId": "wcs-lane-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["WcsLane"], "operationId": "wcs-lane-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsLaneDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/lane/{id}": {"get": {"tags": ["WcsLane"], "operationId": "wcs-lane-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/lane/{ids}": {"delete": {"tags": ["WcsLane"], "operationId": "wcs-lane-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/lane/import": {"post": {"tags": ["WcsLane"], "operationId": "wcs-lane-import-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/wcs/lane/export": {"post": {"tags": ["WcsLane"], "operationId": "wcs-lane-export-Post", "parameters": [{"name": "LaneName", "in": "query", "schema": {"type": "string"}}, {"name": "LaneId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LaneType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "LaneCode", "in": "query", "schema": {"type": "string"}}, {"name": "DeviceId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/wcs/task/list": {"get": {"tags": ["WcsTask"], "operationId": "wcs-task-list-Get", "parameters": [{"name": "TaskId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "TaskCode", "in": "query", "schema": {"type": "string"}}, {"name": "BinNo", "in": "query", "schema": {"type": "string"}}, {"name": "EquipmentNo", "in": "query", "schema": {"type": "string"}}, {"name": "TaskType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartLocation", "in": "query", "schema": {"type": "string"}}, {"name": "EndLocation", "in": "query", "schema": {"type": "string"}}, {"name": "Retries", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxRetries", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ErrMsg", "in": "query", "schema": {"type": "string"}}, {"name": "TaskStatus", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsTaskDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsTaskDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsTaskDto"}}}}}}}, "/wcs/task": {"get": {"tags": ["WcsTask"], "operationId": "wcs-task-Get", "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["WcsTask"], "operationId": "wcs-task-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["WcsTask"], "operationId": "wcs-task-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsTaskDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/task/{id}": {"get": {"tags": ["WcsTask"], "operationId": "wcs-task-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/task/{ids}": {"delete": {"tags": ["WcsTask"], "operationId": "wcs-task-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/task/import": {"post": {"tags": ["WcsTask"], "operationId": "wcs-task-import-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/wcs/task/export": {"post": {"tags": ["WcsTask"], "operationId": "wcs-task-export-Post", "parameters": [{"name": "TaskId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "TaskCode", "in": "query", "schema": {"type": "string"}}, {"name": "BinNo", "in": "query", "schema": {"type": "string"}}, {"name": "EquipmentNo", "in": "query", "schema": {"type": "string"}}, {"name": "TaskType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartLocation", "in": "query", "schema": {"type": "string"}}, {"name": "EndLocation", "in": "query", "schema": {"type": "string"}}, {"name": "Retries", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxRetries", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ErrMsg", "in": "query", "schema": {"type": "string"}}, {"name": "TaskStatus", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/wcs/task/detail/{taskId}": {"get": {"tags": ["WcsTask"], "operationId": "wcs-task-detail-taskId-Get", "parameters": [{"name": "taskId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/location/list": {"get": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-list-Get", "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "X", "in": "query", "schema": {"type": "string"}}, {"name": "Y", "in": "query", "schema": {"type": "string"}}, {"name": "Z", "in": "query", "schema": {"type": "string"}}, {"name": "Layer", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Deep", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "LocationId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LocationName", "in": "query", "schema": {"type": "string"}}, {"name": "LocationCode", "in": "query", "schema": {"type": "string"}}, {"name": "LaneId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LaneName", "in": "query", "schema": {"type": "string"}}, {"name": "LocationType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsWarehouseLocationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsWarehouseLocationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SqlSugarPagedList_WcsWarehouseLocationDto"}}}}}}}, "/wcs/location": {"get": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-Get", "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "post": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-Post", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}, "put": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-Put", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/location/{id}": {"get": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/location/{ids}": {"delete": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-ids-Delete", "parameters": [{"name": "ids", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "application/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"additionalProperties": false}}}}}}}}, "/wcs/location/import": {"post": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-import-Post", "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/wcs/location/export": {"post": {"tags": ["WcsWarehouseLocation"], "operationId": "wcs-location-export-Post", "parameters": [{"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "X", "in": "query", "schema": {"type": "string"}}, {"name": "Y", "in": "query", "schema": {"type": "string"}}, {"name": "Z", "in": "query", "schema": {"type": "string"}}, {"name": "Layer", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Deep", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "LocationId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LocationName", "in": "query", "schema": {"type": "string"}}, {"name": "LocationCode", "in": "query", "schema": {"type": "string"}}, {"name": "LaneId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "LaneName", "in": "query", "schema": {"type": "string"}}, {"name": "LocationType", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsVisibleFlag", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "UpdatedBy", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "CreatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdatedTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UpdateBy", "in": "query", "schema": {"type": "string"}}, {"name": "UpdateTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Remark", "in": "query", "schema": {"type": "string"}}, {"name": "Params.params[beginTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.params[endTime]", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Params.DataScopeSql", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"QueryParam": {"type": "object", "properties": {"beginTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "dataScopeSql": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SqlSugarPagedList_WcsContainerDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/WcsContainerDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_WcsDeviceDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/WcsDeviceDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_WcsLaneDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/WcsLaneDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_WcsTaskDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/WcsTaskDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "SqlSugarPagedList_WcsWarehouseLocationDto": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/WcsWarehouseLocationDto"}, "nullable": true}, "code": {"type": "integer", "format": "int32"}, "hasPrevPages": {"type": "boolean"}, "hasNextPages": {"type": "boolean"}}, "additionalProperties": false}, "WcsContainerDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "containerId": {"type": "integer", "format": "int64", "nullable": true}, "containerName": {"type": "string", "nullable": true}, "containerCode": {"type": "string", "nullable": true}, "locationId": {"type": "integer", "format": "int64", "nullable": true}, "locationCode": {"type": "string", "nullable": true}, "currentAdrr": {"type": "string", "nullable": true}, "isVisibleFlag": {"type": "integer", "format": "int32"}, "createdBy": {"type": "integer", "format": "int64", "nullable": true}, "updatedBy": {"type": "integer", "format": "int64", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}, "updatedTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "WcsDeviceDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "deviceCode": {"type": "string", "nullable": true}, "deviceName": {"type": "string", "nullable": true}, "deviceType": {"type": "integer", "format": "int32", "nullable": true}, "addr": {"type": "string", "nullable": true}, "port": {"type": "integer", "format": "int32", "nullable": true}, "online": {"type": "integer", "format": "int32", "nullable": true}, "runStatus": {"type": "integer", "format": "int32", "nullable": true}, "deviceId": {"type": "integer", "format": "int64", "nullable": true}, "isVisibleFlag": {"type": "integer", "format": "int32", "nullable": true}, "createdBy": {"type": "integer", "format": "int64", "nullable": true}, "updatedBy": {"type": "integer", "format": "int64", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}, "updatedTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "WcsLaneDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "laneName": {"type": "string", "nullable": true}, "laneId": {"type": "integer", "format": "int64", "nullable": true}, "laneType": {"type": "integer", "format": "int32"}, "createdBy": {"type": "integer", "format": "int64", "nullable": true}, "updatedBy": {"type": "integer", "format": "int64", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}, "updatedTime": {"type": "string", "format": "date-time", "nullable": true}, "laneCode": {"type": "string", "nullable": true}, "deviceId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "WcsTaskDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "taskId": {"type": "integer", "format": "int64", "nullable": true}, "taskCode": {"type": "string", "nullable": true}, "binNo": {"type": "string", "nullable": true}, "equipmentNo": {"type": "string", "nullable": true}, "taskType": {"type": "integer", "format": "int32"}, "startLocation": {"type": "string", "nullable": true}, "endLocation": {"type": "string", "nullable": true}, "retries": {"type": "integer", "format": "int32", "nullable": true}, "maxRetries": {"type": "integer", "format": "int32", "nullable": true}, "errMsg": {"type": "string", "nullable": true}, "taskStatus": {"type": "integer", "format": "int32", "nullable": true}, "isVisibleFlag": {"type": "integer", "format": "int32", "nullable": true}, "createdBy": {"type": "integer", "format": "int64", "nullable": true}, "updatedBy": {"type": "integer", "format": "int64", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}, "updatedTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "WcsWarehouseLocationDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "shelves": {"type": "string", "nullable": true}, "x": {"type": "string", "nullable": true}, "y": {"type": "string", "nullable": true}, "z": {"type": "string", "nullable": true}, "layer": {"type": "integer", "format": "int32", "nullable": true}, "deep": {"type": "integer", "format": "int32", "nullable": true}, "locationId": {"type": "integer", "format": "int64", "nullable": true}, "locationName": {"type": "string", "nullable": true}, "locationCode": {"type": "string", "nullable": true}, "laneId": {"type": "integer", "format": "int64", "nullable": true}, "laneName": {"type": "string", "nullable": true}, "locationType": {"type": "integer", "format": "int32", "nullable": true}, "isVisibleFlag": {"type": "integer", "format": "int32", "nullable": true}, "createdBy": {"type": "integer", "format": "int64", "nullable": true}, "updatedBy": {"type": "integer", "format": "int64", "nullable": true}, "createdTime": {"type": "string", "format": "date-time", "nullable": true}, "updatedTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}
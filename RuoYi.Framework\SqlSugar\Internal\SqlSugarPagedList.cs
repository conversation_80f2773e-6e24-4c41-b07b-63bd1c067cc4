namespace SqlSugar;

/// <summary>
///   分页泛型集合
/// </summary>
public class SqlSugarPagedList<TEntity>
  where TEntity : new()
{
  /// <summary>
  ///   页码
  /// </summary>
  public int PageIndex { get; set; }

  /// <summary>
  ///   页容量
  /// </summary>
  public int PageSize { get; set; }

  /// <summary>
  ///   总条数
  /// </summary>
  public int Total { get; set; }

  ///// <summary>
  ///// 总页数
  ///// </summary>
  //public int Total { get; set; }

  /// <summary>
  ///   当前页集合
  /// </summary>
  public IEnumerable<TEntity> Rows { get; set; }

  /// <summary>
  ///   消息状态码
  /// </summary>
  public int Code { get; set; }

  /// <summary>
  ///   是否有上一页
  /// </summary>
  public bool HasPrevPages { get; set; }

  /// <summary>
  ///   是否有下一页
  /// </summary>
  public bool HasNextPages { get; set; }
}

﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace RuoYi.Framework.ConfigurableOptions;

/// <summary>
///   应用选项依赖接口
/// </summary>
public interface IConfigurableOptions
{
}

/// <summary>
///   选项后期配置
/// </summary>
/// <typeparam name="TOptions"></typeparam>
public interface IConfigurableOptions<TOptions> : IConfigurableOptions
  where TOptions : class, IConfigurableOptions
{
  /// <summary>
  ///   选项后期配置
  /// </summary>
  /// <param name="options"></param>
  /// <param name="configuration"></param>
  void PostConfigure(TOptions options, IConfiguration configuration);
}

/// <summary>
///   带验证的应用选项依赖接口
/// </summary>
/// <typeparam name="TOptions"></typeparam>
/// <typeparam name="TOptionsValidation"></typeparam>
public interface IConfigurableOptions<TOptions, TOptionsValidation> : IConfigurableOptions<TOptions>
  where TOptions : class, IConfigurableOptions
  where TOptionsValidation : class, IValidateOptions<TOptions>
{
}

/// <summary>
///   带监听的应用选项依赖接口
/// </summary>
/// <typeparam name="TOptions"></typeparam>
public interface IConfigurableOptionsListener<TOptions> : IConfigurableOptions<TOptions>
  where TOptions : class, IConfigurableOptions
{
  /// <summary>
  ///   监听
  /// </summary>
  /// <param name="options"></param>
  /// <param name="configuration"></param>
  void OnListener(TOptions options, IConfiguration configuration);
}

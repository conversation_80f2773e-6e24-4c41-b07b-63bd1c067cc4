﻿using RuoYi.Framework.JsonSerialization;

namespace Newtonsoft.Json;

/// <summary>
///   Newtonsoft.Json 拓展
/// </summary>
[SuppressSniffer]
public static class NewtonsoftJsonExtensions
{
  /// <summary>
  ///   添加 DateTime/DateTime?/DateTimeOffset/DateTimeOffset? 类型序列化处理
  /// </summary>
  /// <param name="converters"></param>
  /// <param name="outputFormat"></param>
  /// <param name="localized">自动转换 DateTimeOffset 为当地时间</param>
  /// <returns></returns>
  public static IList<JsonConverter> AddDateTimeTypeConverters(this IList<JsonConverter> converters,
    string outputFormat = "yyyy-MM-dd HH:mm:ss", bool localized = false)
  {
    converters.Add(new NewtonsoftJsonDateTimeJsonConverter(outputFormat));
    converters.Add(new NewtonsoftNullableJsonDateTimeJsonConverter(outputFormat));

    converters.Add(new NewtonsoftJsonDateTimeOffsetJsonConverter(outputFormat, localized));
    converters.Add(new NewtonsoftJsonNullableDateTimeOffsetJsonConverter(outputFormat, localized));

    return converters;
  }

  /// <summary>
  ///   添加 long/long? 类型序列化处理
  /// </summary>
  /// <param name="converters"></param>
  /// <param name="overMaxLengthOf17">是否超过最大长度 17 再处理</param>
  /// <remarks></remarks>
  public static IList<JsonConverter> AddLongTypeConverters(this IList<JsonConverter> converters, bool overMaxLengthOf17 = false)
  {
    converters.Add(new NewtonsoftJsonLongToStringJsonConverter(overMaxLengthOf17));
    converters.Add(new NewtonsoftJsonNullableLongToStringJsonConverter(overMaxLengthOf17));

    return converters;
  }

  /// <summary>
  ///   添加 DateOnly/DateOnly? 类型序列化处理
  /// </summary>
  /// <param name="converters"></param>
  /// <returns></returns>
  public static IList<JsonConverter> AddDateOnlyConverters(this IList<JsonConverter> converters)
  {
#if !NET5_0
    converters.Add(new NewtonsoftJsonDateOnlyJsonConverter());
    converters.Add(new NewtonsoftJsonNullableDateOnlyJsonConverter());
#endif
    return converters;
  }

  /// <summary>
  ///   添加 TimeOnly/TimeOnly? 类型序列化处理
  /// </summary>
  /// <param name="converters"></param>
  /// <returns></returns>
  public static IList<JsonConverter> AddTimeOnlyConverters(this IList<JsonConverter> converters)
  {
#if !NET5_0
    converters.Add(new NewtonsoftJsonTimeOnlyJsonConverter());
    converters.Add(new NewtonsoftJsonNullableTimeOnlyJsonConverter());
#endif
    return converters;
  }
}

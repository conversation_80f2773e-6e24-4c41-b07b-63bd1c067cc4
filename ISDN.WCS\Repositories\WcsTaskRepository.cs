using RuoYi.Data;
using RuoYi.Common.Data;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using SqlSugar;

namespace ISDN.WCS.Repositories
{
    /// <summary>
    ///  WCS任务档 Repository
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    public class WcsTaskRepository : BaseRepository<WcsTask, WcsTaskDto>
    {
        public WcsTaskRepository(ISqlSugarRepository<WcsTask> sqlSugarRepository)
        {
            Repo = sqlSugarRepository;
        }

        /// <summary>
        /// 构造条件查询器
        /// </summary>
        /// <param name="dto">domain</param>
        /// <returns></returns>
        public override ISugarQueryable<WcsTask> Queryable(WcsTaskDto dto)
        {
            return Repo.AsQueryable()
                            .WhereIF(dto.TaskType > 0, (t) => t.TaskType == dto.TaskType)
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.StartLocation), (t) => t.StartLocation == dto.StartLocation)
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.EndLocation), (t) => t.EndLocation == dto.EndLocation)
                            .WhereIF(dto.Retries > 0, (t) => t.Retries == dto.Retries)
                            .WhereIF(dto.MaxRetries > 0, (t) => t.MaxRetries == dto.MaxRetries)
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.ErrMsg), (t) => t.ErrMsg == dto.ErrMsg)
                            .WhereIF(dto.TaskStatus > 0, (t) => t.TaskStatus == dto.TaskStatus)
                            .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
;
        }

        /// <summary>
        /// 构造条件查询器
        /// </summary>
        /// <param name="dto">domain</param>
        /// <returns></returns>
        public override ISugarQueryable<WcsTaskDto> DtoQueryable(WcsTaskDto dto)
        {
            return Repo.AsQueryable()
                        .WhereIF(dto.TaskType > 0, (t) => t.TaskType == dto.TaskType)
                        .WhereIF(!string.IsNullOrWhiteSpace(dto.StartLocation), (t) => t.StartLocation == dto.StartLocation)
                        .WhereIF(!string.IsNullOrWhiteSpace(dto.EndLocation), (t) => t.EndLocation == dto.EndLocation)
                        .WhereIF(dto.Retries > 0, (t) => t.Retries == dto.Retries)
                        .WhereIF(dto.MaxRetries > 0, (t) => t.MaxRetries == dto.MaxRetries)
                        .WhereIF(!string.IsNullOrWhiteSpace(dto.ErrMsg), (t) => t.ErrMsg == dto.ErrMsg)
                        .WhereIF(dto.TaskStatus > 0, (t) => t.TaskStatus == dto.TaskStatus)
                        .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
            .Select((t) => new WcsTaskDto
            {
                TaskId = t.TaskId
            }, true);
        }
    }
}

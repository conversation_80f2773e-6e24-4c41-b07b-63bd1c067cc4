[{"ContainingType": "RuoYi.Admin.SampleController", "Method": "Get", "RelativePath": "{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Data.Slave.Dtos.SlaveSysUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.CaptchaController", "Method": "GetCaptchaImage", "RelativePath": "captchaImage", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RuoYi.Common.Controllers.CommonController", "Method": "UploadFile", "RelativePath": "common/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.IndexController", "Method": "GetDescription", "RelativePath": "GetDescription", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.SysLoginController", "Method": "GetInfo", "RelativePath": "getInfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.SysLoginController", "Method": "GetRouters", "RelativePath": "getRouters", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.SampleController", "Method": "GetWithPerminAndRole", "RelativePath": "getWithPerminAndRole/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Data.Slave.Dtos.SlaveSysUserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.SampleController", "Method": "IpRateLimit", "RelativePath": "ipRateLimit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.SysLoginController", "Method": "<PERSON><PERSON>", "RelativePath": "login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginBody", "Type": "RuoYi.Data.Models.LoginBody", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.SysLoginController", "Method": "Logout", "RelativePath": "logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.CacheController", "Method": "GetCacheInfo", "RelativePath": "monitor/cache", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.CacheController", "Method": "ClearCacheAll", "RelativePath": "monitor/cache/clearCacheAll", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.CacheController", "Method": "ClearCache<PERSON>ey", "RelativePath": "monitor/cache/clearCacheKey/{cacheKey}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cache<PERSON>ey", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.CacheController", "Method": "ClearCacheName", "RelativePath": "monitor/cache/clearCacheName/{cacheName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "cacheName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.CacheController", "Method": "GetCache<PERSON><PERSON>s", "RelativePath": "monitor/cache/getKeys/{cacheName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "cacheName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.CacheController", "Method": "GetCacheNames", "RelativePath": "monitor/cache/getNames", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.CacheController", "Method": "GetCacheValue", "RelativePath": "monitor/cache/getValue/{cacheName}/{cacheKey}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "cacheName", "Type": "System.String", "IsRequired": true}, {"Name": "cache<PERSON>ey", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "Get", "RelativePath": "monitor/job", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "jobId", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "Add", "RelativePath": "monitor/job", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "job", "Type": "RuoYi.Quartz.Dtos.SysJobDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "Edit", "RelativePath": "monitor/job", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "job", "Type": "RuoYi.Quartz.Dtos.SysJobDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "Get", "RelativePath": "monitor/job/{jobId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "jobId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "Remove", "RelativePath": "monitor/job/{jobIds}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "jobIds", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "ChangeStatus", "RelativePath": "monitor/job/changeStatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Quartz.Dtos.SysJobDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "Export", "RelativePath": "monitor/job/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "JobId", "Type": "System.Int64", "IsRequired": false}, {"Name": "JobName", "Type": "System.String", "IsRequired": false}, {"Name": "JobGroup", "Type": "System.String", "IsRequired": false}, {"Name": "InvokeTarget", "Type": "System.String", "IsRequired": false}, {"Name": "CronExpression", "Type": "System.String", "IsRequired": false}, {"Name": "MisfirePolicy", "Type": "System.String", "IsRequired": false}, {"Name": "MisfirePolicyDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Concurrent", "Type": "System.String", "IsRequired": false}, {"Name": "ConcurrentDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "GetSysJobPagedList", "RelativePath": "monitor/job/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "JobId", "Type": "System.Int64", "IsRequired": false}, {"Name": "JobName", "Type": "System.String", "IsRequired": false}, {"Name": "JobGroup", "Type": "System.String", "IsRequired": false}, {"Name": "InvokeTarget", "Type": "System.String", "IsRequired": false}, {"Name": "CronExpression", "Type": "System.String", "IsRequired": false}, {"Name": "MisfirePolicy", "Type": "System.String", "IsRequired": false}, {"Name": "MisfirePolicyDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Concurrent", "Type": "System.String", "IsRequired": false}, {"Name": "ConcurrentDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Quartz.Dtos.SysJobDto, RuoYi.Quartz, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobController", "Method": "Run", "RelativePath": "monitor/job/run", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Quartz.Dtos.SysJobDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "Get", "RelativePath": "monitor/jobLog", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "Add", "RelativePath": "monitor/jobLog", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Quartz.Dtos.SysJobLogDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "Edit", "RelativePath": "monitor/jobLog", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Quartz.Dtos.SysJobLogDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "Get", "RelativePath": "monitor/jobLog/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "Remove", "RelativePath": "monitor/jobLog/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "Clean", "RelativePath": "monitor/jobLog/clean", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "Export", "RelativePath": "monitor/jobLog/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "JobLogId", "Type": "System.Int64", "IsRequired": false}, {"Name": "JobName", "Type": "System.String", "IsRequired": false}, {"Name": "JobGroup", "Type": "System.String", "IsRequired": false}, {"Name": "InvokeTarget", "Type": "System.String", "IsRequired": false}, {"Name": "JobMessage", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "ExceptionInfo", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Quartz.Controllers.SysJobLogController", "Method": "GetSysJobLogPagedList", "RelativePath": "monitor/jobLog/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "JobLogId", "Type": "System.Int64", "IsRequired": false}, {"Name": "JobName", "Type": "System.String", "IsRequired": false}, {"Name": "JobGroup", "Type": "System.String", "IsRequired": false}, {"Name": "InvokeTarget", "Type": "System.String", "IsRequired": false}, {"Name": "JobMessage", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "ExceptionInfo", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Quartz.Dtos.SysJobLogDto, RuoYi.Quartz, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "Get", "RelativePath": "monitor/logininfor", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "Add", "RelativePath": "monitor/logininfor", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysLogininforDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "Edit", "RelativePath": "monitor/logininfor", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysLogininforDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "Get", "RelativePath": "monitor/logininfor/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "Remove", "RelativePath": "monitor/logininfor/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "Export", "RelativePath": "monitor/logininfor/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "InfoId", "Type": "System.Int64", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON>pad<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "LoginLocation", "Type": "System.String", "IsRequired": false}, {"Name": "Browser", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Msg", "Type": "System.String", "IsRequired": false}, {"Name": "LoginTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "Import", "RelativePath": "monitor/logininfor/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysLogininforController", "Method": "GetSysLogininforList", "RelativePath": "monitor/logininfor/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "InfoId", "Type": "System.Int64", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON>pad<PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "LoginLocation", "Type": "System.String", "IsRequired": false}, {"Name": "Browser", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Msg", "Type": "System.String", "IsRequired": false}, {"Name": "LoginTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysLogininforDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserOnlineController", "Method": "ForceLogout", "RelativePath": "monitor/online/{tokenId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "tokenId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserOnlineController", "Method": "GetSysOperLogList", "RelativePath": "monitor/online/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ipaddr", "Type": "System.String", "IsRequired": false}, {"Name": "userName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Models.SysUserOnline, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysOperLogController", "Method": "Remove", "RelativePath": "monitor/operlog/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysOperLogController", "Method": "Clean", "RelativePath": "monitor/operlog/clean", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysOperLogController", "Method": "Export", "RelativePath": "monitor/operlog/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "OperId", "Type": "System.Int64", "IsRequired": false}, {"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "BusinessType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "BusinessTypeDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "RequestMethod", "Type": "System.String", "IsRequired": false}, {"Name": "OperatorType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OperName", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "OperUrl", "Type": "System.String", "IsRequired": false}, {"Name": "OperIp", "Type": "System.String", "IsRequired": false}, {"Name": "OperLocation", "Type": "System.String", "IsRequired": false}, {"Name": "OperParam", "Type": "System.String", "IsRequired": false}, {"Name": "JsonResult", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Error<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "OperTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CostTime", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysOperLogController", "Method": "GetSysOperLogList", "RelativePath": "monitor/operlog/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "OperId", "Type": "System.Int64", "IsRequired": false}, {"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "BusinessType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "BusinessTypeDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "RequestMethod", "Type": "System.String", "IsRequired": false}, {"Name": "OperatorType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OperName", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "OperUrl", "Type": "System.String", "IsRequired": false}, {"Name": "OperIp", "Type": "System.String", "IsRequired": false}, {"Name": "OperLocation", "Type": "System.String", "IsRequired": false}, {"Name": "OperParam", "Type": "System.String", "IsRequired": false}, {"Name": "JsonResult", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Error<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "OperTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CostTime", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysOperLogDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.ServerController", "Method": "GetServerInfo", "RelativePath": "monitor/server", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Admin.SampleController", "Method": "RateLimit", "RelativePath": "rateLimit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRegisterController", "Method": "Register", "RelativePath": "register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "RuoYi.Data.Models.RegisterBody", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "Add", "RelativePath": "system/config", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysConfigDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "Edit", "RelativePath": "system/config", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysConfigDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "Remove", "RelativePath": "system/config/{configIds}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "configIds", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "Get", "RelativePath": "system/config/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "GetConfigKey", "RelativePath": "system/config/configKey/{configKey}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "config<PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "Export", "RelativePath": "system/config/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ConfigId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ConfigName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigValue", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigType", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigTypeDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "GetSysConfigList", "RelativePath": "system/config/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ConfigId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ConfigName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigValue", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigType", "Type": "System.String", "IsRequired": false}, {"Name": "ConfigTypeDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysConfigDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysConfigController", "Method": "RefreshCache", "RelativePath": "system/config/refreshCache", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDeptController", "Method": "Add", "RelativePath": "system/dept", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dept", "Type": "RuoYi.Data.Dtos.SysDeptDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDeptController", "Method": "Edit", "RelativePath": "system/dept", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dept", "Type": "RuoYi.Data.Dtos.SysDeptDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDeptController", "Method": "Get", "RelativePath": "system/dept/{deptId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deptId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDeptController", "Method": "Remove", "RelativePath": "system/dept/{deptId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "deptId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDeptController", "Method": "GetSysDeptList", "RelativePath": "system/dept/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ParentId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Ancestors", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Leader", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "Children", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysDeptDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ParentIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDeptController", "Method": "ExcludeChildList", "RelativePath": "system/dept/list/exclude/{deptId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictDataController", "Method": "Add", "RelativePath": "system/dict/data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysDictDataDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictDataController", "Method": "Edit", "RelativePath": "system/dict/data", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysDictDataDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictDataController", "Method": "Get", "RelativePath": "system/dict/data/{dictCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dictCode", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictDataController", "Method": "Remove", "RelativePath": "system/dict/data/{dictCodes}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "dictCodes", "Type": "System.Int64[]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictDataController", "Method": "Export", "RelativePath": "system/dict/data/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "DictCode", "Type": "System.Int64", "IsRequired": false}, {"Name": "DictSort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DictLabel", "Type": "System.String", "IsRequired": false}, {"Name": "DictValue", "Type": "System.String", "IsRequired": false}, {"Name": "DictType", "Type": "System.String", "IsRequired": false}, {"Name": "CssClass", "Type": "System.String", "IsRequired": false}, {"Name": "ListClass", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "IsDefaultDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictDataController", "Method": "GetSysDictDataList", "RelativePath": "system/dict/data/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DictCode", "Type": "System.Int64", "IsRequired": false}, {"Name": "DictSort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DictLabel", "Type": "System.String", "IsRequired": false}, {"Name": "DictValue", "Type": "System.String", "IsRequired": false}, {"Name": "DictType", "Type": "System.String", "IsRequired": false}, {"Name": "CssClass", "Type": "System.String", "IsRequired": false}, {"Name": "ListClass", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "IsDefaultDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysDictDataDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictDataController", "Method": "GetListByDictType", "RelativePath": "system/dict/data/type/{dictType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dictType", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "Add", "RelativePath": "system/dict/type", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysDictTypeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "Edit", "RelativePath": "system/dict/type", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysDictTypeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "Get", "RelativePath": "system/dict/type/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "Remove", "RelativePath": "system/dict/type/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Int64[]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "Export", "RelativePath": "system/dict/type/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "DictId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DictName", "Type": "System.String", "IsRequired": false}, {"Name": "DictType", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "GetSysDictTypeList", "RelativePath": "system/dict/type/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DictId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DictName", "Type": "System.String", "IsRequired": false}, {"Name": "DictType", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysDictTypeDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "OptionSelect", "RelativePath": "system/dict/type/optionselect", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysDictTypeController", "Method": "RefreshCache", "RelativePath": "system/dict/type/refreshCache", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysMenuController", "Method": "Add", "RelativePath": "system/menu", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "menu", "Type": "RuoYi.Data.Dtos.SysMenuDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysMenuController", "Method": "Edit", "RelativePath": "system/menu", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "menu", "Type": "RuoYi.Data.Dtos.SysMenuDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysMenuController", "Method": "Get", "RelativePath": "system/menu/{menuId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysMenuController", "Method": "Remove", "RelativePath": "system/menu/{menuId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "menuId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysMenuController", "Method": "SysMenuListAsync", "RelativePath": "system/menu/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "MenuId", "Type": "System.Int64", "IsRequired": false}, {"Name": "MenuName", "Type": "System.String", "IsRequired": false}, {"Name": "ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "ParentId", "Type": "System.Int64", "IsRequired": false}, {"Name": "OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Path", "Type": "System.String", "IsRequired": false}, {"Name": "Component", "Type": "System.String", "IsRequired": false}, {"Name": "Query", "Type": "System.String", "IsRequired": false}, {"Name": "IsFrame", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "MenuType", "Type": "System.String", "IsRequired": false}, {"Name": "Visible", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Perms", "Type": "System.String", "IsRequired": false}, {"Name": "Icon", "Type": "System.String", "IsRequired": false}, {"Name": "RoleStatus", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.Int64", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Int64", "IsRequired": false}, {"Name": "MenuTypes", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysMenuController", "Method": "RoleMenuTreeselectAsync", "RelativePath": "system/menu/roleMenuTreeselect/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysMenuController", "Method": "Treeselect", "RelativePath": "system/menu/treeselect", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "MenuId", "Type": "System.Int64", "IsRequired": false}, {"Name": "MenuName", "Type": "System.String", "IsRequired": false}, {"Name": "ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "ParentId", "Type": "System.Int64", "IsRequired": false}, {"Name": "OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Path", "Type": "System.String", "IsRequired": false}, {"Name": "Component", "Type": "System.String", "IsRequired": false}, {"Name": "Query", "Type": "System.String", "IsRequired": false}, {"Name": "IsFrame", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "MenuType", "Type": "System.String", "IsRequired": false}, {"Name": "Visible", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Perms", "Type": "System.String", "IsRequired": false}, {"Name": "Icon", "Type": "System.String", "IsRequired": false}, {"Name": "RoleStatus", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.Int64", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Int64", "IsRequired": false}, {"Name": "MenuTypes", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysNoticeController", "Method": "Add", "RelativePath": "system/notice", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysNoticeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysNoticeController", "Method": "Edit", "RelativePath": "system/notice", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysNoticeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysNoticeController", "Method": "Get", "RelativePath": "system/notice/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysNoticeController", "Method": "Remove", "RelativePath": "system/notice/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysNoticeController", "Method": "GetSysNoticeList", "RelativePath": "system/notice/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "NoticeId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "NoticeTitle", "Type": "System.String", "IsRequired": false}, {"Name": "NoticeType", "Type": "System.String", "IsRequired": false}, {"Name": "NoticeContent", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysNoticeDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysPostController", "Method": "Add", "RelativePath": "system/post", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "post", "Type": "RuoYi.Data.Dtos.SysPostDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysPostController", "Method": "Edit", "RelativePath": "system/post", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "post", "Type": "RuoYi.Data.Dtos.SysPostDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysPostController", "Method": "Get", "RelativePath": "system/post/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysPostController", "Method": "Remove", "RelativePath": "system/post/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysPostController", "Method": "Export", "RelativePath": "system/post/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "PostId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PostCode", "Type": "System.String", "IsRequired": false}, {"Name": "PostCodeLike", "Type": "System.String", "IsRequired": false}, {"Name": "PostName", "Type": "System.String", "IsRequired": false}, {"Name": "PostNameLike", "Type": "System.String", "IsRequired": false}, {"Name": "PostSort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysPostController", "Method": "GetSysPostList", "RelativePath": "system/post/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PostId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PostCode", "Type": "System.String", "IsRequired": false}, {"Name": "PostCodeLike", "Type": "System.String", "IsRequired": false}, {"Name": "PostName", "Type": "System.String", "IsRequired": false}, {"Name": "PostNameLike", "Type": "System.String", "IsRequired": false}, {"Name": "PostSort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysPostDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysPostController", "Method": "OptionSelect", "RelativePath": "system/post/optionselect", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "Add", "RelativePath": "system/role", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "RuoYi.Data.Dtos.SysRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "Edit", "RelativePath": "system/role", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "RuoYi.Data.Dtos.SysRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "Get", "RelativePath": "system/role/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "Remove", "RelativePath": "system/role/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "GetAllocatedList", "RelativePath": "system/role/authUser/allocatedList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Phonenumber", "Type": "System.String", "IsRequired": false}, {"Name": "Sex", "Type": "System.String", "IsRequired": false}, {"Name": "SexDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Avatar", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "LoginIp", "Type": "System.String", "IsRequired": false}, {"Name": "LoginDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Dept.DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Ancestors", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Leader", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Email", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Status", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Children", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysDeptDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Roles", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysRoleDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "RoleIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PostIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsAllocated", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysUserDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "CancelAuthUser", "RelativePath": "system/role/authUser/cancel", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "RuoYi.Data.Dtos.SysUserRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "CancelAuthUserBath", "RelativePath": "system/role/authUser/cancelAll", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Int64", "IsRequired": false}, {"Name": "UserIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Int64", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "SaveAuthUserAll", "RelativePath": "system/role/authUser/selectAll", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Int64", "IsRequired": false}, {"Name": "UserIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Int64", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "GetUnallocatedList", "RelativePath": "system/role/authUser/unallocatedList", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Phonenumber", "Type": "System.String", "IsRequired": false}, {"Name": "Sex", "Type": "System.String", "IsRequired": false}, {"Name": "SexDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Avatar", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "LoginIp", "Type": "System.String", "IsRequired": false}, {"Name": "LoginDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Dept.DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Ancestors", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Leader", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Email", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Status", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Children", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysDeptDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Roles", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysRoleDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "RoleIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PostIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsAllocated", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysUserDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "ChangeStatus", "RelativePath": "system/role/changeStatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "RuoYi.Data.Dtos.SysRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "SaveDataScope", "RelativePath": "system/role/dataScope", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "RuoYi.Data.Dtos.SysRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "GetDeptTree", "RelativePath": "system/role/deptTree/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "Export", "RelativePath": "system/role/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "RoleId", "Type": "System.Int64", "IsRequired": false}, {"Name": "RoleName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "RoleSort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DataScope", "Type": "System.String", "IsRequired": false}, {"Name": "DataScopeDesc", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "Flag", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MenuIds", "Type": "System.Int64[]", "IsRequired": false}, {"Name": "DeptIds", "Type": "System.Int64[]", "IsRequired": false}, {"Name": "Permissions", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "GetSysRoleList", "RelativePath": "system/role/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RoleId", "Type": "System.Int64", "IsRequired": false}, {"Name": "RoleName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "RoleSort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DataScope", "Type": "System.String", "IsRequired": false}, {"Name": "DataScopeDesc", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "Flag", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MenuIds", "Type": "System.Int64[]", "IsRequired": false}, {"Name": "DeptIds", "Type": "System.Int64[]", "IsRequired": false}, {"Name": "Permissions", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Dtos.SysRoleDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysRoleController", "Method": "OptionSelect", "RelativePath": "system/role/optionselect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "GetInfo", "RelativePath": "system/user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "Add", "RelativePath": "system/user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "RuoYi.Data.Dtos.SysUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "Edit", "RelativePath": "system/user", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "RuoYi.Data.Dtos.SysUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "Remove", "RelativePath": "system/user/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "GetInfo", "RelativePath": "system/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "InsertAuthRole", "RelativePath": "system/user/authRole", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int64", "IsRequired": false}, {"Name": "roleIds", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "GetAuthRole", "RelativePath": "system/user/authRole/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "ChangeStatus", "RelativePath": "system/user/changeStatus", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "RuoYi.Data.Dtos.SysUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "GetDeptTree", "RelativePath": "system/user/deptTree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ParentId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Ancestors", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Leader", "Type": "System.String", "IsRequired": false}, {"Name": "Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "Children", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysDeptDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ParentIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "Export", "RelativePath": "system/user/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Phonenumber", "Type": "System.String", "IsRequired": false}, {"Name": "Sex", "Type": "System.String", "IsRequired": false}, {"Name": "SexDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Avatar", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "LoginIp", "Type": "System.String", "IsRequired": false}, {"Name": "LoginDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Dept.DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Ancestors", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Leader", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Email", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Status", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Children", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysDeptDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Roles", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysRoleDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "RoleIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PostIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsAllocated", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "Import", "RelativePath": "system/user/importData", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "updateSupport", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "DownloadImportTemplate", "RelativePath": "system/user/importTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "GetUserList", "RelativePath": "system/user/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Phonenumber", "Type": "System.String", "IsRequired": false}, {"Name": "Sex", "Type": "System.String", "IsRequired": false}, {"Name": "SexDesc", "Type": "System.String", "IsRequired": false}, {"Name": "Avatar", "Type": "System.String", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StatusDesc", "Type": "System.String", "IsRequired": false}, {"Name": "DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "LoginIp", "Type": "System.String", "IsRequired": false}, {"Name": "LoginDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Dept.DeptId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Ancestors", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.OrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Leader", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Phone", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Email", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Status", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.DelFlag", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.ParentName", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Children", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysDeptDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "Dept.<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.ParentIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Dept.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Dept.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "DeptName", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Roles", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.SysRoleDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "RoleIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PostIds", "Type": "System.Collections.Generic.List`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RoleId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsAllocated", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Entities.SysUser, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysProfileController", "Method": "GetProfile", "RelativePath": "system/user/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysProfileController", "Method": "UpdateProfile", "RelativePath": "system/user/profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "RuoYi.Data.Dtos.SysUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysProfileController", "Method": "UploadAvatar", "RelativePath": "system/user/profile/avatar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RuoYi.System.Controllers.SysProfileController", "Method": "UpdatePwd", "RelativePath": "system/user/profile/updatePwd", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "oldPassword", "Type": "System.String", "IsRequired": false}, {"Name": "newPassword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.System.Controllers.SysUserController", "Method": "ResetPwd", "RelativePath": "system/user/resetPwd", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "RuoYi.Data.Dtos.SysUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.TestController", "Method": "CreateWcsTaskTest", "RelativePath": "test/CreateWcsTaskTest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "BinNo", "Type": "System.String", "IsRequired": false}, {"Name": "TaskType", "Type": "System.Int32", "IsRequired": false}, {"Name": "StartLocation", "Type": "System.String", "IsRequired": false}, {"Name": "EndLocation", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ISDN.WCS.Data.Dtos.ResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "Edit", "RelativePath": "tool/Gen", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "genTable", "Type": "RuoYi.Data.Dtos.GenTableDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "GetTableInfo", "RelativePath": "tool/Gen/{tableId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "Remove", "RelativePath": "tool/Gen/{tableIds}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableIds", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "DownloadBatch", "RelativePath": "tool/Gen/batchGenCode", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tables", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "ColumnList", "RelativePath": "tool/Gen/column/{tableId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Entities.GenTableColumn, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "TableList", "RelativePath": "tool/Gen/db/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TableId", "Type": "System.Int64", "IsRequired": false}, {"Name": "TableName", "Type": "System.String", "IsRequired": false}, {"Name": "TableComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTableFkName", "Type": "System.String", "IsRequired": false}, {"Name": "ClassName", "Type": "System.String", "IsRequired": false}, {"Name": "TplWebType", "Type": "System.String", "IsRequired": false}, {"Name": "TplCategory", "Type": "System.String", "IsRequired": false}, {"Name": "PackageName", "Type": "System.String", "IsRequired": false}, {"Name": "ModuleName", "Type": "System.String", "IsRequired": false}, {"Name": "BusinessName", "Type": "System.String", "IsRequired": false}, {"Name": "FunctionName", "Type": "System.String", "IsRequired": false}, {"Name": "FunctionAuthor", "Type": "System.String", "IsRequired": false}, {"Name": "GenType", "Type": "System.String", "IsRequired": false}, {"Name": "GenPath", "Type": "System.String", "IsRequired": false}, {"Name": "Options", "Type": "System.String", "IsRequired": false}, {"Name": "TreeCode", "Type": "System.String", "IsRequired": false}, {"Name": "TreeParentCode", "Type": "System.String", "IsRequired": false}, {"Name": "TreeName", "Type": "System.String", "IsRequired": false}, {"Name": "ParentMenuId", "Type": "System.String", "IsRequired": false}, {"Name": "ParentMenuName", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.ColumnId", "Type": "System.Int64", "IsRequired": false}, {"Name": "PkColumn.TableId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.ColumnType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.NetType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.NetField", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsPk", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsIncrement", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsRequired", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsInsert", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsEdit", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsList", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsQuery", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.HtmlType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.DictType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.Sort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TableId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.TableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TableComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTableFkName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ClassName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TplWebType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TplCategory", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PackageName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ModuleName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.BusinessName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.FunctionName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.FunctionAuthor", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.GenType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.GenPath", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.Options", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TreeCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TreeParentCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TreeName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ParentMenuId", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ParentMenuName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.PkColumn.TableId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.NetType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.NetField", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsPk", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsIncrement", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsRequired", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsInsert", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsEdit", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsList", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsQuery", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.HtmlType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.DictType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.Sort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TableId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.SubTable.TableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TableComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.SubTableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.SubTableFkName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ClassName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TplWebType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TplCategory", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PackageName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ModuleName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.BusinessName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.FunctionName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.FunctionAuthor", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.GenType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.GenPath", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.Options", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TreeCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TreeParentCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TreeName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ParentMenuId", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ParentMenuName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.TableId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.NetType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.NetField", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsPk", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsIncrement", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsRequired", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsInsert", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsEdit", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsList", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsQuery", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.HtmlType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.DictType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Sort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.SubTable", "Type": "RuoYi.Data.Dtos.GenTableDto", "IsRequired": false}, {"Name": "SubTable.SubTable.Columns", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.GenTableColumnDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "SubTable.SubTable.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.Columns", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.GenTableColumnDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "SubTable.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "Columns", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.GenTableColumnDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Entities.GenTable, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "GenCode", "RelativePath": "tool/Gen/genCode/{tableName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "ImportTable", "RelativePath": "tool/Gen/importTable", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tables", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "GenList", "RelativePath": "tool/Gen/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TableId", "Type": "System.Int64", "IsRequired": false}, {"Name": "TableName", "Type": "System.String", "IsRequired": false}, {"Name": "TableComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTableFkName", "Type": "System.String", "IsRequired": false}, {"Name": "ClassName", "Type": "System.String", "IsRequired": false}, {"Name": "TplWebType", "Type": "System.String", "IsRequired": false}, {"Name": "TplCategory", "Type": "System.String", "IsRequired": false}, {"Name": "PackageName", "Type": "System.String", "IsRequired": false}, {"Name": "ModuleName", "Type": "System.String", "IsRequired": false}, {"Name": "BusinessName", "Type": "System.String", "IsRequired": false}, {"Name": "FunctionName", "Type": "System.String", "IsRequired": false}, {"Name": "FunctionAuthor", "Type": "System.String", "IsRequired": false}, {"Name": "GenType", "Type": "System.String", "IsRequired": false}, {"Name": "GenPath", "Type": "System.String", "IsRequired": false}, {"Name": "Options", "Type": "System.String", "IsRequired": false}, {"Name": "TreeCode", "Type": "System.String", "IsRequired": false}, {"Name": "TreeParentCode", "Type": "System.String", "IsRequired": false}, {"Name": "TreeName", "Type": "System.String", "IsRequired": false}, {"Name": "ParentMenuId", "Type": "System.String", "IsRequired": false}, {"Name": "ParentMenuName", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.ColumnId", "Type": "System.Int64", "IsRequired": false}, {"Name": "PkColumn.TableId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.ColumnType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.NetType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.NetField", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsPk", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsIncrement", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsRequired", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsInsert", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsEdit", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsList", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.IsQuery", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.HtmlType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.DictType", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.Sort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "PkColumn.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PkColumn.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TableId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.TableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TableComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTableFkName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ClassName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TplWebType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TplCategory", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PackageName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ModuleName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.BusinessName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.FunctionName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.FunctionAuthor", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.GenType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.GenPath", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.Options", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TreeCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TreeParentCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.TreeName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ParentMenuId", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.ParentMenuName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.PkColumn.TableId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.ColumnType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.NetType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.NetField", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsPk", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsIncrement", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsRequired", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsInsert", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsEdit", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsList", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.IsQuery", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.HtmlType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.DictType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.Sort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.PkColumn.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.PkColumn.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TableId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.SubTable.TableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TableComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.SubTableName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.SubTableFkName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ClassName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TplWebType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TplCategory", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PackageName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ModuleName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.BusinessName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.FunctionName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.FunctionAuthor", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.GenType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.GenPath", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.Options", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TreeCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TreeParentCode", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.TreeName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ParentMenuId", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.ParentMenuName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnId", "Type": "System.Int64", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.TableId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnName", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnComment", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.ColumnType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.NetType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.NetField", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsPk", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsIncrement", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsRequired", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsInsert", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsEdit", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsList", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.IsQuery", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.QueryType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.HtmlType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.DictType", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Sort", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.PkColumn.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.SubTable", "Type": "RuoYi.Data.Dtos.GenTableDto", "IsRequired": false}, {"Name": "SubTable.SubTable.Columns", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.GenTableColumnDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "SubTable.SubTable.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.SubTable.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.SubTable.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.Columns", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.GenTableColumnDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "SubTable.CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.Remark", "Type": "System.String", "IsRequired": false}, {"Name": "SubTable.Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SubTable.Params.DataScopeSql", "Type": "System.String", "IsRequired": false}, {"Name": "Columns", "Type": "System.Collections.Generic.List`1[[RuoYi.Data.Dtos.GenTableColumnDto, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[RuoYi.Data.Entities.GenTable, RuoYi.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "Preview", "RelativePath": "tool/Gen/preview/{tableId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RuoYi.Generator.Controllers.GenController", "Method": "SyncDbAsync", "RelativePath": "tool/Gen/synchDb/{tableName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tableName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "Get", "RelativePath": "wcs/container", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "Add", "RelativePath": "wcs/container", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsContainerDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "Edit", "RelativePath": "wcs/container", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsContainerDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "Get", "RelativePath": "wcs/container/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "Remove", "RelativePath": "wcs/container/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "Export", "RelativePath": "wcs/container/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContainerId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ContainerName", "Type": "System.String", "IsRequired": false}, {"Name": "ContainerCode", "Type": "System.String", "IsRequired": false}, {"Name": "LocationId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LocationCode", "Type": "System.String", "IsRequired": false}, {"Name": "CurrentAdrr", "Type": "System.String", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Int32", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "Import", "RelativePath": "wcs/container/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsContainerController", "Method": "GetWcsContainerPagedList", "RelativePath": "wcs/container/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContainerId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ContainerName", "Type": "System.String", "IsRequired": false}, {"Name": "ContainerCode", "Type": "System.String", "IsRequired": false}, {"Name": "LocationId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LocationCode", "Type": "System.String", "IsRequired": false}, {"Name": "CurrentAdrr", "Type": "System.String", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Int32", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[ISDN.WCS.Data.Dtos.WcsContainerDto, ISDN.WCS, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Get", "RelativePath": "wcs/device", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Add", "RelativePath": "wcs/device", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsDeviceDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Edit", "RelativePath": "wcs/device", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsDeviceDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Get", "RelativePath": "wcs/device/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Remove", "RelativePath": "wcs/device/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Export", "RelativePath": "wcs/device/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceCode", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceName", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Addr", "Type": "System.String", "IsRequired": false}, {"Name": "Port", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Online", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RunStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeviceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Import", "RelativePath": "wcs/device/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "GetWcsDevicePagedList", "RelativePath": "wcs/device/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "DeviceCode", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceName", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Addr", "Type": "System.String", "IsRequired": false}, {"Name": "Port", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Online", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "RunStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DeviceId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[ISDN.WCS.Data.Dtos.WcsDeviceDto, ISDN.WCS, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "GetList", "RelativePath": "wcs/device/ListAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsDeviceController", "Method": "Online", "RelativePath": "wcs/device/online/{deviceId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "Get", "RelativePath": "wcs/lane", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "Add", "RelativePath": "wcs/lane", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsLaneDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "Edit", "RelativePath": "wcs/lane", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsLaneDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "Get", "RelativePath": "wcs/lane/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "Remove", "RelativePath": "wcs/lane/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "Export", "RelativePath": "wcs/lane/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "LaneName", "Type": "System.String", "IsRequired": false}, {"Name": "LaneId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LaneType", "Type": "System.Int32", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LaneCode", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceId", "Type": "System.Int64", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "Import", "RelativePath": "wcs/lane/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "GetWcsLanePagedList", "RelativePath": "wcs/lane/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "LaneName", "Type": "System.String", "IsRequired": false}, {"Name": "LaneId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LaneType", "Type": "System.Int32", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LaneCode", "Type": "System.String", "IsRequired": false}, {"Name": "DeviceId", "Type": "System.Int64", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[ISDN.WCS.Data.Dtos.WcsLaneDto, ISDN.WCS, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsLaneController", "Method": "GetList", "RelativePath": "wcs/lane/ListAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "Get", "RelativePath": "wcs/location", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "Add", "RelativePath": "wcs/location", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsWarehouseLocationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "Edit", "RelativePath": "wcs/location", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsWarehouseLocationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "Get", "RelativePath": "wcs/location/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "Remove", "RelativePath": "wcs/location/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "Export", "RelativePath": "wcs/location/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "X", "Type": "System.String", "IsRequired": false}, {"Name": "Y", "Type": "System.String", "IsRequired": false}, {"Name": "Z", "Type": "System.String", "IsRequired": false}, {"Name": "Layer", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Deep", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LocationId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LocationName", "Type": "System.String", "IsRequired": false}, {"Name": "LocationCode", "Type": "System.String", "IsRequired": false}, {"Name": "LaneId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LaneName", "Type": "System.String", "IsRequired": false}, {"Name": "LocationType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "Import", "RelativePath": "wcs/location/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsWarehouseLocationController", "Method": "GetWcsWarehouseLocationPagedList", "RelativePath": "wcs/location/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "X", "Type": "System.String", "IsRequired": false}, {"Name": "Y", "Type": "System.String", "IsRequired": false}, {"Name": "Z", "Type": "System.String", "IsRequired": false}, {"Name": "Layer", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Deep", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LocationId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LocationName", "Type": "System.String", "IsRequired": false}, {"Name": "LocationCode", "Type": "System.String", "IsRequired": false}, {"Name": "LaneId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LaneName", "Type": "System.String", "IsRequired": false}, {"Name": "LocationType", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[ISDN.WCS.Data.Dtos.WcsWarehouseLocationDto, ISDN.WCS, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Get", "RelativePath": "wcs/task", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": false}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Add", "RelativePath": "wcs/task", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsTaskDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Edit", "RelativePath": "wcs/task", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "ISDN.WCS.Data.Dtos.WcsTaskDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Get", "RelativePath": "wcs/task/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Remove", "RelativePath": "wcs/task/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Detail", "RelativePath": "wcs/task/detail/{taskId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskId", "Type": "System.Int64", "IsRequired": true}], "ReturnTypes": [{"Type": "RuoYi.Framework.AjaxResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Export", "RelativePath": "wcs/task/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "TaskId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TaskCode", "Type": "System.String", "IsRequired": false}, {"Name": "BinNo", "Type": "System.String", "IsRequired": false}, {"Name": "EquipmentNo", "Type": "System.String", "IsRequired": false}, {"Name": "TaskType", "Type": "System.Int32", "IsRequired": false}, {"Name": "StartLocation", "Type": "System.String", "IsRequired": false}, {"Name": "EndLocation", "Type": "System.String", "IsRequired": false}, {"Name": "Retries", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxRetries", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ErrMsg", "Type": "System.String", "IsRequired": false}, {"Name": "TaskStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "Import", "RelativePath": "wcs/task/import", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "ISDN.WCS.Controllers.WcsTaskController", "Method": "GetWcsTaskPagedList", "RelativePath": "wcs/task/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "TaskId", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TaskCode", "Type": "System.String", "IsRequired": false}, {"Name": "BinNo", "Type": "System.String", "IsRequired": false}, {"Name": "EquipmentNo", "Type": "System.String", "IsRequired": false}, {"Name": "TaskType", "Type": "System.Int32", "IsRequired": false}, {"Name": "StartLocation", "Type": "System.String", "IsRequired": false}, {"Name": "EndLocation", "Type": "System.String", "IsRequired": false}, {"Name": "Retries", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxRetries", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ErrMsg", "Type": "System.String", "IsRequired": false}, {"Name": "TaskStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsVisibleFlag", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedBy", "Type": "System.Nullable`1[[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdatedTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreateBy", "Type": "System.String", "IsRequired": false}, {"Name": "CreateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UpdateBy", "Type": "System.String", "IsRequired": false}, {"Name": "UpdateTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Remark", "Type": "System.String", "IsRequired": false}, {"Name": "Params.params[beginTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.params[endTime]", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Params.DataScopeSql", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SqlSugar.SqlSugarPagedList`1[[ISDN.WCS.Data.Dtos.WcsTaskDto, ISDN.WCS, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]
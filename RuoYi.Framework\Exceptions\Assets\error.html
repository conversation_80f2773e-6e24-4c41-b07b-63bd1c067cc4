<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <meta content="@{Description}" name="description"/>
    <title>@{Title}</title>
    <style>
        html,
        body {
            background-color: rgb(246, 246, 246);
            font-family: "Segoe UI", <PERSON><PERSON>, "Microsoft Yahei", sans-serif;
        }

        #frame-error {
            box-sizing: border-box;
            font-size: 1em;
            line-height: 1.6em;
            margin: 14vh auto 0;
            max-width: 600px;
            width: 100%;
            width: 600px;
        }

        .icon {
            user-select: none;
            -webkit-user-select: none;
            display: inline-block;
            height: 72px;
            margin: 0 0 40px;
            width: 72px;
            background-repeat: no-repeat;
            background-size: 100%;
        }

        .icon-page-error {
            content: url(@{Base64Icon});
        }

        h1 {
            color: #101010;
            font-size: 1.6em;
            font-weight: bold;
            line-height: 1.25em;
            margin-top: 0;
            word-wrap: break-word;
            margin-bottom: 22px;
        }

        p {
            font-size: 14px;
            line-height: 20px;
            color: #101010;
        }

        .error-code {
            display: block;
            font-size: 10px;
            line-height: 12px;
            color: #6f6f6f;
            margin-top: 24px;
            font-weight: 500;
            text-transform: uppercase;
        }

        summary {
            user-select: none;
            -webkit-user-select: none;
            cursor: pointer;
        }

        .code {
            border: 1px solid #e9e9e9;
            background: #f5f5f5;
            width: 100%;
            padding: 20px 10px 20px 0;
            position: relative;
            margin: 0;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .code-language {
            position: absolute;
            right: 0;
            bottom: 0;
            z-index: 10;
            font-family: Arial;
            padding: 3px;
            color: #999;
            font-size: 12px;
        }

        .code ol.code-area {
            list-style-type: decimal;
            margin: 0;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .code ol.code-area li {
            font-size: 12px;
            color: #999;
            padding: 0;
        }

        .code ol.code-area li:nth-child(2n + 1) {
            background: #f0f0f0;
        }

        .code ol.code-area li code {
            color: #000;
            line-height: 1.8;
            font-size: 13px;
            padding: 0 10px;
            font-family: Arial;
            display: block;
            white-space: nowrap;
        }
    </style>
</head>
<body>
<div id="frame-error">
    <div id="content">
        <div class="icon icon-page-error"></div>
        <div id="message">
            <h1>@{Title}</h1>
            <p>@{Description}</p>
            <details>
                <summary>See more details.</summary>
                <pre class="code" data-language="@{CodeLang}">
@{Code}</pre>
            </details>
            <div class="error-code">HTTP ERROR @{StatusCode}</div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var codes = document.querySelectorAll(".code");
    Array.prototype.forEach.call(codes, function (code, i) {
        var language = code.getAttribute("data-language");
        var html = code.innerHTML;
        if (
            language.toLowerCase() == "html" ||
            language.toLowerCase() == "xml"
        ) {
            html = html
                .replace(/&(?!#?[a-zA-Z0-9]+;)/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/'/g, "&#39;")
                .replace(/"/g, "&quot;");
        }
        code.innerHTML =
            '<ol class="code-area"><li><code>' +
            html.replace(/[\r\t\n]+/g, "</code></li><li><code>") +
            '</code></li></ol><span class="code-language">' +
            language +
            "</span>";
    });
</script>
</body>
</html>

using ISDN.WCS.Data.Dtos;
using ISDN.WCS.PLC.SocketService;

//using ISDN.Data.Dtos;
using ISDN.WCS.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Enums;
using RuoYi.Common.Utils;
using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;
using RuoYi.Framework;
using RuoYi.Framework.Extensions;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ISDN.WCS.Controllers
{
    /// <summary>
    /// WCS任务档
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    [ApiDescriptionSettings("Wcs")]
    [Route("wcs/task")]
    public class WcsTaskController : ControllerBase
    {
        private readonly ILogger<WcsTaskController> _logger;
        private readonly WcsTaskService _wcsTaskService;

        public WcsTaskController(ILogger<WcsTaskController> logger,
            WcsTaskService wcsTaskService)
        {
            _logger = logger;
            _wcsTaskService = wcsTaskService;
        }

        /// <summary>
        /// 查询WCS任务档列表
        /// </summary>
        [HttpGet("list")]
        [AppAuthorize("wcs:task:list")]
        public async Task<SqlSugarPagedList<WcsTaskDto>> GetWcsTaskPagedList([FromQuery] WcsTaskDto dto)
        {
            return await _wcsTaskService.GetDtoPagedListAsync(dto);
        }

        /// <summary>
        /// 获取 WCS任务档 详细信息
        /// </summary>
        [HttpGet("")]
        [HttpGet("{id}")]
        [AppAuthorize("wcs:task:query")]
        public async Task<AjaxResult> Get(long id)
        {
            var data = await _wcsTaskService.GetDtoAsync(id);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 新增 WCS任务档
        /// </summary>
        [HttpPost("")]
        [AppAuthorize("wcs:task:add")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "WCS任务档", BusinessType = BusinessType.INSERT)]
        public async Task<AjaxResult> Add([FromBody] WcsTaskDto dto)
        {
            var data = await _wcsTaskService.InsertAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 修改 WCS任务档
        /// </summary>
        [HttpPut("")]
        [AppAuthorize("wcs:task:edit")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "WCS任务档", BusinessType = BusinessType.UPDATE)]
        public async Task<AjaxResult> Edit([FromBody] WcsTaskDto dto)
        {
            var data = await _wcsTaskService.UpdateAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 删除 WCS任务档
        /// </summary>
        [HttpDelete("{ids}")]
        [AppAuthorize("wcs:task:remove")]
        [RuoYi.System.Log(Title = "WCS任务档", BusinessType = BusinessType.DELETE)]
        public async Task<AjaxResult> Remove(string ids)
        {
            var idList = ids.SplitToList<long>();
            var data = await _wcsTaskService.DeleteAsync(idList);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 导入 WCS任务档
        /// </summary>
        [HttpPost("import")]
        [AppAuthorize("wcs:task:import")]
        [RuoYi.System.Log(Title = "WCS任务档", BusinessType = BusinessType.IMPORT)]
        public async Task Import([Required] IFormFile file)
        {
            var stream = new MemoryStream();
            file.CopyTo(stream);
            var list = await ExcelUtils.ImportAsync<WcsTaskDto>(stream);
            await _wcsTaskService.ImportDtoBatchAsync(list);
        }

        /// <summary>
        /// 导出 WCS任务档
        /// </summary>
        [HttpPost("export")]
        [AppAuthorize("wcs:task:export")]
        [RuoYi.System.Log(Title = "WCS任务档", BusinessType = BusinessType.EXPORT)]
        public async Task Export(WcsTaskDto dto)
        {
            var list = await _wcsTaskService.GetDtoListAsync(dto);
            await ExcelUtils.ExportAsync(App.HttpContext.Response, list);
        }

        [HttpGet("detail/{taskId}")]
        [AppAuthorize("wcs:task:detail")]
        public async Task<AjaxResult> Detail(long taskId)
        {
            var data = await _wcsTaskService.GetTaskStepList(taskId);
            return AjaxResult.Success(data);

        }
    }

}

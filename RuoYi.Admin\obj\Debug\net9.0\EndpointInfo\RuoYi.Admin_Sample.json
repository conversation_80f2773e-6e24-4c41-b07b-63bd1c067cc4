{"openapi": "3.0.4", "info": {"title": "<PERSON><PERSON>", "version": "1.0.0"}, "paths": {"/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SlaveSysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SlaveSysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SlaveSysUserDto"}}}}}}}, "/getWithPerminAndRole/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "getWithPerminAndRole-id-Get", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SlaveSysUserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SlaveSysUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SlaveSysUserDto"}}}}}}}, "/rateLimit": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "rateLimit-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/ipRateLimit": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "ipRateLimit-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}}, "components": {"schemas": {"QueryParam": {"type": "object", "properties": {"beginTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "dataScopeSql": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SlaveSysUserDto": {"type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "userId": {"type": "integer", "format": "int64"}, "deptId": {"type": "integer", "format": "int64"}, "userName": {"type": "string", "nullable": true}, "nickName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "sex": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "loginIp": {"type": "string", "nullable": true}, "loginDate": {"type": "string", "format": "date-time"}, "dept": {"$ref": "#/components/schemas/SysDeptDto"}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/SysRoleDto"}, "nullable": true}, "roleIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "postIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "roleId": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "SysDeptDto": {"required": ["deptName", "orderNum"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "deptId": {"type": "integer", "format": "int64", "nullable": true}, "parentId": {"type": "integer", "format": "int64", "nullable": true}, "ancestors": {"type": "string", "nullable": true}, "deptName": {"maxLength": 30, "minLength": 1, "type": "string"}, "orderNum": {"type": "integer", "format": "int32"}, "leader": {"type": "string", "nullable": true}, "phone": {"maxLength": 30, "type": "string", "nullable": true}, "email": {"maxLength": 50, "type": "string", "format": "email", "nullable": true}, "status": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "parentName": {"type": "string", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/SysDeptDto"}, "nullable": true}, "deptCheckStrictly": {"type": "boolean", "nullable": true}, "roleId": {"type": "integer", "format": "int64", "nullable": true}, "parentIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}}, "additionalProperties": false}, "SysRoleDto": {"required": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "roleSort"], "type": "object", "properties": {"createBy": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "remark": {"type": "string", "nullable": true}, "params": {"$ref": "#/components/schemas/QueryParam"}, "roleId": {"type": "integer", "format": "int64"}, "roleName": {"maxLength": 30, "minLength": 1, "type": "string"}, "roleKey": {"maxLength": 100, "minLength": 1, "type": "string"}, "roleSort": {"type": "integer", "format": "int32"}, "dataScope": {"type": "string", "nullable": true}, "dataScopeDesc": {"type": "string", "nullable": true}, "menuCheckStrictly": {"type": "boolean", "nullable": true}, "deptCheckStrictly": {"type": "boolean", "nullable": true}, "status": {"type": "string", "nullable": true}, "statusDesc": {"type": "string", "nullable": true}, "delFlag": {"type": "string", "nullable": true}, "flag": {"type": "boolean", "nullable": true}, "menuIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "deptIds": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "userId": {"type": "integer", "format": "int64", "nullable": true}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}
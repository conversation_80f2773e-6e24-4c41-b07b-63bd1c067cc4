namespace ISDN.WCS.PLC.Dto;

/// <summary>
/// PLC任务执行状态DTO
/// </summary>
public class PLCTaskStatusDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public string TaskCode { get; set; }

    /// <summary>
    /// 容器号
    /// </summary>
    public string BinNo { get; set; }

    /// <summary>
    /// 任务类型
    /// 1: 上架
    /// 2: 下架
    /// </summary>
    public short TaskType { get; set; }

    /// <summary>
    /// 执行状态
    /// 1: 成功
    /// 2: 失败
    /// 3: 执行中
    /// </summary>
    public byte Status { get; set; }
}
using Mapster;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Data;
using RuoYi.Framework.DependencyInjection;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.Repositories;

namespace ISDN.WCS.Services
{
    /// <summary>
    ///  巷道管理 Service
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:42
    /// </summary>
    public class WcsLaneService : BaseService<WcsLane, WcsLaneDto>, ITransient
    {
        private readonly ILogger<WcsLaneService> _logger;
        private readonly WcsLaneRepository _wcsLaneRepository;

        public WcsLaneService(ILogger<WcsLaneService> logger,
            WcsLaneRepository wcsLaneRepository)
        {
            BaseRepo = wcsLaneRepository;

            _logger = logger;
            _wcsLaneRepository = wcsLaneRepository;
        }

        /// <summary>
        /// 查询 巷道管理 详情
        /// </summary>
        public async Task<WcsLane> GetAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.LaneId == id);
            return entity;
        }

        /// <summary>
        /// 查询 巷道管理 详情
        /// </summary>
        public async Task<WcsLaneDto> GetDtoAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.LaneId == id);
            var dto = entity.Adapt<WcsLaneDto>();
            // TODO 填充关联表数据
            return dto;
        }
    }
}

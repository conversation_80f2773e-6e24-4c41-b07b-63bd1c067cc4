﻿-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('@(Model.FunctionName)', '@(Model.ParentMenuId)', '1', '@(Model.businessName)', '@(Model.moduleName)/@(Model.businessName)/index', 1, 0, 'C', '0', '0', '@(Model.PermissionPrefix):list', '#', 'admin', getdate(), '', null, '@(Model.FunctionName)菜单');

-- 按钮父菜单ID
DECLARE @@parentId BIGINT = @@@@IDENTITY

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('@(Model.FunctionName)查询', @@parentId, '1',  '#', '', 1, 0, 'F', '0', '0', '@(Model.PermissionPrefix):query',        '#', 'admin', getdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('@(Model.FunctionName)新增', @@parentId, '2',  '#', '', 1, 0, 'F', '0', '0', '@(Model.PermissionPrefix):add',          '#', 'admin', getdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('@(Model.FunctionName)修改', @@parentId, '3',  '#', '', 1, 0, 'F', '0', '0', '@(Model.PermissionPrefix):edit',         '#', 'admin', getdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('@(Model.FunctionName)删除', @@parentId, '4',  '#', '', 1, 0, 'F', '0', '0', '@(Model.PermissionPrefix):remove',       '#', 'admin', getdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('@(Model.FunctionName)导出', @@parentId, '5',  '#', '', 1, 0, 'F', '0', '0', '@(Model.PermissionPrefix):export',       '#', 'admin', getdate(), '', null, '');
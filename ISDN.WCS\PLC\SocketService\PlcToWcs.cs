using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.PLC.Dto;
using ISDN.WCS.PLC.Socket;
using ISDN.WCS.Repositories;
using RuoYi.Framework;
using RuoYi.Framework.JsonSerialization;
using SkiaSharp;
using SqlSugar;
using SqlSugar.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ISDN.WCS.PLC.SocketService
{
    public class PlcToWcs
    {
        //private  ISqlSugarRepository<WcsTask> _sqlSugarRepository;
        //private  ISqlSugarRepository<WcsDevice> _wcsDeviceRepository;
        private readonly WcsToPlc _wcsToPlc;

        public PlcToWcs()
        {
            //_logger = logger;
            //// 如果没有提供wcsToPlc，则尝试从全局服务获取或创建新实例
            //_wcsToPlc = wcsToPlc ?? GetOrCreateWcsToPlc();
            _wcsToPlc = new WcsToPlc();
            //_sqlSugarRepository = App.GetService<ISqlSugarRepository<WcsTask>>();
            //_wcsDeviceRepository = App.GetService<ISqlSugarRepository<WcsDevice>>();

            RegisterDataHandlers();
        }
        private WcsToPlc GetOrCreateWcsToPlc()
        {
            try
            {
                // 尝试从全局服务获取WcsToPlc实例
                var existingInstance = App.GetService<WcsToPlc>();
                if (existingInstance != null)
                {
                    return existingInstance;
                }
                // 如果没有现有实例，则创建一个新的实例
                return new WcsToPlc();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取或创建WcsToPlc实例失败: {ex.Message}");
                // 在获取失败时返回null，后续操作需要进行空值检查
                return null;
            }
        }
        /// <summary>
        /// 数据处理委托
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        public delegate Task DataHandler(string clientId, string data);

        /// <summary>
        /// 数据处理器字典
        /// 用于根据数据类型路由到不同的处理方法
        /// </summary>
        public Dictionary<string, DataHandler> DataHandlers { get; private set; }

        /// <summary>
        /// 处理接收到的数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        public async Task ProcessReceivedData(string clientId, string data)
        {
            // 如果数据格式支持，尝试根据数据类型路由到特定的处理器
            // 这里假设数据格式为: "TYPE|DATA"，例如 "TASK|{...}" 或 "STATUS|{...}"
            int separatorIndex = data.IndexOf('|');
            if (separatorIndex > 0 && separatorIndex < data.Length - 1)
            {
                string dataType = data.Substring(0, separatorIndex).Trim();
                string dataContent = data.Substring(separatorIndex + 1);

                if (DataHandlers.TryGetValue(dataType, out DataHandler handler))
                {
                    await handler(clientId, dataContent);
                }
                else
                {
                    Console.WriteLine($"未找到数据类型 '{dataType}' 的处理器");
                }
            }
        }

        /// <summary>
        /// 注册PLC数据类型与对应处理方法的映射关系
        /// </summary>
        private void RegisterDataHandlers()
        {
            DataHandlers = new Dictionary<string, DataHandler>();
            DataHandlers["CT"] = HandleTaskStatusData;
            DataHandlers["ER"] = HandleEquipmentInfoData;
            DataHandlers["MW"] = HandleWeighingData;
            DataHandlers["MS"] = HandleSizeData;
            DataHandlers["PCL"] = HandleCheckpointData;
            DataHandlers["HEARTBEAT"] = HandleHeartbeat;
            DataHandlers["RAW_DATA"] = HandleRawData;
        }
        #region 数据处理方法

        /// <summary>
        /// 处理任务状态数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        private async Task HandleTaskStatusData(string clientId, string data)
        {
            try
            {
                var sugarClient = SqlSugarSetup.GetSqlSugarClient();
                var _sqlSugarRepository = new SqlSugarRepository<WcsTask>(sugarClient);
                var _wcsDeviceRepository = new SqlSugarRepository<WcsDevice>(sugarClient);

                Console.WriteLine($"接收到客户端 {clientId} 的任务状态数据: {data}");
                // 这里可以添加具体的任务状态处理逻辑
                // 例如：更新数据库中的任务状态、触发相关事件等
                //var pLCTaskStatus = JSON.Deserialize<PLCTaskStatusDto>(data);
                var datas = data.Split('|');
                if (datas.Length < 4)
                    throw new Exception($"任务状态数据格式错误: {data}");

                var pLCTaskStatus = new PLCTaskStatusDto()
                {
                    TaskCode = datas[0],
                    BinNo = datas[1],
                    TaskType = short.Parse(datas[2]),
                    Status = byte.Parse(datas[3])
                };
                var taskId = pLCTaskStatus.TaskCode.ObjToInt();
                var task = _sqlSugarRepository.FirstOrDefault(f => f.TaskId == taskId);
                if (task.TaskStatus == 4) return;
                // 开始事务
                _sqlSugarRepository.Context.Ado.BeginTran();
                try
                {
                    if (pLCTaskStatus.Status==1&& task.TaskStatus != 4)
                        task.TaskStatus = 4;
                    if (pLCTaskStatus.Status == 2 && task.TaskStatus != 3)
                        task.TaskStatus = 3;
                    if (pLCTaskStatus.Status == 3 && task.TaskStatus != 2)
                        task.TaskStatus = 2;

                    await _sqlSugarRepository.UpdateAsync(task);
                    await _wcsToPlc.AddStep(_sqlSugarRepository.Context, task.TaskId, clientId, data, "PLC反馈调度任务执行状态");

                    var locationId = 0;
                    if (task.TaskType == 1)//上架任务，更新容器位置；下架时，容器位置置空！
                    {
                        var locationIds = _sqlSugarRepository.Context.Ado.SqlQuery<int>($"select location_id from wcs_warehouse_location where location_code='{task.EndLocation}' LIMIT 1");
                        if (locationIds.Count == 1) locationId = locationIds[0];
                    }

                    _sqlSugarRepository.Context.Ado.ExecuteCommand($"update wcs_container set location_id={locationId} where container_code='{task.BinNo}'");
                    _sqlSugarRepository.Context.Ado.CommitTran();
                }
                catch (Exception ex)
                {
                    _sqlSugarRepository.Context.Ado.RollbackTran();
                    throw new Exception(ex.Message, ex);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"处理客户端 {clientId} 的任务状态数据失败: {ex.GetBaseException().Message}");
            }
        }

        /// <summary>
        /// 处理设备信息数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        private async Task HandleEquipmentInfoData(string clientId, string data)
        {
            try
            {
                Console.WriteLine($"接收到客户端 {clientId} 的设备信息数据: {data}");
                // 这里可以添加具体的设备信息处理逻辑

                //var weighingData = JSON.Deserialize<PLCEquipmentDto>(data);
                //var datas = data.Split('|');

                //var device = _wcsDeviceRepository.FirstOrDefault(f => f.DeviceCode == clientId);
                //if (device != null)
                //{
                //    device.RunStatus = weighingData.RunningState;
                //    device.Online = 2;
                //}
                //await _wcsDeviceRepository.UpdateAsync(device);
            }
            catch (Exception ex)
            {
                throw new Exception($"处理客户端 {clientId} 的设备信息数据失败: {ex.GetBaseException().Message}");
            }
        }


        /// <summary>
        /// 处理称重数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        private async Task HandleWeighingData(string clientId, string data)
        {
            try
            {
                Console.WriteLine($"接收到客户端 {clientId} 的称重数据: {data}");

                // 反序列化称重数据
                var weighingData = JSON.Deserialize<PLCWeighingDto>(data);

                if (weighingData == null) throw new Exception($"称重数据解析失败: {data}");

                {
                    //// 构建反馈数据
                    //var feedbackDto = new WcsFeedbackDto
                    //{
                    //    BinNo = weighingData.BinNo,
                    //    Status = 1
                    //};

                    //// 调用反馈方法
                    //if (_wcsToPlc != null)
                    //{
                    //    var result = await _wcsToPlc.FeedbackPointAsync(clientId, feedbackDto);
                    //    await _wcsToPlc.DispatchTaskAsync(new  WmsTaskDto() {  StartLocation = "123" });
                    //    Console.WriteLine($"称重数据反馈结果: {result.Message}");
                    //}
                    //else
                    //{
                    //    Console.WriteLine("WcsToPlc未初始化，无法反馈称重数据");
                    //}
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"处理客户端 {clientId} 的称重数据失败: {ex.GetBaseException().Message}");
            }
        }

        /// <summary>
        /// 处理尺寸数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        private async Task HandleSizeData(string clientId, string data)
        {
            try
            {
                Console.WriteLine($"接收到客户端 {clientId} 的尺寸数据: {data}");

                // 反序列化尺寸数据
                var sizeData = JSON.Deserialize<PLCMeasurementDto>(data);

                if (sizeData != null)
                {
                    //// 构建反馈数据
                    //var feedbackDto = new WcsFeedbackDto
                    //{
                    //    BinNo = sizeData.BinNo,
                    //    Status =  string.Equals(sizeData.Status, "success", StringComparison.OrdinalIgnoreCase) ? 1 : 0
                    //};

                    //// 调用反馈方法
                    //if (_wcsToPlc != null)
                    //{
                    //    var result = await _wcsToPlc.FeedbackPointAsync(clientId, feedbackDto);
                    //    Console.WriteLine($"尺寸数据反馈结果: {result.Message}");
                    //}
                    //else
                    //{
                    //    Console.WriteLine("WcsToPlc未初始化，无法反馈尺寸数据");
                    //}
                }
                else
                {
                    throw new Exception($"尺寸数据解析失败: {data}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"处理客户端 {clientId} 的尺寸数据失败: {ex.GetBaseException().Message}");
            }
        }

        /// <summary>
        /// 处理检查点数据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        private async Task HandleCheckpointData(string clientId, string data)
        {
            try
            {
                Console.WriteLine($"接收到客户端 {clientId} 的检查点数据: {data}");

                await _wcsToPlc.FeedbackPointAsync(clientId, data);
            }
            catch (Exception ex)
            {
                throw new Exception($"处理客户端 {clientId} 的检查点数据失败: {ex.GetBaseException().Message}");
            }
        }

        /// <summary>
        /// 处理心跳包
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        private async Task HandleHeartbeat(string clientId, string data)
        {
            try
            {
                Console.WriteLine($"接收到客户端 {clientId} 的心跳包");
                // 心跳包通常不需要复杂处理，PLCServerSocket已经更新了最后活动时间
            }
            catch (Exception ex)
            {
                throw new Exception($"处理客户端 {clientId} 的心跳包失败: {ex.GetBaseException().Message}");
            }
        }

        /// <summary>
        /// 处理原始数据（支持小端格式）
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="data">数据</param>
        private async Task HandleRawData(string clientId, string data)
        {
            try
            {
                Console.WriteLine($"接收到客户端 {clientId} 的原始数据: {data}");
                // 这里可以添加具体的原始数据处理逻辑
            }
            catch (Exception ex)
            {
                throw new Exception($"处理客户端 {clientId} 的原始数据失败: {ex.GetBaseException().Message}");
            }
        }

        #endregion

    }
}

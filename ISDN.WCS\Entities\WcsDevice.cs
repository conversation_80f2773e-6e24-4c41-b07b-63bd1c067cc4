using SqlSugar;
using System;
using System.Collections.Generic;
using RuoYi.Data.Entities;

namespace ISDN.WCS.Data.Entities
{
    /// <summary>
    ///  设备管理 对象 wcs_device
    ///  author zgq
    ///  date   2025-08-13 16:36:40
    /// </summary>
    [SugarTable("wcs_device", "设备管理")]
    public class WcsDevice : IsdnBaseEntity
  {
        /// <summary>
        /// 设备编号 (device_code)
        /// </summary>
        [SugarColumn(ColumnName = "device_code", ColumnDescription = "设备编号")]
        public string DeviceCode { get; set; }
        
        /// <summary>
        /// 设备名称 (device_name)
        /// </summary>
        [SugarColumn(ColumnName = "device_name", ColumnDescription = "设备名称")]
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 设备类型 (device_type)
        /// </summary>
        [SugarColumn(ColumnName = "device_type", ColumnDescription = "设备类型")]
        public int? DeviceType { get; set; }
        
        /// <summary>
        /// 通信地址 (addr)
        /// </summary>
        [SugarColumn(ColumnName = "addr", ColumnDescription = "通信地址")]
        public string? Addr { get; set; }
        
        /// <summary>
        /// 端口号 (port)
        /// </summary>
        [SugarColumn(ColumnName = "port", ColumnDescription = "端口号")]
        public int? Port { get; set; }
        
        /// <summary>
        /// 在线状态 (online)
        /// </summary>
        [SugarColumn(ColumnName = "online", ColumnDescription = "在线状态")]
        public int? Online { get; set; }
        
        /// <summary>
        /// 运行状态 (run_status)
        /// </summary>
        [SugarColumn(ColumnName = "run_status", ColumnDescription = "运行状态")]
        public int? RunStatus { get; set; }
        
        /// <summary>
        /// 设备ID (device_id)
        /// </summary>
        [SugarColumn(ColumnName = "device_id", ColumnDescription = "设备ID", IsPrimaryKey = true, IsIdentity = true)]
        public long DeviceId { get; set; }
        
        /// <summary>
        /// 生效 (is_visible_flag)
        /// </summary>
        [SugarColumn(ColumnName = "is_visible_flag", ColumnDescription = "生效")]
        public int? IsVisibleFlag { get; set; }
        
        /// <summary>
        /// 创建人id (created_by)
        /// </summary>
        [SugarColumn(ColumnName = "created_by", ColumnDescription = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id (updated_by)
        /// </summary>
        [SugarColumn(ColumnName = "updated_by", ColumnDescription = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间 (created_time)
        /// </summary>
        [SugarColumn(ColumnName = "created_time", ColumnDescription = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间 (updated_time)
        /// </summary>
        [SugarColumn(ColumnName = "updated_time", ColumnDescription = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
    }
}

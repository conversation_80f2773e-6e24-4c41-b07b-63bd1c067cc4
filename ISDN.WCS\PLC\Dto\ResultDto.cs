using System;

namespace ISDN.WCS.Data.Dtos
{
  /// <summary>
  /// 结果数据传输对象
  /// </summary>
  public class ResultDto
  {
    public string ServerTag { get; set; }
    public bool Result { get; set; } = true;
    public string Message { get; set; }
    public object Data { get; set; }

    public static ResultDto Success(string message = "操作成功")
    {
      return new ResultDto { Result = true, Message = message };
    }

    public static ResultDto Fail(string message = "操作失败")
    {
      return new ResultDto { Result = false, Message = message };
    }
  }

  public class ResultDto<T>
  {
    public string ServerTag { get; set; }
    public int Code { get; set; }
    public string Message { get; set; }
    public T Data { get; set; }
    public static ResultDto Success(string message = "操作成功", T _data = default(T))
    {
      return new ResultDto { Result = true, Message = message, Data = _data };
    }

    public static ResultDto Fail(string message = "操作失败")
    {
      return new ResultDto { Result = false, Message = message };
    }
  }
}

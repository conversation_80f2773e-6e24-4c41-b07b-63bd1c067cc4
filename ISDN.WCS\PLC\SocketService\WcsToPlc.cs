using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.Entities;
using ISDN.WCS.PLC.Dto;
using ISDN.WCS.PLC.Socket;
using ISDN.WCS.Repositories;
using Microsoft.Extensions.Configuration;
using RuoYi.Data;
using RuoYi.Data.Entities;
using RuoYi.Framework;
using RuoYi.Framework.JsonSerialization;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UAParser.Objects;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace ISDN.WCS.PLC.SocketService
{

    public class WcsToPlc
    {
        private const string TaskTag = "TSK";//任务下发
        private const string FeedbackTag = "WCL";//CS反馈接驳点/分拣点是否正确
        //private readonly ISqlSugarRepository<WcsTask> _wcsTaskRepository;
        //private readonly ISqlSugarRepository<WcsContainer> _wcsContainerRepository;
        //private const string DEFAULT_PLC_CLIENT_ID = "DefaultPLC";

        public WcsToPlc()
        {
            // 不使用依赖注入，直接通过App.GetService获取服务实例
            //_wcsTaskRepository = App.GetService<ISqlSugarRepository<WcsTask>>();
            //_wcsContainerRepository = App.GetService<ISqlSugarRepository<WcsContainer>>();
        }



        /// <summary>
        /// WCS调度任务下发接口
        /// 接收WMS调度任务，拆分设备任务下发给PLC
        /// </summary>
        /// <param name="wmsTaskDto">任务数据传输对象</param>
        /// <returns>处理结果</returns>
        public async Task<ResultDto> DispatchTaskAsync(WmsTaskDto wmsTaskDto)//string clientId,
        {
            string responseStr = null;
            try
            {

                var sugarClient = SqlSugarSetup.GetSqlSugarClient();
                var _wcsTaskRepository = new SqlSugarRepository<WcsTask>(sugarClient);
                var _wcsContainerRepository = new SqlSugarRepository<WcsContainer>(sugarClient);

                // 1. 验证任务数据
                if (wmsTaskDto == null || string.IsNullOrEmpty(wmsTaskDto.BinNo))
                {
                    return ResultDto.Fail("容器不能为空");
                }

                var isExistBinNo = _wcsTaskRepository.FirstOrDefault(f => f.BinNo == wmsTaskDto.BinNo && f.IsVisibleFlag == 1 && f.TaskStatus < 3);

                if (isExistBinNo != null) return ResultDto.Fail("该容器存在未结束的任务！");

                var deviceLocation = wmsTaskDto.EndLocation;
                if (string.IsNullOrEmpty(wmsTaskDto.EndLocation))
                    return ResultDto.Fail("下架任务，必须要有目标货位！");

                if (wmsTaskDto.TaskType == 2)
                {
                    if (string.IsNullOrEmpty(wmsTaskDto.StartLocation))
                        return ResultDto.Fail("下架任务，必须要有起始货位！");
                    deviceLocation = wmsTaskDto.StartLocation;
                }


                //调用sql语句查询，出来Datatable
                var sqlStr = @$"SELECT wcs_device.* from wcs_warehouse_location
LEFT JOIN wcs_lane ON wcs_warehouse_location.lane_id=wcs_lane.lane_id
LEFT JOIN wcs_device on wcs_lane.device_id=wcs_device.device_id
WHERE location_code='{(wmsTaskDto.TaskType == 1 ? wmsTaskDto.EndLocation : wmsTaskDto.StartLocation)}'";


                // 执行SQL查询并返回DataTable结果
                //DataTable dataTable = _wcsTaskRepository.Context.Ado.GetDataTable(sqlStr);
                var devices = _wcsTaskRepository.Context.Ado.SqlQuery<WcsDevice>(sqlStr);

                if (devices.Count > 1) return ResultDto.Fail("根据货位找到多条设备！");

                var device = devices.FirstOrDefault();
                if (device == null || string.IsNullOrEmpty(device.DeviceCode)) return ResultDto.Fail("根据货位不能查找到设备！");



                // 2. 使用事务保存容器和任务信息
                WcsTask taskEntity = null;

                // 开始事务
                _wcsTaskRepository.Context.Ado.BeginTran();
                try
                {
                    var wcs_container = _wcsContainerRepository.FirstOrDefault(f => f.ContainerCode == wmsTaskDto.BinNo);
                    if (wcs_container == null)
                    {
                        wcs_container = new WcsContainer() { ContainerCode = wmsTaskDto.BinNo, ContainerName = wmsTaskDto.BinNo, IsVisibleFlag = 1 };
                        // 在同一个事务中执行插入操作
                        _wcsTaskRepository.Context.Insertable(wcs_container).ExecuteCommand();
                    }
                    if (wmsTaskDto.TaskType == 1 && wcs_container.LocationId > 0) throw new Exception("该容器已在库位上，只能做下架操作！");
                    if(wmsTaskDto.TaskType == 2 && wcs_container.LocationId <= 0) throw new Exception("该容器不在库位上，不能做下架操作！");
                    // 保存任务到数据库
                    taskEntity = new WcsTask
                        {
                            TaskCode = $"{device.DeviceId}{DateTime.Now:yyMMddHHmmss}",// DateTime.Now.ToString("yyyyMMdd"),
                            BinNo = wmsTaskDto.BinNo,
                            TaskType = wmsTaskDto.TaskType,
                            EquipmentNo = device.DeviceCode,
                            StartLocation = wmsTaskDto.StartLocation,
                            EndLocation = wmsTaskDto.EndLocation,
                            TaskStatus = 2, // 1: 未执行
                            IsVisibleFlag = 1,
                            CreatedTime = DateTime.Now
                        };

                    // 在同一个事务中执行插入操作
                    var keyId = await _wcsTaskRepository.Context.Insertable(taskEntity).ExecuteReturnIdentityAsync();// .ExecuteCommandAsync();
                    taskEntity.TaskId = keyId;

                    // 3. 拆分任务并下发给PLC
                    //// 创建要发送的任务数据对象
                    //var taskData = new
                    //{
                    //    TaskId = taskEntity.TaskId,
                    //    TaskCode = taskEntity.TaskCode,
                    //    BinNo = taskEntity.BinNo,
                    //    TaskType = wmsTaskDto.TaskType,
                    //    EquipmentNo = taskEntity.EquipmentNo,
                    //    StartingLocation = taskEntity.StartLocation,
                    //    TargetLocation = taskEntity.EndLocation,
                    //    Priority = 1,
                    //};

                    //// 将任务数据序列化为JSON字符串
                    //string jsonData = JSON.Serialize(taskData);
                    var jsonData = $"{taskEntity.TaskId}|{taskEntity.BinNo}|{taskEntity.TaskType}|{taskEntity.EquipmentNo}|{taskEntity.StartLocation}|{taskEntity.EndLocation}";
                    await AddStep(_wcsTaskRepository.Context, taskEntity.TaskId, taskEntity.EquipmentNo, jsonData, "调度任务下发");

                    // 使用PLC客户端Socket管理器发送数据
                    var clientId = device.DeviceCode;
                    bool sendSuccess = await PLCClientSocketManager.SendDataAsync(clientId, $"{TaskTag}|{jsonData}");

                    if (sendSuccess)
                    {
                        Console.WriteLine($"向PLC发送任务成功: {taskEntity.TaskCode}");
                    }
                    else
                    {
                        Console.WriteLine($"向PLC发送任务失败: {taskEntity.TaskCode}");
                        throw new Exception("调度任务下发失败");
                    }
                    // 提交事务
                    _wcsTaskRepository.Context.Ado.CommitTran();
                    return ResultDto.Success("调度任务下发成功");
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    _wcsTaskRepository.Context.Ado.RollbackTran();
                    Console.WriteLine($"事务执行失败: {ex.GetBaseException().Message}");
                    throw new Exception(ex.Message, ex);
                }

            }
            catch (Exception ex)
            {
                responseStr = ex.GetBaseException().Message;
                Console.WriteLine($"调度任务下发失败: {wmsTaskDto.BinNo},{wmsTaskDto.StartLocation},{wmsTaskDto.EndLocation}, {ex.Message}");
                return ResultDto.Fail("任务下发失败: " + ex.GetBaseException().Message);
            }
            finally
            {
                ExtensionsClass.AddLog(null, "WCS调度任务下发接口", JSON.Serialize(wmsTaskDto), responseStr);
            }
        }

        public async Task AddStep(ISqlSugarClient context, long taskId, string deviceCode, string jsonData, string remark)
        {
            var taskStep = new WcsTaskStep()
            {
                CreatedTime = DateTime.Now,
                DeviceCode = deviceCode,
                Params = jsonData,
                Remark = remark,
                TaskId = taskId,
            };
            await context.Insertable(taskStep).ExecuteCommandAsync();
        }
        /// <summary>
        /// WCS反馈接驳点/分拣点是否正确接口
        /// 反馈PLC扫描的容器号/接驳点是否正确
        /// </summary>
        /// <param name="feedbackDto">反馈数据传输对象</param>
        /// <returns>处理结果</returns>
        public async Task<ResultDto> FeedbackPointAsync(string clientId, string checkpointDataStr)
        {
            string responseStr = "";
            try
            {

                var sugarClient = SqlSugarSetup.GetSqlSugarClient();
                var _wcsTaskRepository = new SqlSugarRepository<WcsTask>(sugarClient);
                var _wcsContainerRepository = new SqlSugarRepository<WcsContainer>(sugarClient);
                //// 反序列化检查点数据
                //var checkpointData = JSON.Deserialize<CheckPointDto>(checkpointDataStr);

                var checkpointDataStrs = checkpointDataStr.Split('|');
                var checkpointData = new CheckPointDto()
                {
                    BinNo = checkpointDataStrs[0],
                    Location = checkpointDataStrs[1],
                };
                //if (checkpointData == null)
                //    return ResultDto.Fail("反馈数据不能为空");

                //判断点位是否一致
                //由容器找到任务-》找到最终货位-》当前货位是否和最终货位是否在一个巷道

                //var task = await _wcsTaskRepository.GetAsync(x => x.BinNo == checkpointData.BinNo);

                var sqlStr = $"SELECT lane_id from wcs_warehouse_location where location_code = '{checkpointData.Location}' LIMIT 1";

                var currentLaneId = _wcsTaskRepository.Context.Ado.GetLong(sqlStr);//接驳点位对应的巷道


                sqlStr = $@"SELECT task_id,lane_id from wcs_task
 LEFT JOIN wcs_warehouse_location on wcs_warehouse_location.location_code = wcs_task.end_location
where  wcs_task.is_visible_flag=1 AND wcs_task.task_status =2 AND wcs_warehouse_location.lane_id = '{currentLaneId}' LIMIT 1";

                var endDtAddr = _wcsTaskRepository.Context.Ado.GetDataTable(sqlStr);//根据窗口及巷道查询运行的任务
                var _status = false;
                if (endDtAddr.Rows.Count == 1)
                {
                    //    throw new Exception("不能找到运行任务对应的巷道");
                    //else
                    await AddStep(_wcsTaskRepository.Context, (long)endDtAddr.Rows[0]["task_id"], clientId, checkpointDataStr, "接驳点/分拣点");

                    var endLaneId = (long)endDtAddr.Rows[0]["lane_id"];

                    _status = currentLaneId == endLaneId;
                }
                //// 构建反馈数据
                //var feedbackDto = new WcsFeedbackDto
                //{
                //    BinNo = checkpointData.BinNo,
                //    LocationCode = checkpointData.Location,
                //    Status = _status,
                //};

                ////发送反馈结果到PLC服务端
                //string feedbackJson = JSON.Serialize(feedbackDto);
                string feedbackJson = $"{checkpointData.BinNo}|{checkpointData.Location}|{(_status ? 1 : 2)}";
                if (endDtAddr.Rows.Count == 1)
                    await AddStep(_wcsTaskRepository.Context, (long)endDtAddr.Rows[0]["task_id"], clientId, feedbackJson, "接驳点/分拣点 反馈");

                // 使用PLC客户端Socket管理器发送反馈数据
                bool sendSuccess = await PLCClientSocketManager.SendDataAsync(clientId, $"{FeedbackTag}|{feedbackJson}");

                if (sendSuccess)
                {
                    Console.WriteLine($"向PLC发送反馈成功: 容器号{checkpointData.BinNo}");
                    return ResultDto.Success("反馈信息处理成功");
                }
                else
                {
                    throw new Exception($"向PLC发送反馈失败: 容器号{checkpointData.BinNo}");
                    //// 即使发送失败，也返回成功，因为业务逻辑已经处理完成
                    //return ResultDto.Success("反馈信息处理成功，但发送至PLC失败");
                }
            }
            catch (Exception ex)
            {
                responseStr = ex.GetBaseException().Message;
                return ResultDto.Fail("反馈信息处理失败: " + ex.GetBaseException().Message);
            }
            finally
            {
                ExtensionsClass.AddLog(null, "WCS反馈接驳点/分拣点是否正确接口", checkpointDataStr, responseStr);
            }

        }

    }
}

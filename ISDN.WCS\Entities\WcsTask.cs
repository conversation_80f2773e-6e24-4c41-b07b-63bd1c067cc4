using SqlSugar;
using System;
using System.Collections.Generic;
using RuoYi.Data.Entities;

namespace ISDN.WCS.Data.Entities
{
    /// <summary>
    ///  WCS任务档 对象 wcs_task
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    [SugarTable("wcs_task", "WCS任务档")]
    public class WcsTask : IsdnBaseEntity
    {
        /// <summary>
        /// 任务ID (task_id)
        /// </summary>
        [SugarColumn(ColumnName = "task_id", ColumnDescription = "任务ID", IsPrimaryKey = true, IsIdentity = true)]
        public long TaskId { get; set; }


        /// <summary>
        /// 任务号 (task_code)
        /// </summary>
        [SugarColumn(ColumnName = "task_code", ColumnDescription = "任务号")]
        public string TaskCode { get; set; }

        /// <summary>
        /// 容器号 (bin_no)
        /// </summary>
        [SugarColumn(ColumnName = "bin_no", ColumnDescription = "容器号")]
        public string BinNo { get; set; }

        /// <summary>
        /// 设备编号 (equipment_no)
        /// </summary>
        [SugarColumn(ColumnName = "equipment_no", ColumnDescription = "设备编号")]
        public string EquipmentNo { get; set; }

        /// <summary>
        /// 任务类型 (task_type)
        /// </summary>
        [SugarColumn(ColumnName = "task_type", ColumnDescription = "任务类型")]
        public int TaskType { get; set; }

        /// <summary>
        /// 起始位置 (start_location)
        /// </summary>
        [SugarColumn(ColumnName = "start_location", ColumnDescription = "起始位置")]
        public string? StartLocation { get; set; }

        /// <summary>
        /// 目标位置 (end_location)
        /// </summary>
        [SugarColumn(ColumnName = "end_location", ColumnDescription = "目标位置")]
        public string? EndLocation { get; set; }

        /// <summary>
        /// 重试次数 (retries)
        /// </summary>
        [SugarColumn(ColumnName = "retries", ColumnDescription = "重试次数")]
        public int? Retries { get; set; }

        /// <summary>
        /// 最大重试次数 (max_retries)
        /// </summary>
        [SugarColumn(ColumnName = "max_retries", ColumnDescription = "最大重试次数")]
        public int? MaxRetries { get; set; }

        /// <summary>
        /// 错误消息 (err_msg)
        /// </summary>
        [SugarColumn(ColumnName = "err_msg", ColumnDescription = "错误消息")]
        public string? ErrMsg { get; set; }

        /// <summary>
        /// 任务状态 (task_status)
        /// </summary>
        [SugarColumn(ColumnName = "task_status", ColumnDescription = "任务状态")]
        public int? TaskStatus { get; set; }

        /// <summary>
        /// 生效 (is_visible_flag)
        /// </summary>
        [SugarColumn(ColumnName = "is_visible_flag", ColumnDescription = "生效")]
        public int? IsVisibleFlag { get; set; }

        /// <summary>
        /// 创建人id (created_by)
        /// </summary>
        [SugarColumn(ColumnName = "created_by", ColumnDescription = "创建人id")]
        public long? CreatedBy { get; set; }

        /// <summary>
        /// 更新人id (updated_by)
        /// </summary>
        [SugarColumn(ColumnName = "updated_by", ColumnDescription = "更新人id")]
        public long? UpdatedBy { get; set; }

        /// <summary>
        /// 创建时间 (created_time)
        /// </summary>
        [SugarColumn(ColumnName = "created_time", ColumnDescription = "创建时间")]
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// 修改时间 (updated_time)
        /// </summary>
        [SugarColumn(ColumnName = "updated_time", ColumnDescription = "修改时间")]
        public DateTime? UpdatedTime { get; set; }

    }
}

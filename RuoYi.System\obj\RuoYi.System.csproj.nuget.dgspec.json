{"format": 1, "restore": {"E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.System\\RuoYi.System.csproj": {}}, "projects": {"E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Common\\RuoYi.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Common\\RuoYi.Common.csproj", "projectName": "RuoYi.Common", "projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Common\\RuoYi.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files (x86)\\Progress\\Telerik UI for Blazor 8.1.1\\packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "\\\\192.168.0.207\\packages": {}, "\\\\192.168.0.228\\nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\RuoYi.Data.csproj": {"projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\RuoYi.Data.csproj"}, "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\RuoYi.Framework.csproj": {"projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\RuoYi.Framework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\RuoYi.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\RuoYi.Data.csproj", "projectName": "RuoYi.Data", "projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\RuoYi.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files (x86)\\Progress\\Telerik UI for Blazor 8.1.1\\packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "\\\\192.168.0.207\\packages": {}, "\\\\192.168.0.228\\nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MySqlConnector": {"target": "Package", "version": "[2.4.0, )"}, "SqlSugarCore": {"target": "Package", "version": "[*********, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\RuoYi.Framework.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\RuoYi.Framework.csproj", "projectName": "RuoYi.Framework", "projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\RuoYi.Framework.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files (x86)\\Progress\\Telerik UI for Blazor 8.1.1\\packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "\\\\192.168.0.207\\packages": {}, "\\\\192.168.0.228\\nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AspectCore.Abstractions": {"target": "Package", "version": "[2.4.0, )"}, "AspectCore.Extensions.DependencyInjection": {"target": "Package", "version": "[2.4.0, )"}, "Ben.Demystifier": {"target": "Package", "version": "[0.4.1, )"}, "Hardware.Info": {"target": "Package", "version": "[101.0.0, )"}, "Lazy.Captcha.Core": {"target": "Package", "version": "[2.0.9, )"}, "Mapster": {"target": "Package", "version": "[7.4.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[4.11.0, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[8.0.10, )"}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[8.0.2, )"}, "MiniExcel": {"target": "Package", "version": "[1.34.2, )"}, "MiniProfiler.AspNetCore.Mvc": {"target": "Package", "version": "[4.3.8, )"}, "MiniProfiler.Shared": {"target": "Package", "version": "[4.3.8, )"}, "RedisRateLimiting.AspNetCore": {"target": "Package", "version": "[1.2.0, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "SkiaSharp.NativeAssets.Linux.NoDependencies": {"target": "Package", "version": "[2.88.9, )"}, "SqlSugarCore": {"target": "Package", "version": "[*********, )"}, "Swashbuckle.AspNetCore.Swagger": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore.SwaggerGen": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[8.0.0, )"}, "UAParser.Core": {"target": "Package", "version": "[4.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.System\\RuoYi.System.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.System\\RuoYi.System.csproj", "projectName": "RuoYi.System", "projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.System\\RuoYi.System.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.System\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Progress\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files (x86)\\Progress\\Telerik UI for Blazor 8.1.1\\packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "\\\\192.168.0.207\\packages": {}, "\\\\192.168.0.228\\nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Common\\RuoYi.Common.csproj": {"projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Common\\RuoYi.Common.csproj"}, "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\RuoYi.Data.csproj": {"projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Data\\RuoYi.Data.csproj"}, "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\RuoYi.Framework.csproj": {"projectPath": "E:\\SourceCode\\SelfSVNCode\\WCS\\isdn_wcs\\RuoYi.Framework\\RuoYi.Framework.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}
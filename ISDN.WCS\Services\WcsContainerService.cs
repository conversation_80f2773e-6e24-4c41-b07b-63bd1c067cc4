using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using ISDN.WCS.Repositories;
using Mapster;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Data;
using RuoYi.Framework.DependencyInjection;
using SqlSugar;

namespace ISDN.WCS.Services
{
    /// <summary>
    ///  容器档 Service
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    public class WcsContainerService : BaseService<WcsContainer, WcsContainerDto>, ITransient
    {
        private readonly ILogger<WcsContainerService> _logger;
        private readonly WcsContainerRepository _wcsContainerRepository;

        public WcsContainerService(ILogger<WcsContainerService> logger,
            WcsContainerRepository wcsContainerRepository)
        {
            BaseRepo = wcsContainerRepository;

            _logger = logger;
            _wcsContainerRepository = wcsContainerRepository;
        }

        /// <summary>
        /// 查询 容器档 详情
        /// </summary>
        public async Task<WcsContainer> GetAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.ContainerId == id);
            return entity;
        }

        /// <summary>
        /// 查询 容器档 详情
        /// </summary>
        public async Task<WcsContainerDto> GetDtoAsync(long id)
        {
            var entity = await base.FirstOrDefaultAsync(e => e.ContainerId == id);
            var dto = entity.Adapt<WcsContainerDto>();
            // TODO 填充关联表数据
            return dto;
        }

        public override async Task<SqlSugarPagedList<WcsContainerDto>> GetDtoPagedListAsync(WcsContainerDto dto)
        {
            var result = await base.GetDtoPagedListAsync(dto);
            foreach (var item in result.Rows)
            {
                if (item.LocationId > 0)
                    item.LocationCode = _wcsContainerRepository.Repo.Context.Ado.SqlQuery<string>($"select location_code  from  wcs_warehouse_location where location_id={item.LocationId}").FirstOrDefault();
            }

            return result;
        }
    }
}

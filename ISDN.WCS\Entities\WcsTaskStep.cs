﻿using RuoYi.Data.Entities;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ISDN.WCS.Entities
{
    /// <summary>
    ///  任务步骤 对象 wcs_task_step
    ///  author ruoyi.net
    ///  date   2025-08-28 20:02:55
    /// </summary>
    [SugarTable("wcs_task_step", "任务步骤")]
    public class WcsTaskStep : BaseEntity
    {
        /// <summary>
        ///  (task_step_id)
        /// </summary>
        [SugarColumn(ColumnName = "task_step_id", ColumnDescription = "", IsPrimaryKey = true, IsIdentity = true)]
        public long TaskStepId { get; set; }

        /// <summary>
        /// 任务id (task_id)
        /// </summary>
        [SugarColumn(ColumnName = "task_id", ColumnDescription = "任务id")]
        public long TaskId { get; set; }

        /// <summary>
        /// 设备编码 (device_code)
        /// </summary>
        [SugarColumn(ColumnName = "device_code", ColumnDescription = "设备编码")]
        public string? DeviceCode { get; set; }

        /// <summary>
        /// 参数 (params)
        /// </summary>
        [SugarColumn(ColumnName = "params", ColumnDescription = "参数")]
        public string? Params { get; set; }

        /// <summary>
        /// 备注 (remark)
        /// </summary>
        [SugarColumn(ColumnName = "remark", ColumnDescription = "备注")]
        public string? Remark { get; set; }

        /// <summary>
        /// 创建人id (created_by)
        /// </summary>
        [SugarColumn(ColumnName = "created_by", ColumnDescription = "创建人id")]
        public long? CreatedBy { get; set; }

        /// <summary>
        /// 更新人id (updated_by)
        /// </summary>
        [SugarColumn(ColumnName = "updated_by", ColumnDescription = "更新人id")]
        public long? UpdatedBy { get; set; }

        /// <summary>
        /// 创建时间 (created_time)
        /// </summary>
        [SugarColumn(ColumnName = "created_time", ColumnDescription = "创建时间")]
        public DateTime? CreatedTime { get; set; }

        /// <summary>
        /// 修改时间 (updated_time)
        /// </summary>
        [SugarColumn(ColumnName = "updated_time", ColumnDescription = "修改时间")]
        public DateTime? UpdatedTime { get; set; }

    }
}
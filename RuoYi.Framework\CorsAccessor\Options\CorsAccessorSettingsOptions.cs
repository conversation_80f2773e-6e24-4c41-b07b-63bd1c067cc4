﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Configuration;
using RuoYi.Framework.ConfigurableOptions;

namespace RuoYi.Framework.CorsAccessor;

/// <summary>
///   跨域配置选项
/// </summary>
public sealed class CorsAccessorSettingsOptions : IConfigurableOptions<CorsAccessorSettingsOptions>
{
  /// <summary>
  ///   策略名称
  /// </summary>
  [Required]
  public string PolicyName { get; set; }

  /// <summary>
  ///   允许来源域名，没有配置则允许所有来源
  /// </summary>
  public string[] WithOrigins { get; set; }

  /// <summary>
  ///   请求表头，没有配置则允许所有表头
  /// </summary>
  public string[] WithHeaders { get; set; }

  /// <summary>
  ///   设置客户端可获取的响应标头
  /// </summary>
  public string[] WithExposedHeaders { get; set; }

  /// <summary>
  ///   设置跨域允许请求谓词，没有配置则允许所有
  /// </summary>
  public string[] WithMethods { get; set; }

  /// <summary>
  ///   是否允许跨域请求中的凭据
  /// </summary>
  public bool? AllowCredentials { get; set; }

  /// <summary>
  ///   设置预检过期时间
  /// </summary>
  public int? SetPreflightMaxAge { get; set; }

  /// <summary>
  ///   修正前端无法获取 Token 问题
  /// </summary>
  public bool? FixedClientToken { get; set; }

  /// <summary>
  ///   启用 SignalR 跨域支持
  /// </summary>
  public bool? SignalRSupport { get; set; }

  /// <summary>
  ///   后期配置
  /// </summary>
  /// <param name="options"></param>
  /// <param name="configuration"></param>
  public void PostConfigure(CorsAccessorSettingsOptions options, IConfiguration configuration)
  {
    PolicyName ??= "App.Cors.Policy";
    WithOrigins ??= Array.Empty<string>();
    AllowCredentials ??= true;
    FixedClientToken ??= true;
    SignalRSupport ??= false;
  }
}

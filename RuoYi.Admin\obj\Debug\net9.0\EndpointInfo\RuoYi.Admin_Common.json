{"openapi": "3.0.4", "info": {"title": "Common", "version": "1.0.0"}, "paths": {"/captchaImage": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取验证码", "operationId": "captchaImage-Get", "responses": {"200": {"description": "OK"}}}}, "/GetDescription": {"get": {"tags": ["Index"], "summary": "获取系统描述", "operationId": "GetDescription-Get", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}}, "components": {"securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}
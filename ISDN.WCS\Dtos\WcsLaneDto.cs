using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;
using SqlSugar;
using System.Collections.Generic;

namespace ISDN.WCS.Data.Dtos
{
    /// <summary>
    ///  巷道管理 对象 wcs_lane
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:42
    /// </summary>
    public class WcsLaneDto : BaseDto
    {
        /// <summary>
        /// 巷道名称
        /// </summary>
        [Excel(Name = "巷道名称")]
        public string LaneName { get; set; }
        
        /// <summary>
        /// 巷道ID
        /// </summary>
        [Excel(Name = "巷道ID")]
        public long? LaneId { get; set; }
        
        /// <summary>
        /// 巷道类型
        /// </summary>
        [Excel(Name = "巷道类型")]
        public int LaneType { get; set; }
        
        /// <summary>
        /// 创建人id
        /// </summary>
        [Excel(Name = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id
        /// </summary>
        [Excel(Name = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Excel(Name = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间
        /// </summary>
        [Excel(Name = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
        /// <summary>
        /// 巷道编号
        /// </summary>
        [Excel(Name = "巷道编号")]
        public string LaneCode { get; set; }

    /// <summary>
    /// 设备ID (device_id)
    /// </summary>
    [Excel(Name = "设备ID")]
    public long DeviceId { get; set; }
  }
}

fail: 2025-09-02 14:13:32.4577921 +08:00 星期二 L System.Logging.LoggingMonitor[0] #12
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-39f4e147a83b30c87adcbf965ef0993a-bdd674a555b59fe8-00
      ┣ 服务线程 ID：               #12
      ┣ 执行耗时：                  577ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}; username=admin; rememberMe=true; password=RlUsz7ZW/d8c3J+hLJSLnCLmSrbLcwrF7P7+bmpIyUzJe+AF4i4vRMcp8eg4JCyYtFwKyxn0Aum3Cor7F4Y6qg==
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D; username=admin; rememberMe=true; password=RlUsz7ZW/d8c3J+hLJSLnCLmSrbLcwrF7P7+bmpIyUzJe+AF4i4vRMcp8eg4JCyYtFwKyxn0Aum3Cor7F4Y6qg==
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                      SqlSugar.SqlSugarException
      ┣ 消息：                      中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Packet received out-of-order. Expected 1; got 2.DbType="MySql";ConfigId="master".
      English Message : Connection open error . Packet received out-of-order. Expected 1; got 2.DbType="MySql";ConfigId="master" 
      ┣ 错误堆栈：                  at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
         at SqlSugar.AdoProvider.CheckConnection()
         at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at SqlSugar.QueryableProvider`1.First()
         at RuoYi.Common.Data.BaseRepository`2.FirstOrDefault(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseRepository.cs:line 156
         at RuoYi.System.Services.SysConfigService.SelectConfigByKey(String configKey) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysConfigService.cs:line 47
         at RuoYi.System.Services.SysConfigService.IsCaptchaEnabled() in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysConfigService.cs:line 71
         at RuoYi.Admin.CaptchaController.GetCaptchaImage() in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin\Common\Controllers\CaptchaController.cs:line 35
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Packet received out-of-order. Expected 1; got 2.DbType="MySql";ConfigId="master".
      English Message : Connection open error . Packet received out-of-order. Expected 1; got 2.DbType="MySql";ConfigId="master" 
         at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
         at SqlSugar.AdoProvider.CheckConnection()
         at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at SqlSugar.QueryableProvider`1.First()
         at RuoYi.Common.Data.BaseRepository`2.FirstOrDefault(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseRepository.cs:line 156
         at RuoYi.System.Services.SysConfigService.SelectConfigByKey(String configKey) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysConfigService.cs:line 47
         at RuoYi.System.Services.SysConfigService.IsCaptchaEnabled() in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysConfigService.cs:line 71
         at RuoYi.Admin.CaptchaController.GetCaptchaImage() in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin\Common\Controllers\CaptchaController.cs:line 35
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-09-02 14:13:32.4628845 +08:00 星期二 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #12
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: 中文提示 :  连接数据库过程中发生错误，检查服务器是否正常连接字符串是否正确，错误信息：Packet received out-of-order. Expected 1; got 2.DbType="MySql";ConfigId="master".
      English Message : Connection open error . Packet received out-of-order. Expected 1; got 2.DbType="MySql";ConfigId="master" 
         at SqlSugar.Check.Exception(Boolean isException, String message, String[] args)
         at SqlSugar.AdoProvider.CheckConnection()
         at SqlSugar.MySqlProvider.GetCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at SqlSugar.QueryableProvider`1.First()
         at RuoYi.Common.Data.BaseRepository`2.FirstOrDefault(Expression`1 predicate) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Common\Data\BaseRepository.cs:line 156
         at RuoYi.System.Services.SysConfigService.SelectConfigByKey(String configKey) in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysConfigService.cs:line 47
         at RuoYi.System.Services.SysConfigService.IsCaptchaEnabled() in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.System\Services\SysConfigService.cs:line 71
         at RuoYi.Admin.CaptchaController.GetCaptchaImage() in E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin\Common\Controllers\CaptchaController.cs:line 35
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-09-02 17:00:17.5211003 +08:00 星期二 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #11
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Unable to resolve service for type 'ISDN.WCS.PLC.Socket.PLCClientSocketManager' while attempting to activate 'ISDN.WCS.Controllers.WcsDeviceController'.
         at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ThrowHelperUnableToResolveService(Type type, Type requiredBy)
         at lambda_method285(Closure, IServiceProvider, Object[])
         at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-09-02 17:00:23.1261550 +08:00 星期二 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #24
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Unable to resolve service for type 'ISDN.WCS.PLC.Socket.PLCClientSocketManager' while attempting to activate 'ISDN.WCS.Controllers.WcsDeviceController'.
         at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ThrowHelperUnableToResolveService(Type type, Type requiredBy)
         at lambda_method285(Closure, IServiceProvider, Object[])
         at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-09-02 17:02:46.3694852 +08:00 星期二 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #11
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Unable to resolve service for type 'ISDN.WCS.PLC.Socket.PLCClientSocketManager' while attempting to activate 'ISDN.WCS.Controllers.WcsDeviceController'.
         at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ThrowHelperUnableToResolveService(Type type, Type requiredBy)
         at lambda_method293(Closure, IServiceProvider, Object[])
         at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-09-02 17:18:08.3632655 +08:00 星期二 L RuoYi.Framework.Filters.GlobalExceptionFilter[0] #13
      GlobalException      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Unable to resolve service for type 'ISDN.WCS.PLC.Socket.PLCClientSocketManager' while attempting to activate 'ISDN.WCS.Controllers.WcsDeviceController'.
         at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ThrowHelperUnableToResolveService(Type type, Type requiredBy)
         at lambda_method293(Closure, IServiceProvider, Object[])
         at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++

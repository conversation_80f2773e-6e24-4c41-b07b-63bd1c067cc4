namespace ISDN.WCS.PLC.Dto;

/// <summary>
/// WCS调度任务DTO
/// </summary>
public class WCSTaskDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int TaskCode { get; set; }

    /// <summary>
    /// 容器号
    /// </summary>
    public int BinNo { get; set; }

    /// <summary>
    /// 任务类型
    /// 1: 上架
    /// 2: 下架
    /// </summary>
    public short TaskType { get; set; }

    /// <summary>
    /// 设备编号
    /// </summary>
    public int Equipment { get; set; }

    /// <summary>
    /// 起始位置X轴
    /// </summary>
    public int StartingX { get; set; }

    /// <summary>
    /// 起始位置Z轴
    /// </summary>
    public int StartingZ { get; set; }

    /// <summary>
    /// 起始位置层品
    /// </summary>
    public int StartingStory { get; set; }

    /// <summary>
    /// 目标位置X轴
    /// </summary>
    public int TargetX { get; set; }

    /// <summary>
    /// 目标位置Z轴
    /// </summary>
    public int TargetZ { get; set; }

    /// <summary>
    /// 目标位置层品
    /// </summary>
    public int TargetStory { get; set; }

    /// <summary>
    /// 内外层
    /// 1: 内层
    /// 2: 外层
    /// </summary>
    public byte InOutLayers { get; set; }

    /// <summary>
    /// 优先级
    /// 1-10
    /// </summary>
    public byte Priority { get; set; }
}
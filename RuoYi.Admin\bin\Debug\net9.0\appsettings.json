{
    "ConnectionConfigs": [
      {
        "ConfigId": "master",
        //"ConnectionString": "Server=127.0.0.1;port=3306;Database=ry_net_dev;Uid=root;Pwd=******;AllowLoadLocalInfile=true;",
        "ConnectionString": "Server=127.0.0.1;port=3306;Database=isdn_wcs;Uid=root;Pwd=Isdn-******;AllowLoadLocalInfile=true;Pooling=true;MinPoolSize=5;MaxPoolSize=100;CommandTimeout=300;",
       
        "DbType": "MySql",
        "IsAutoCloseConnection": true
      }
        //{
        //  "ConfigId": "slave",
        //  "ConnectionString": "Server=localhost;Database=ry_net2;Uid=root;Pwd=**************;AllowLoadLocalInfile=true;",
        //  "DbType": "MySql",
        //  "IsAutoCloseConnection": true
        //}
    ],
    "CacheConfig": {
        "CacheType": "Memory",
        // 缓存类型: Redis/Memory, 当 CacheType: Redis时, RedisConfig有效
        "RedisConfig": {
            "Host": "**************",
            "Port": "26379",
            "Database": 0,
            "Password": "nas!@#redis#@!",
            "InstanceName": "ruoyi_net:"
        }
    },
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning",
            "Microsoft.EntityFrameworkCore": "Warning",
            "System.Net.Http.HttpClient": "Warning"
        },
        "Monitor": {
            "GlobalEnabled": true,
            // 是否启用全局拦截，默认 `false`
            "IncludeOfMethods": [],
            // 是否指定拦截特定方法，当 GlobalEnabled: false 有效
            "ExcludeOfMethods": [],
            // 是否指定排除特定方法，当 GlobalEnabled: true 有效
            "ServiceLogLevel": "Information",
            // 配置业务日志输出级别，默认 Information
            "WithReturnValue": true,
            // 配置是否包含返回值，默认 `true`
            "ReturnValueThreshold": 0,
            // 配置返回值字符串阈值，默认 0，全量输出
            "JsonBehavior": "None",
            // 配置 LoggingMonitor Json 输出行为，默认 None
            "JsonIndented": false,
            // 配置 LoggingMonitor Json 格式化行为，默认 false
            "ContractResolver": "CamelCase"
            // 配置 LoggingMonitor 序列化属性命名规则，默认 CamelCase
        }
    },
    "SpecificationDocumentSettings": {
        "DocumentTitle": "RuoYi.Net 规范化接口",
        "GroupOpenApiInfos": [
            {
                "Group": "Default",
                "Title": "RuoYi.Net 规范化接口演示",
                "Description": "让 .NET 开发更简单，更通用，更流行。",
                "Version": "1.0.0"
            }
        ],
        "RoutePrefix": "api-doc",
        "HideServers": true
    },
    // job白名单: Assembly 集合, 逗号分隔
    "JobWhiteListAssembly": "RuoYi",
    "AllowedHosts": "*",
    "CorsAccessorSettings": {
        "WithExposedHeaders": [
            "access-token",
            "x-access-token",
            "environment"
        ]
    },
    "RuoYiConfig": {
        // 名称
        "Name": "RuoYi.Net",
        // 版本
        "Version": "2.0.0",
        // 版权年份
        "CopyrightYear": "2023",
        // 实例演示开关
        "DemoEnabled": true,
        // 文件路径 上传文件的文件夹名称
        "Profile": "/upload",
        // 获取ip地址开关
        "AddressEnabled": false
    },
    "UserConfig": {
        // 密码最大错误次数
        "MaxRetryCount": 5,
        // 密码锁定时间（默认10分钟）
        "LockTime": 10
    },
    // 验证码配置
    // 参考: https://github.com/pojianbing/LazyCaptcha/blob/master/README.md
    "CaptchaOptions": {
        // 验证码类型: 0-默认(英文字符大小写，数字混合), 1-中文, 2-数字, 3-中文数字小写, 4-中文数字大写, 5-英文字符大小写混合
        //             6-英文字符小写, 7-英文字符大写, 8-英文小写，数字混合, 9-英文大写，数字混合, 10-数字计算, 11-数字计算，中文
        "CaptchaType": 10,
        "CodeLength": 1,
        // 验证码长度, 要放在CaptchaType设置后  当类型为算术表达式时，长度代表操作的个数, 例如2
        "IgnoreCase": true,
        // 比较时是否忽略大小写
        "StoreageKeyPrefix": "captcha_codes:",
        // 存储键前缀
        "ImageOption": {
            "Width": 150,
            // 验证码宽度
            "Height": 50,
            // 验证码高度
            "BubbleMinRadius": 5,
            // 气泡最小半径
            "BubbleMaxRadius": 10,
            // 气泡最大半径
            "BubbleCount": 1,
            // 气泡数量
            "BubbleThickness": 1.0,
            // 气泡边沿厚度
            "InterferenceLineCount": 2,
            // 干扰线数量
            "FontSize": 32,
            // 字体大小
            "FontFamily": "kaiti",
            // 包含actionj,epilog,fresnel,headache,lexo,prefix,progbot,ransom,robot,scandal,kaiti
            "BackgroundColor": "#ffffff",
            // 验证码背景色
            "ForegroundColors": "",
            //  颜色格式同BackgroundColor,多个颜色逗号分割，随机选取。不填，空值，则使用默认颜色集
            "Quality": 100,
            // 图片质量（质量越高图片越大，gif调整无效可能会更大）
            "TextBold": true
            // 粗体
        }
    },
    "JWTSettings": {
        "ValidateIssuerSigningKey": true,
        // 是否验证密钥，bool 类型，默认true
        "IssuerSigningKey": "Z$7H5g#&&#uxzv6DyVxS%aJ%P4q*G$7uwM#Z",
        // 密钥，string 类型，必须是复杂密钥，长度大于32
        "ValidateIssuer": true,
        // 是否验证签发方，bool 类型，默认true
        "ValidIssuer": "ruoyi.net.issuer",
        // 签发方，string 类型
        "ValidateAudience": true,
        // 是否验证签收方，bool 类型，默认true
        "ValidAudience": "ruoyi.net.audience",
        // 签收方，string 类型
        "ValidateLifetime": true,
        // 是否验证过期时间，bool 类型，默认true，建议true
        "ExpiredTime": 30,
        // 过期时间，long 类型，单位分钟，默认30分钟
        "ClockSkew": 5,
        // 过期时间容错值，long 类型，单位秒，默认 5秒
        "Algorithm": "HS256"
        // 加密算法，string 类型，默认 HS256
    },
    // 全局限流配置, 如以下配置为: 在 1秒的窗口期内可访问100次
    "GlobalLimitConfig": {
        "PermitLimit": 50,
        // 最多并发的请求数。该值必须 > 0
        "Window": 1
        // 窗口大小，即时间长度(秒)
    },
    // ip限流配置, 如以下配置为: 每个ip 在 1秒的窗口期内可访问10次
    "IpRateLimitConfig": {
        "PermitLimit": 10,
        // 最多并发的请求数。该值必须 > 0
        "Window": 1
        // 窗口大小，即时间长度(秒)
    }
}

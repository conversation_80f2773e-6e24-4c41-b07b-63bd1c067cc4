using ISDN.WCS.Data.Dtos;
//using ISDN.Data.Dtos;
using ISDN.WCS.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Enums;
using RuoYi.Common.Utils;
using RuoYi.Data.Dtos;
using RuoYi.Framework;
using RuoYi.Framework.Extensions;
using RuoYi.System.Services;
using RuoYi.System.Slave.Services;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ISDN.WCS.Controllers
{
  /// <summary>
  /// 巷道管理
  ///  author ruoyi.net
  ///  date   2025-08-13 16:36:42
  /// </summary>
  [ApiDescriptionSettings("Wcs")]
  [Route("wcs/lane")]
  public class WcsLaneController : ControllerBase
  {
    private readonly ILogger<WcsLaneController> _logger;
    private readonly WcsLaneService _wcsLaneService;

    public WcsLaneController(ILogger<WcsLaneController> logger,
        WcsLaneService wcsLaneService)
    {
      _logger = logger;
      _wcsLaneService = wcsLaneService;
    }

    /// <summary>
    /// 查询巷道管理列表
    /// </summary>
    [HttpGet("list")]
    [AppAuthorize("wcs:lane:list")]
    public async Task<SqlSugarPagedList<WcsLaneDto>> GetWcsLanePagedList([FromQuery] WcsLaneDto dto)
    {
      return await _wcsLaneService.GetDtoPagedListAsync(dto);
    }

    [HttpGet("ListAll")]
    public async Task<AjaxResult> GetList()
    {
      var lanes = await _wcsLaneService.GetListAsync(new WcsLaneDto());
      var ajax = AjaxResult.Success();
      ajax.Add("lanes", lanes);

      return ajax;
    }

    /// <summary>
    /// 获取 巷道管理 详细信息
    /// </summary>
    [HttpGet("")]
    [HttpGet("{id}")]
    [AppAuthorize("wcs:lane:query")]
    public async Task<AjaxResult> Get(long id)
    {
      var data = await _wcsLaneService.GetDtoAsync(id);
      return AjaxResult.Success(data);
    }

    /// <summary>
    /// 新增 巷道管理
    /// </summary>
    [HttpPost("")]
    [AppAuthorize("wcs:lane:add")]
    [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
    [RuoYi.System.Log(Title = "巷道管理", BusinessType = BusinessType.INSERT)]
    public async Task<AjaxResult> Add([FromBody] WcsLaneDto dto)
    {
      var data = await _wcsLaneService.InsertAsync(dto);
      return AjaxResult.Success(data);
    }

    /// <summary>
    /// 修改 巷道管理
    /// </summary>
    [HttpPut("")]
    [AppAuthorize("wcs:lane:edit")]
    [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
    [RuoYi.System.Log(Title = "巷道管理", BusinessType = BusinessType.UPDATE)]
    public async Task<AjaxResult> Edit([FromBody] WcsLaneDto dto)
    {
      var data = await _wcsLaneService.UpdateAsync(dto);
      return AjaxResult.Success(data);
    }

    /// <summary>
    /// 删除 巷道管理
    /// </summary>
    [HttpDelete("{ids}")]
    [AppAuthorize("wcs:lane:remove")]
    [RuoYi.System.Log(Title = "巷道管理", BusinessType = BusinessType.DELETE)]
    public async Task<AjaxResult> Remove(string ids)
    {
      var idList = ids.SplitToList<long>();
      var data = await _wcsLaneService.DeleteAsync(idList);
      return AjaxResult.Success(data);
    }

    /// <summary>
    /// 导入 巷道管理
    /// </summary>
    [HttpPost("import")]
    [AppAuthorize("wcs:lane:import")]
    [RuoYi.System.Log(Title = "巷道管理", BusinessType = BusinessType.IMPORT)]
    public async Task Import([Required] IFormFile file)
    {
      var stream = new MemoryStream();
      file.CopyTo(stream);
      var list = await ExcelUtils.ImportAsync<WcsLaneDto>(stream);
      await _wcsLaneService.ImportDtoBatchAsync(list);
    }

    /// <summary>
    /// 导出 巷道管理
    /// </summary>
    [HttpPost("export")]
    [AppAuthorize("wcs:lane:export")]
    [RuoYi.System.Log(Title = "巷道管理", BusinessType = BusinessType.EXPORT)]
    public async Task Export(WcsLaneDto dto)
    {
      var list = await _wcsLaneService.GetDtoListAsync(dto);
      await ExcelUtils.ExportAsync(App.HttpContext.Response, list);
    }
  }
}

using SqlSugar;
using System;
using System.Collections.Generic;
using RuoYi.Data.Entities;

namespace ISDN.WCS.Data.Entities
{
    /// <summary>
    ///  巷道管理 对象 wcs_lane
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:42
    /// </summary>
    [SugarTable("wcs_lane", "巷道管理")]
    public class WcsLane : IsdnBaseEntity
  {
        /// <summary>
        /// 巷道名称 (lane_name)
        /// </summary>
        [SugarColumn(ColumnName = "lane_name", ColumnDescription = "巷道名称")]
        public string LaneName { get; set; }
        
        /// <summary>
        /// 巷道ID (lane_id)
        /// </summary>
        [SugarColumn(ColumnName = "lane_id", ColumnDescription = "巷道ID", IsPrimaryKey = true, IsIdentity = true)]
        public long LaneId { get; set; }
        
        /// <summary>
        /// 巷道类型 (lane_type)
        /// </summary>
        [SugarColumn(ColumnName = "lane_type", ColumnDescription = "巷道类型")]
        public int LaneType { get; set; }
        
        /// <summary>
        /// 创建人id (created_by)
        /// </summary>
        [SugarColumn(ColumnName = "created_by", ColumnDescription = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id (updated_by)
        /// </summary>
        [SugarColumn(ColumnName = "updated_by", ColumnDescription = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间 (created_time)
        /// </summary>
        [SugarColumn(ColumnName = "created_time", ColumnDescription = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间 (updated_time)
        /// </summary>
        [SugarColumn(ColumnName = "updated_time", ColumnDescription = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
        /// <summary>
        /// 巷道编号 (lane_code)
        /// </summary>
        [SugarColumn(ColumnName = "lane_code", ColumnDescription = "巷道编号")]
        public string LaneCode { get; set; }


    /// <summary>
    /// 设备ID (device_id)
    /// </summary>
    [SugarColumn(ColumnName = "device_id", ColumnDescription = "设备ID")]
    public long DeviceId { get; set; }

  }
}

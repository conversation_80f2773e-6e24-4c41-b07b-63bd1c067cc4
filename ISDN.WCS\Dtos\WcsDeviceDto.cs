using System.Collections.Generic;
using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;

namespace ISDN.WCS.Data.Dtos
{
    /// <summary>
    ///  设备管理 对象 wcs_device
    ///  author zgq
    ///  date   2025-08-13 16:36:40
    /// </summary>
    public class WcsDeviceDto : BaseDto
    {
        /// <summary>
        /// 设备编号
        /// </summary>
        [Excel(Name = "设备编号")]
        public string DeviceCode { get; set; }
        
        /// <summary>
        /// 设备名称
        /// </summary>
        [Excel(Name = "设备名称")]
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 设备类型
        /// </summary>
        [Excel(Name = "设备类型")]
        public int? DeviceType { get; set; }
        
        /// <summary>
        /// 通信地址
        /// </summary>
        [Excel(Name = "通信地址")]
        public string? Addr { get; set; }
        
        /// <summary>
        /// 端口号
        /// </summary>
        [Excel(Name = "端口号")]
        public int? Port { get; set; }
        
        /// <summary>
        /// 在线状态
        /// </summary>
        [Excel(Name = "在线状态")]
        public int? Online { get; set; }
        
        /// <summary>
        /// 运行状态
        /// </summary>
        [Excel(Name = "运行状态")]
        public int? RunStatus { get; set; }
        
        /// <summary>
        /// 设备ID
        /// </summary>
        [Excel(Name = "设备ID")]
        public long? DeviceId { get; set; }
        
        /// <summary>
        /// 生效
        /// </summary>
        [Excel(Name = "生效")]
        public int? IsVisibleFlag { get; set; }
        
        /// <summary>
        /// 创建人id
        /// </summary>
        [Excel(Name = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id
        /// </summary>
        [Excel(Name = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Excel(Name = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间
        /// </summary>
        [Excel(Name = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
    }
}

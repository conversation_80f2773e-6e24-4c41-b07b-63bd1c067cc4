info: 2025-08-27 13:10:12.3005095 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 13:10:12.3029768 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 13:10:12.3042893 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 13:10:12.3886990 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 13:10:12.3898636 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 13:10:12.3908516 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs1\RuoYi.Admin
info: 2025-08-27 13:10:13.4920343 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-fe405d0d08b73656b9b798206c243f6c-ea6e25ebc93b7644-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  62ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 13:32:51.7089441 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 13:32:51.7112188 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 13:32:51.7124102 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 13:32:51.7804464 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 13:32:51.7822991 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 13:32:51.8109632 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-27 13:32:53.2211015 +08:00 星期三 L System.Logging.LoggingMonitor[0] #26
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-48afc43bfa2dd0e50e8bc40e0bdefc2a-42387564c301c86d-00
      ┣ 服务线程 ID：               #26
      ┣ 执行耗时：                  45ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 13:33:52.9571470 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 13:33:52.9594968 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 13:33:52.9604503 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 13:33:53.0322140 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 13:33:53.0678689 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 13:33:53.0690551 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-27 13:33:54.2722236 +08:00 星期三 L System.Logging.LoggingMonitor[0] #25
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-e009486bfc2ea465046c884a8482334a-67626241e0122120-00
      ┣ 服务线程 ID：               #25
      ┣ 执行耗时：                  38ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 13:34:28.0387921 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 13:34:28.0411665 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 13:34:28.0421225 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 13:34:28.1154202 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 13:34:28.1165062 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 13:34:28.1174790 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-27 13:34:29.6510704 +08:00 星期三 L System.Logging.LoggingMonitor[0] #27
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-0e32fae8b63f3cb351200a0f951e9c00-0a922345e99a0781-00
      ┣ 服务线程 ID：               #27
      ┣ 执行耗时：                  37ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 13:47:48.1116487 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 13:47:48.1138482 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 13:47:48.1148204 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 13:47:48.1827285 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 13:47:48.1839058 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 13:47:48.2062930 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-27 13:47:49.4762736 +08:00 星期三 L System.Logging.LoggingMonitor[0] #28
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-a14d26001e4930519846bd0994fd0c1d-4946b1d0f776249c-00
      ┣ 服务线程 ID：               #9
      ┣ 执行耗时：                  28ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 13:53:26.7949824 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 13:53:26.8237206 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 13:53:26.8262365 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 13:53:26.8970611 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 13:53:26.9310892 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 13:53:26.9331320 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-27 13:53:28.2138223 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-6c3b61fa482fef8ca5908da72d30842d-8ba15c0f4f650df2-00
      ┣ 服务线程 ID：               #23
      ┣ 执行耗时：                  58ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 13:55:04.2719717 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 13:55:04.2740722 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 13:55:04.2750264 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 13:55:04.3433314 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 13:55:04.3445152 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 13:55:04.3454934 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-27 13:55:05.6063482 +08:00 星期三 L System.Logging.LoggingMonitor[0] #29
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-fd9a1562c731740173ad55bea0aa6068-47306534c96a6f83-00
      ┣ 服务线程 ID：               #28
      ┣ 执行耗时：                  22ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:16.3665470 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://localhost:5000
info: 2025-08-27 14:00:16.3686742 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: https://localhost:5001
info: 2025-08-27 14:00:16.3700470 +08:00 星期三 L Microsoft.Hosting.Lifetime[14] #1
      Now listening on: http://0.0.0.0:5000
info: 2025-08-27 14:00:16.4396822 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Application started. Press Ctrl+C to shut down.
info: 2025-08-27 14:00:16.4407251 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Hosting environment: Development
info: 2025-08-27 14:00:16.4416908 +08:00 星期三 L Microsoft.Hosting.Lifetime[0] #1
      Content root path: E:\SourceCode\SelfSVNCode\WCS\isdn_wcs\RuoYi.Admin
info: 2025-08-27 14:00:17.7134047 +08:00 星期三 L System.Logging.LoggingMonitor[0] #23
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-4d0c39649db2e4a5b2b29d2f7c874832-fd4ecda2ad2ff1bb-00
      ┣ 服务线程 ID：               #10
      ┣ 执行耗时：                  25ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:20.2271923 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-2c1b884a0fbaa537729979de87d7b1f1-c39c099c9d734112-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  6ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:21.2710969 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-712ae9784415dbb0bd85a8ff8b25268a-8077be33a081beb2-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  3ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:21.7065083 +08:00 星期三 L System.Logging.LoggingMonitor[0] #10
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-ac65aaae90457f531158813a724f8a96-e99ae45b86088fad-00
      ┣ 服务线程 ID：               #10
      ┣ 执行耗时：                  4ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:22.0072559 +08:00 星期三 L System.Logging.LoggingMonitor[0] #10
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-aa223bfdb8f8a5c19bcf4cd94ce20339-3f53082723b1dad0-00
      ┣ 服务线程 ID：               #10
      ┣ 执行耗时：                  5ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:22.3107949 +08:00 星期三 L System.Logging.LoggingMonitor[0] #10
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-4815dee457b8a014eb64af57cbf12a35-931a859146be3eed-00
      ┣ 服务线程 ID：               #10
      ┣ 执行耗时：                  4ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:22.6147525 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-907c5da740e3d7dfac527468ab0bd303-0db1f285769282ef-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  3ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:22.8983074 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-f8a2a9522d09b6e77320c42f31e89d08-33652a0519651d39-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  3ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:23.1931784 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-f832709c409c47ecf34b1708709f1f57-4c1fc020199003d6-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  5ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:23.5268964 +08:00 星期三 L System.Logging.LoggingMonitor[0] #12
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-29432078998e5fb29047956fb840fd84-fee8ab62fef52f28-00
      ┣ 服务线程 ID：               #12
      ┣ 执行耗时：                  4ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:23.9495860 +08:00 星期三 L System.Logging.LoggingMonitor[0] #10
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-a2872e43c4afa9c25425ac40694afeeb-4c5520b592a6fc82-00
      ┣ 服务线程 ID：               #10
      ┣ 执行耗时：                  3ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:00:24.3424807 +08:00 星期三 L System.Logging.LoggingMonitor[0] #24
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-10833a7551bf261eb2ad51a9c0744cb8-e63f5bb75c11685a-00
      ┣ 服务线程 ID：               #24
      ┣ 执行耗时：                  3ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:09.1956266 +08:00 星期三 L System.Logging.LoggingMonitor[0] #29
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-af389c7cc49d8f8f7b9959984c16daeb-57b368b0ca9a4956-00
      ┣ 服务线程 ID：               #29
      ┣ 执行耗时：                  92ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"cb89ab36-fda6-4463-9c24-db1b3618fcd0","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:15.6858820 +08:00 星期三 L System.Logging.LoggingMonitor[0] #30
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.CaptchaController.GetCaptchaImage
      ┣ 
      ┣ 控制器名称：                CaptchaController
      ┣ 操作名称：                  GetCaptchaImage
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Captcha; [action]: GetCaptchaImage
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/captchaImage
      ┣ 来源地址：                  http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-73759a2ab463c1e446f3729c810a4f9f-f5ccff91d766645e-00
      ┣ 服务线程 ID：               #30
      ┣ 执行耗时：                  1ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    application/json, text/plain, */*
      ┣ Connection：                close
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Referer：                   http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ istoken：                   false
      ┣ sec-ch-ua-mobile：          ?0
      ┣ sec-fetch-site：            same-origin
      ┣ sec-fetch-mode：            cors
      ┣ sec-fetch-dest：            empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Object
      ┣ 最终类型：                  <>f__AnonymousType1<System.String, System.String>
      ┣ 最终返回值：                {"uuid":"5cb3216a-aaf7-4333-986b-7a7f3104f8b4","img":"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"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:18.4906548 +08:00 星期三 L RuoYi.System.Services.SysLogininforService[0] #31
      []X.X.X.X[admin][Error][密码输入错误1次]
info: 2025-08-27 14:06:18.5367925 +08:00 星期三 L RuoYi.System.Services.SysLogininforService[0] #30
      []X.X.X.X[admin][Error][用户不存在/密码错误]
info: 2025-08-27 14:06:23.8742477 +08:00 星期三 L RuoYi.System.Services.SysLogininforService[0] #31
      [*******]X.X.X.X[admin][Success][登录成功]
info: 2025-08-27 14:06:24.0044589 +08:00 星期三 L System.Logging.LoggingMonitor[0] #31
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.Login
      ┣ 
      ┣ 控制器名称：                   SysLoginController
      ┣ 操作名称：                     Login
      ┣ 显示名称：                     
      ┣ 路由信息：                     [area]: ; [controller]: SysLogin; [action]: Login
      ┣ 请求方式：                     POST
      ┣ 请求地址：                     http://localhost:5000/login
      ┣ 来源地址：                     http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                     client
      ┣ 浏览器标识：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：               zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：               *******
      ┣ 服务端 IP 地址：               *******
      ┣ 客户端连接 ID：                00-21aaf0f17891ba7bbf91cc27c5e2c5e4-9cbb7418ead54693-00
      ┣ 服务线程 ID：                  #31
      ┣ 执行耗时：                     289ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                       ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}
      ┣ 响应端：                       
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                     Microsoft Windows 10.0.26100
      ┣ 系统架构：                     X64
      ┣ 基础框架：                     RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                    .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                     Development
      ┣ 启动程序集：                   RuoYi.Admin
      ┣ 进程名称：                     RuoYi.Admin
      ┣ 托管程序：                     Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                       application/json, text/plain, */*
      ┣ Connection：                   close
      ┣ Host：                         localhost:5000
      ┣ User-Agent：                   Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：              gzip, deflate, br, zstd
      ┣ Accept-Language：              zh-CN,zh;q=0.9
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ Cookie：                       ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D
      ┣ Origin：                       http://localhost:5000
      ┣ Referer：                      http://localhost:10081/login?redirect=/index
      ┣ Content-Length：               99
      ┣ sec-ch-ua-platform：           "Windows"
      ┣ repeatsubmit：                 false
      ┣ sec-ch-ua：                    "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：             ?0
      ┣ istoken：                      false
      ┣ sec-fetch-site：               same-origin
      ┣ sec-fetch-mode：               cors
      ┣ sec-fetch-dest：               empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                 application/json;charset=UTF-8
      ┣ 
      ┣ loginBody (LoginBody)：        {"username":"admin","password":"a-123456","code":"3","uuid":"5cb3216a-aaf7-4333-986b-7a7f3104f8b4"}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：               200
      ┣ 原始类型：                     System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                     RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                   {"msg":"操作成功.","token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o","code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:24.1587647 +08:00 星期三 L System.Logging.LoggingMonitor[0] #31
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetInfo
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetInfo
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetInfo
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getInfo
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-1858640169f05d3732a8889de16a2855-1272260f4599d5bc-00
      ┣ 服务线程 ID：                    #31
      ┣ 执行耗时：                       30ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"permissions":["*:*:*"],"roles":["admin"],"msg":"操作成功.","user":{"userId":1,"deptId":103,"userName":"admin","nickName":"isdn","email":"<EMAIL>","phonenumber":"15888888888","sex":"0","sexDesc":null,"avatar":"","status":"0","statusDesc":null,"delFlag":"0","loginIp":"*************","loginDate":"2025-08-27T10:29:54","dept":{"deptId":103,"parentId":101,"ancestors":"0,100,101","deptName":"研发部门","orderNum":1,"leader":"若依","phone":"15888888888","email":"<EMAIL>","status":"0","delFlag":"0","parentName":null,"children":[],"deptCheckStrictly":null,"roleId":null,"parentIds":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"","updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"deptName":null,"deptLeader":null,"roles":[{"roleId":1,"roleName":"超级管理员","roleKey":"admin","roleSort":1,"dataScope":"1","dataScopeDesc":null,"menuCheckStrictly":true,"deptCheckStrictly":true,"status":"0","statusDesc":null,"delFlag":"0","flag":false,"menuIds":null,"deptIds":null,"permissions":null,"userId":null,"userName":null,"createBy":"admin","createTime":"2024-11-06T07:07:10","updateBy":"","updateTime":null,"remark":"超级管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"roleIds":null,"postIds":null,"roleId":null,"isAllocated":null,"createBy":"admin","createTime":"2024-11-06T07:07:09","updateBy":"admin","updateTime":"2025-08-19T17:11:13","remark":"管理员","params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:24.2698617 +08:00 星期三 L System.Logging.LoggingMonitor[0] #31
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SysLoginController.GetRouters
      ┣ 
      ┣ 控制器名称：                     SysLoginController
      ┣ 操作名称：                       GetRouters
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysLogin; [action]: GetRouters
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/getRouters
      ┣ 来源地址：                       http://localhost:10081/login?redirect=/index
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-9508ff20afb94f8118b6830c10d5f9d0-ea999a3fe52dc633-00
      ┣ 服务线程 ID：                    #31
      ┣ 执行耗时：                       52ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/login?redirect=/index
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"name":"System","path":"/system","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统管理","icon":"system","noCache":false,"link":null},"children":[{"name":"User","path":"user","hidden":false,"redirect":null,"component":"system/user/index","query":"","alwaysShow":false,"meta":{"title":"用户管理","icon":"user","noCache":false,"link":null},"children":null},{"name":"Role","path":"role","hidden":false,"redirect":null,"component":"system/role/index","query":"","alwaysShow":false,"meta":{"title":"角色管理","icon":"peoples","noCache":false,"link":null},"children":null},{"name":"Menu","path":"menu","hidden":false,"redirect":null,"component":"system/menu/index","query":"","alwaysShow":false,"meta":{"title":"菜单管理","icon":"tree-table","noCache":false,"link":null},"children":null},{"name":"Dept","path":"dept","hidden":false,"redirect":null,"component":"system/dept/index","query":"","alwaysShow":false,"meta":{"title":"部门管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Post","path":"post","hidden":false,"redirect":null,"component":"system/post/index","query":"","alwaysShow":false,"meta":{"title":"岗位管理","icon":"post","noCache":false,"link":null},"children":null},{"name":"Dict","path":"dict","hidden":false,"redirect":null,"component":"system/dict/index","query":"","alwaysShow":false,"meta":{"title":"字典管理","icon":"dict","noCache":false,"link":null},"children":null},{"name":"Config","path":"config","hidden":false,"redirect":null,"component":"system/config/index","query":"","alwaysShow":false,"meta":{"title":"参数设置","icon":"edit","noCache":false,"link":null},"children":null},{"name":"Notice","path":"notice","hidden":false,"redirect":null,"component":"system/notice/index","query":"","alwaysShow":false,"meta":{"title":"通知公告","icon":"message","noCache":false,"link":null},"children":null},{"name":"Log","path":"log","hidden":false,"redirect":"noRedirect","component":"ParentView","query":"","alwaysShow":true,"meta":{"title":"日志管理","icon":"log","noCache":false,"link":null},"children":[{"name":"Operlog","path":"operlog","hidden":false,"redirect":null,"component":"monitor/operlog/index","query":"","alwaysShow":false,"meta":{"title":"操作日志","icon":"form","noCache":false,"link":null},"children":null},{"name":"Logininfor","path":"logininfor","hidden":false,"redirect":null,"component":"monitor/logininfor/index","query":"","alwaysShow":false,"meta":{"title":"登录日志","icon":"logininfor","noCache":false,"link":null},"children":null}]}]},{"name":"Monitor","path":"/monitor","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统监控","icon":"monitor","noCache":false,"link":null},"children":[{"name":"Online","path":"online","hidden":false,"redirect":null,"component":"monitor/online/index","query":"","alwaysShow":false,"meta":{"title":"在线用户","icon":"online","noCache":false,"link":null},"children":null},{"name":"Job","path":"job","hidden":false,"redirect":null,"component":"monitor/job/index","query":"","alwaysShow":false,"meta":{"title":"定时任务","icon":"job","noCache":false,"link":null},"children":null},{"name":"Server","path":"server","hidden":false,"redirect":null,"component":"monitor/server/index","query":"","alwaysShow":false,"meta":{"title":"服务监控","icon":"server","noCache":false,"link":null},"children":null},{"name":"Cache","path":"cache","hidden":false,"redirect":null,"component":"monitor/cache/index","query":"","alwaysShow":false,"meta":{"title":"缓存监控","icon":"redis","noCache":false,"link":null},"children":null},{"name":"CacheList","path":"cacheList","hidden":false,"redirect":null,"component":"monitor/cache/list","query":"","alwaysShow":false,"meta":{"title":"缓存列表","icon":"redis-list","noCache":false,"link":null},"children":null}]},{"name":"Tool","path":"/tool","hidden":false,"redirect":"noRedirect","component":"Layout","query":"","alwaysShow":true,"meta":{"title":"系统工具","icon":"tool","noCache":false,"link":null},"children":[{"name":"Gen","path":"gen","hidden":false,"redirect":null,"component":"tool/gen/index","query":"","alwaysShow":false,"meta":{"title":"代码生成","icon":"code","noCache":false,"link":null},"children":null}]},{"name":"Wcs","path":"/wcs","hidden":false,"redirect":"noRedirect","component":"Layout","query":null,"alwaysShow":true,"meta":{"title":"wcs管理","icon":"international","noCache":false,"link":null},"children":[{"name":"Device","path":"device","hidden":false,"redirect":null,"component":"wcs/device/index","query":null,"alwaysShow":false,"meta":{"title":"设备管理","icon":"shopping","noCache":false,"link":null},"children":null},{"name":"Lane","path":"lane","hidden":false,"redirect":null,"component":"wcs/lane/index","query":null,"alwaysShow":false,"meta":{"title":"巷道管理","icon":"table","noCache":false,"link":null},"children":null},{"name":"Location","path":"location","hidden":false,"redirect":null,"component":"wcs/location/index","query":null,"alwaysShow":false,"meta":{"title":"库位管理","icon":"tree","noCache":false,"link":null},"children":null},{"name":"Container","path":"container","hidden":false,"redirect":null,"component":"wcs/container/index","query":null,"alwaysShow":false,"meta":{"title":"容器档","icon":"build","noCache":false,"link":null},"children":null},{"name":"Task","path":"task","hidden":false,"redirect":null,"component":"wcs/task/index","query":null,"alwaysShow":false,"meta":{"title":"WCS任务档","icon":"email","noCache":false,"link":null},"children":null}]}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:29.0151033 +08:00 星期三 L System.Logging.LoggingMonitor[0] #31
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/task_status
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-fe7a352e16376bf73e285c5cbb1c4c18-5f339d4d42e4bb35-00
      ┣ 服务线程 ID：                    #31
      ┣ 执行耗时：                       5ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              task_status
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":118,"dictSort":1,"dictLabel":"未执行","dictValue":"1","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:03","updateBy":null,"updateTime":null},{"dictCode":119,"dictSort":2,"dictLabel":"执行中","dictValue":"2","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:18","updateBy":null,"updateTime":null},{"dictCode":120,"dictSort":3,"dictLabel":"执行失败","dictValue":"3","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:29","updateBy":null,"updateTime":null},{"dictCode":121,"dictSort":4,"dictLabel":"执行成功","dictValue":"4","dictType":"task_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:13:38","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:29.0752515 +08:00 星期三 L System.Logging.LoggingMonitor[0] #27
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/is_visible_flag
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-c52e7132d6a62fbcb9b1ca186b768053-da0171d6ed48a10b-00
      ┣ 服务线程 ID：                    #27
      ┣ 执行耗时：                       4ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              is_visible_flag
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":109,"dictSort":0,"dictLabel":"是","dictValue":"1","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:36","updateBy":"admin","updateTime":"2025-08-13T16:43:30"},{"dictCode":110,"dictSort":2,"dictLabel":"否","dictValue":"2","dictType":"is_visible_flag","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:33:44","updateBy":"admin","updateTime":"2025-08-13T16:43:35"}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:29.1091339 +08:00 星期三 L System.Logging.LoggingMonitor[0] #30
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/task_type
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-d65b69eb44f3a145e3e71e7be93bff3c-5594c677fa3620cd-00
      ┣ 服务线程 ID：                    #30
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              task_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":122,"dictSort":1,"dictLabel":"入库","dictValue":"1","dictType":"task_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:54:54","updateBy":null,"updateTime":null},{"dictCode":123,"dictSort":2,"dictLabel":"出库","dictValue":"2","dictType":"task_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:55:03","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:29.1929958 +08:00 星期三 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsTaskController.GetWcsTaskPagedList
      ┣ 
      ┣ 控制器名称：                     WcsTaskController
      ┣ 操作名称：                       GetWcsTaskPagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsTask; [action]: GetWcsTaskPagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/task/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/task
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-7506602f816ec2ede4215b194235a41c-047813c73f9bd42c-00
      ┣ 服务线程 ID：                    #5
      ┣ 执行耗时：                       244ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/task
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsTaskDto)：               {"taskId":null,"taskCode":null,"binNo":0,"equipmentNo":null,"taskType":0,"startLocation":null,"endLocation":null,"retries":null,"maxRetries":null,"errMsg":null,"taskStatus":null,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsTaskDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsTaskDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":1,"rows":[{"taskId":1,"taskCode":null,"binNo":0,"equipmentNo":null,"taskType":1,"startLocation":5675,"endLocation":56756,"retries":55,"maxRetries":66,"errMsg":null,"taskStatus":null,"isVisibleFlag":2,"createdBy":1,"updatedBy":1,"createdTime":"2025-08-13T17:29:33","updatedTime":"2025-08-13T17:46:30","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:37.0235052 +08:00 星期三 L System.Logging.LoggingMonitor[0] #27
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_online
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-3f03e6c12f8dc997cf9982b00cae58b6-efa22f64e05db582-00
      ┣ 服务线程 ID：                    #27
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_online
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":100,"dictSort":1,"dictLabel":"未知","dictValue":"1","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:36","updateBy":"admin","updateTime":"2025-08-06T10:26:09"},{"dictCode":101,"dictSort":2,"dictLabel":"在线","dictValue":"2","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:47","updateBy":"admin","updateTime":"2025-08-06T10:26:05"},{"dictCode":102,"dictSort":3,"dictLabel":"离线","dictValue":"3","dictType":"device_online","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:25:57","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:37.0279508 +08:00 星期三 L System.Logging.LoggingMonitor[0] #31
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_type
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-71f8e6117cd41122b34834112efd7b70-edce4cc9fb656fd4-00
      ┣ 服务线程 ID：                    #31
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":107,"dictSort":0,"dictLabel":"堆垛机","dictValue":"1","dictType":"device_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:30:45","updateBy":null,"updateTime":null},{"dictCode":108,"dictSort":2,"dictLabel":"输送线","dictValue":"2","dictType":"device_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:30:56","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:37.0480880 +08:00 星期三 L System.Logging.LoggingMonitor[0] #5
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/device_run_status
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-17f563018f2ba07ab6be225eaf16c9f5-c782d14aed3302d3-00
      ┣ 服务线程 ID：                    #5
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              device_run_status
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":103,"dictSort":1,"dictLabel":"未知","dictValue":"1","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:26:58","updateBy":null,"updateTime":null},{"dictCode":104,"dictSort":2,"dictLabel":"运行","dictValue":"2","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:10","updateBy":null,"updateTime":null},{"dictCode":105,"dictSort":3,"dictLabel":"停止","dictValue":"3","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:23","updateBy":"admin","updateTime":"2025-08-06T10:27:28"},{"dictCode":106,"dictSort":4,"dictLabel":"报警","dictValue":"4","dictType":"device_run_status","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-06T10:27:41","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:37.0724464 +08:00 星期三 L System.Logging.LoggingMonitor[0] #28
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsDeviceController.GetWcsDevicePagedList
      ┣ 
      ┣ 控制器名称：                     WcsDeviceController
      ┣ 操作名称：                       GetWcsDevicePagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsDevice; [action]: GetWcsDevicePagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/device/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/device
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-ade45abe813169aed957e17ecfb7382d-2969b1d1c91b7572-00
      ┣ 服务线程 ID：                    #14
      ┣ 执行耗时：                       81ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/device
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsDeviceDto)：             {"deviceCode":null,"deviceName":null,"deviceType":null,"addr":null,"port":null,"online":null,"runStatus":null,"deviceId":null,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsDeviceDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsDeviceDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":3,"rows":[{"deviceCode":"a1","deviceName":"b2","deviceType":1,"addr":"127.0.0.1","port":60001,"online":2,"runStatus":2,"deviceId":1,"isVisibleFlag":1,"createdBy":0,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-27T14:00:15","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"234","deviceName":"234","deviceType":1,"addr":"127.0.0.1","port":60002,"online":3,"runStatus":null,"deviceId":2,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-27T14:00:32","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"deviceCode":"23","deviceName":"23","deviceType":1,"addr":"127.0.0.1","port":60003,"online":3,"runStatus":null,"deviceId":3,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-27T14:06:10","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:38.7489122 +08:00 星期三 L System.Logging.LoggingMonitor[0] #14
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsDeviceController.GetList
      ┣ 
      ┣ 控制器名称：                     WcsDeviceController
      ┣ 操作名称：                       GetList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsDevice; [action]: GetList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/device/listAll
      ┣ 来源地址：                       http://localhost:10081/wcs/lane
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-de0a2559186748b7fd1076d9c1551d56-da579f29212a6bb4-00
      ┣ 服务线程 ID：                    #14
      ┣ 执行耗时：                       6ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/lane
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","code":200,"lanes":[{"deviceCode":"a1","deviceName":"b2","deviceType":1,"addr":"127.0.0.1","port":60001,"online":2,"runStatus":2,"deviceId":1,"isVisibleFlag":1,"createdBy":0,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-27T14:00:15"},{"deviceCode":"234","deviceName":"234","deviceType":1,"addr":"127.0.0.1","port":60002,"online":3,"runStatus":null,"deviceId":2,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-27T14:00:32"},{"deviceCode":"23","deviceName":"23","deviceType":1,"addr":"127.0.0.1","port":60003,"online":3,"runStatus":null,"deviceId":3,"isVisibleFlag":1,"createdBy":null,"updatedBy":0,"createdTime":null,"updatedTime":"2025-08-27T14:06:10"}]}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:38.7570931 +08:00 星期三 L System.Logging.LoggingMonitor[0] #27
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/lane_type
      ┣ 来源地址：                       http://localhost:10081/wcs/lane
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-a32f251c7dcc09af4cc3231b051cc6b8-80fcbbb505eb497f-00
      ┣ 服务线程 ID：                    #27
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/lane
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              lane_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":111,"dictSort":1,"dictLabel":"存储巷道","dictValue":"1","dictType":"lane_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:52:42","updateBy":null,"updateTime":null},{"dictCode":112,"dictSort":1,"dictLabel":"分拣巷道","dictValue":"2","dictType":"lane_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T14:52:50","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:38.7989979 +08:00 星期三 L System.Logging.LoggingMonitor[0] #28
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsLaneController.GetWcsLanePagedList
      ┣ 
      ┣ 控制器名称：                     WcsLaneController
      ┣ 操作名称：                       GetWcsLanePagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsLane; [action]: GetWcsLanePagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/lane/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/lane
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-995f250c4c04459990a4ce99e18a49c2-75fdfc7355070772-00
      ┣ 服务线程 ID：                    #28
      ┣ 执行耗时：                       102ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/lane
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsLaneDto)：               {"laneName":null,"laneId":null,"laneType":0,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"laneCode":null,"deviceId":0,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsLaneDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsLaneDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":5,"rows":[{"laneName":"1巷道","laneId":1,"laneType":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-19T19:38:16","laneCode":"2","deviceId":1,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"laneName":"2巷道","laneId":2,"laneType":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-19T19:38:20","laneCode":"4","deviceId":2,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"laneName":"3巷道","laneId":3,"laneType":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-14T13:26:05","laneCode":"345","deviceId":0,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"laneName":"4巷道","laneId":4,"laneType":2,"createdBy":null,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-14T13:26:10","laneCode":"123","deviceId":0,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"laneName":"5巷道","laneId":5,"laneType":1,"createdBy":1,"updatedBy":1,"createdTime":"2025-08-13T17:17:10","updatedTime":"2025-08-14T13:26:15","laneCode":"12","deviceId":0,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:42.0465861 +08:00 星期三 L System.Logging.LoggingMonitor[0] #14
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.System.Controllers.SysDictDataController.GetListByDictType
      ┣ 
      ┣ 控制器名称：                     SysDictDataController
      ┣ 操作名称：                       GetListByDictType
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: SysDictData; [action]: GetListByDictType
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/system/dict/data/type/location_type
      ┣ 来源地址：                       http://localhost:10081/wcs/location
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-4bd7eb52fa818d3d0f02a3aedf58a1ff-30fa69759c5aadc6-00
      ┣ 服务线程 ID：                    #14
      ┣ 执行耗时：                       0ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/location
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dictType (String)：              location_type
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":[{"dictCode":113,"dictSort":1,"dictLabel":"储存位置","dictValue":"1","dictType":"location_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:03:48","updateBy":null,"updateTime":null},{"dictCode":114,"dictSort":0,"dictLabel":"接驳位置","dictValue":"2","dictType":"location_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:03:58","updateBy":null,"updateTime":null},{"dictCode":115,"dictSort":3,"dictLabel":"分拣位置","dictValue":"3","dictType":"location_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:04:09","updateBy":null,"updateTime":null},{"dictCode":116,"dictSort":4,"dictLabel":"巷道位置","dictValue":"4","dictType":"location_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:04:19","updateBy":null,"updateTime":null},{"dictCode":117,"dictSort":5,"dictLabel":"上架位置","dictValue":"5","dictType":"location_type","cssClass":null,"listClass":"default","isDefault":null,"status":"0","remark":null,"createBy":"admin","createTime":"2025-08-11T15:04:26","updateBy":null,"updateTime":null}],"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:42.0852491 +08:00 星期三 L System.Logging.LoggingMonitor[0] #28
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsLaneController.GetList
      ┣ 
      ┣ 控制器名称：                     WcsLaneController
      ┣ 操作名称：                       GetList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsLane; [action]: GetList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/lane/listAll
      ┣ 来源地址：                       http://localhost:10081/wcs/location
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-f7b8123bfae5eba63766d5fd6bd8478e-3c9b86f341fb8108-00
      ┣ 服务线程 ID：                    #27
      ┣ 执行耗时：                       62ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/location
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","code":200,"lanes":[{"laneName":"1巷道","laneId":1,"laneType":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-19T19:38:16","laneCode":"2","deviceId":1},{"laneName":"2巷道","laneId":2,"laneType":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-19T19:38:20","laneCode":"4","deviceId":2},{"laneName":"3巷道","laneId":3,"laneType":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-14T13:26:05","laneCode":"345","deviceId":0},{"laneName":"4巷道","laneId":4,"laneType":2,"createdBy":null,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-14T13:26:10","laneCode":"123","deviceId":0},{"laneName":"5巷道","laneId":5,"laneType":1,"createdBy":1,"updatedBy":1,"createdTime":"2025-08-13T17:17:10","updatedTime":"2025-08-14T13:26:15","laneCode":"12","deviceId":0}]}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:42.1198259 +08:00 星期三 L System.Logging.LoggingMonitor[0] #31
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsWarehouseLocationController.GetWcsWarehouseLocationPagedList
      ┣ 
      ┣ 控制器名称：                           WcsWarehouseLocationController
      ┣ 操作名称：                             GetWcsWarehouseLocationPagedList
      ┣ 显示名称：                             
      ┣ 路由信息：                             [area]: ; [controller]: WcsWarehouseLocation; [action]: GetWcsWarehouseLocationPagedList
      ┣ 请求方式：                             GET
      ┣ 请求地址：                             http://localhost:5000/wcs/location/list?pageNum=1&pageSize=10
      ┣ 来源地址：                             http://localhost:10081/wcs/location
      ┣ 请求端源：                             client
      ┣ 浏览器标识：                           Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                       zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                       *******
      ┣ 服务端 IP 地址：                       *******
      ┣ 客户端连接 ID：                        00-143f2590185d923161ac24a936fc52b0-f1d5001058dbe29f-00
      ┣ 服务线程 ID：                          #28
      ┣ 执行耗时：                             47ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                               ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                               
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                             Microsoft Windows 10.0.26100
      ┣ 系统架构：                             X64
      ┣ 基础框架：                             RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                            .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                             Development
      ┣ 启动程序集：                           RuoYi.Admin
      ┣ 进程名称：                             RuoYi.Admin
      ┣ 托管程序：                             Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                            Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：              9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：                   1
      ┣ UserName (string)：                    admin
      ┣ DeptId (integer32)：                   103
      ┣ exp (integer64)：                      1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                      1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                      1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                         ruoyi.net.issuer
      ┣ aud (string)：                         ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                               application/json, text/plain, */*
      ┣ Connection：                           close
      ┣ Host：                                 localhost:5000
      ┣ User-Agent：                           Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                      gzip, deflate, br, zstd
      ┣ Accept-Language：                      zh-CN,zh;q=0.9
      ┣ Authorization：                        Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                               ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                              http://localhost:10081/wcs/location
      ┣ sec-ch-ua-platform：                   "Windows"
      ┣ sec-ch-ua：                            "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：                     ?0
      ┣ sec-fetch-site：                       same-origin
      ┣ sec-fetch-mode：                       cors
      ┣ sec-fetch-dest：                       empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                         
      ┣ 
      ┣ dto (WcsWarehouseLocationDto)：        {"shelves":null,"x":null,"y":null,"z":null,"layer":null,"deep":null,"locationId":null,"locationName":null,"locationCode":null,"laneId":null,"laneName":null,"locationType":null,"isVisibleFlag":null,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                       200
      ┣ 原始类型：                             System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsWarehouseLocationDto>>
      ┣ 最终类型：                             SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsWarehouseLocationDto>
      ┣ 最终返回值：                           {"pageIndex":1,"pageSize":10,"total":2,"rows":[{"shelves":"4","x":null,"y":null,"z":null,"layer":3,"deep":0,"locationId":1,"locationName":"1","locationCode":"2","laneId":5,"laneName":null,"locationType":2,"isVisibleFlag":1,"createdBy":0,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-14T14:21:12","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"shelves":"123","x":"1","y":"2","z":"2","layer":3,"deep":1,"locationId":2,"locationName":"123","locationCode":"123","laneId":4,"laneName":null,"locationType":1,"isVisibleFlag":1,"createdBy":null,"updatedBy":1,"createdTime":null,"updatedTime":"2025-08-14T13:31:48","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:43.8716248 +08:00 星期三 L System.Logging.LoggingMonitor[0] #31
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsContainerController.GetWcsContainerPagedList
      ┣ 
      ┣ 控制器名称：                     WcsContainerController
      ┣ 操作名称：                       GetWcsContainerPagedList
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsContainer; [action]: GetWcsContainerPagedList
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/container/list?pageNum=1&pageSize=10
      ┣ 来源地址：                       http://localhost:10081/wcs/container
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-a9a1359865f6f84417aaeb594b75721c-12936beed351ce34-00
      ┣ 服务线程 ID：                    #31
      ┣ 执行耗时：                       32ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/container
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ dto (WcsContainerDto)：          {"containerId":null,"containerName":null,"containerCode":null,"locationId":null,"currentAdrr":null,"isVisibleFlag":0,"createdBy":null,"updatedBy":null,"createdTime":null,"updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsContainerDto>>
      ┣ 最终类型：                       SqlSugar.SqlSugarPagedList<ISDN.WCS.Data.Dtos.WcsContainerDto>
      ┣ 最终返回值：                     {"pageIndex":1,"pageSize":10,"total":2,"rows":[{"containerId":1,"containerName":"222","containerCode":"3333","locationId":null,"currentAdrr":null,"isVisibleFlag":2,"createdBy":1,"updatedBy":1,"createdTime":"2025-08-13T17:29:09","updatedTime":"2025-08-13T17:29:18","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},{"containerId":2,"containerName":"342","containerCode":"234","locationId":null,"currentAdrr":null,"isVisibleFlag":1,"createdBy":1,"updatedBy":null,"createdTime":"2025-08-13T17:45:47","updatedTime":null,"createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}}],"code":200,"hasPrevPages":false,"hasNextPages":false}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:06:59.6807877 +08:00 星期三 L System.Logging.LoggingMonitor[0] #28
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ ISDN.WCS.Controllers.WcsContainerController.Get
      ┣ 
      ┣ 控制器名称：                     WcsContainerController
      ┣ 操作名称：                       Get
      ┣ 显示名称：                       
      ┣ 路由信息：                       [area]: ; [controller]: WcsContainer; [action]: Get
      ┣ 请求方式：                       GET
      ┣ 请求地址：                       http://localhost:5000/wcs/container/1
      ┣ 来源地址：                       http://localhost:10081/wcs/container
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：                 *******
      ┣ 服务端 IP 地址：                 *******
      ┣ 客户端连接 ID：                  00-c0275aa2a01f7186e155d8d19d1fe334-dec3c7a3ca7fab92-00
      ┣ 服务线程 ID：                    #14
      ┣ 执行耗时：                       19ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                      .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     RuoYi.Admin
      ┣ 进程名称：                       RuoYi.Admin
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 
      ┣ login_user_key (string)：        9eed7e3c-2688-4097-b450-2530e752bc1e
      ┣ UserId (integer32)：             1
      ┣ UserName (string)：              admin
      ┣ DeptId (integer32)：             103
      ┣ exp (integer64)：                1756879583 (2025-09-03 14:06:23:0000(+08:00) 星期三 L)
      ┣ iat (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ nbf (integer64)：                1756274783 (2025-08-27 14:06:23:0000(+08:00) 星期三 L)
      ┣ iss (string)：                   ruoyi.net.issuer
      ┣ aud (string)：                   ruoyi.net.audience
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     close
      ┣ Host：                           localhost:5000
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Cookie：                         ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                        http://localhost:10081/wcs/container
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ sec-fetch-site：                 same-origin
      ┣ sec-fetch-mode：                 cors
      ┣ sec-fetch-dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   
      ┣ 
      ┣ id (Int64)：                     1
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       System.Threading.Tasks.Task<RuoYi.Framework.AjaxResult>
      ┣ 最终类型：                       RuoYi.Framework.AjaxResult
      ┣ 最终返回值：                     {"msg":"操作成功.","data":{"containerId":1,"containerName":"222","containerCode":"3333","locationId":null,"currentAdrr":null,"isVisibleFlag":2,"createdBy":1,"updatedBy":1,"createdTime":"2025-08-13T17:29:09","updatedTime":"2025-08-13T17:29:18","createBy":null,"createTime":null,"updateBy":null,"updateTime":null,"remark":null,"params":{"beginTime":null,"endTime":null,"dataScopeSql":null}},"code":200}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 14:12:14.2988023 +08:00 星期三 L System.Logging.LoggingMonitor[0] #36
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-82cb3e5f83fc301c80450b2163f57e1c-002fcdfb7eeb397a-00
      ┣ 服务线程 ID：               #35
      ┣ 执行耗时：                  20ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":1,"PlatformType":0}; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_65176ae7-c976-4af9-9f5c-e91c6c0c6ad3; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A1%2C%22PlatformType%22%3A0%7D; Admin-Token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2dpbl91c2VyX2tleSI6IjllZWQ3ZTNjLTI2ODgtNDA5Ny1iNDUwLTI1MzBlNzUyYmMxZSIsIlVzZXJJZCI6MSwiVXNlck5hbWUiOiJhZG1pbiIsIkRlcHRJZCI6MTAzLCJleHAiOjE3NTY4Nzk1ODMsImlhdCI6MTc1NjI3NDc4MywibmJmIjoxNzU2Mjc0NzgzLCJpc3MiOiJydW95aS5uZXQuaXNzdWVyIiwiYXVkIjoicnVveWkubmV0LmF1ZGllbmNlIn0.DdI3hnERup3QVc1uAHXDmKRura0xG7DHofAtaiUTy-o
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
info: 2025-08-27 17:34:24.4096958 +08:00 星期三 L System.Logging.LoggingMonitor[0] #51
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ RuoYi.Admin.SampleController.Get
      ┣ 
      ┣ 控制器名称：                SampleController
      ┣ 操作名称：                  Get
      ┣ 显示名称：                  
      ┣ 路由信息：                  [area]: ; [controller]: Sample; [action]: Get
      ┣ 请求方式：                  GET
      ┣ 请求地址：                  http://localhost:5000/favicon.ico
      ┣ 来源地址：                  http://localhost:5000/
      ┣ 请求端源：                  client
      ┣ 浏览器标识：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：            zh-CN,zh;q=0.9
      ┣ 客户端 IP 地址：            *******
      ┣ 服务端 IP 地址：            *******
      ┣ 客户端连接 ID：             00-ee1a9615d2e93b80c4c37947b1fc5183-366dcd3512939a4f-00
      ┣ 服务线程 ID：               #51
      ┣ 执行耗时：                  158ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                    ISDNLanguageType=1; authToken=Z6_BusModel:1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo={"BookCode":"Z6_BusModel","BookName":"开发账套","OrgId":1,"OrgCode":"Nanjing","OrgName":"南京公司","BusOrgId":1,"BusOrgCode":"Nanjing","BusOrgName":"南京公司","ClientUserId":1,"ClientUserName":"张帆2","LanguageType":1,"IsQueryFilter":true,"VersionType":0,"PlatformType":13}
      ┣ 响应端：                    
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                  Microsoft Windows 10.0.26100
      ┣ 系统架构：                  X64
      ┣ 基础框架：                  RuoYi.Framework v1.0.0.0
      ┣ .NET 架构：                 .NET 9.0.6
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ 运行环境：                  Development
      ┣ 启动程序集：                RuoYi.Admin
      ┣ 进程名称：                  RuoYi.Admin
      ┣ 托管程序：                  Kestrel
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                    image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8
      ┣ Connection：                keep-alive
      ┣ Host：                      localhost:5000
      ┣ User-Agent：                Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：           gzip, deflate, br, zstd
      ┣ Accept-Language：           zh-CN,zh;q=0.9
      ┣ Cookie：                    ISDNLanguageType=1; authToken=Z6_BusModel%3A1_652ab1e8-2469-4840-ba3f-7fbce99574fb; ClientInfo=%7B%22BookCode%22%3A%22Z6_BusModel%22%2C%22BookName%22%3A%22%E5%BC%80%E5%8F%91%E8%B4%A6%E5%A5%97%22%2C%22OrgId%22%3A1%2C%22OrgCode%22%3A%22Nanjing%22%2C%22OrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22BusOrgId%22%3A1%2C%22BusOrgCode%22%3A%22Nanjing%22%2C%22BusOrgName%22%3A%22%E5%8D%97%E4%BA%AC%E5%85%AC%E5%8F%B8%22%2C%22ClientUserId%22%3A1%2C%22ClientUserName%22%3A%22%E5%BC%A0%E5%B8%862%22%2C%22LanguageType%22%3A1%2C%22IsQueryFilter%22%3Atrue%2C%22VersionType%22%3A0%2C%22PlatformType%22%3A13%7D
      ┣ Referer：                   http://localhost:5000/
      ┣ sec-ch-ua-platform：        "Windows"
      ┣ sec-ch-ua：                 "Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"
      ┣ sec-ch-ua-mobile：          ?0
      ┣ Sec-Fetch-Site：            same-origin
      ┣ Sec-Fetch-Mode：            no-cors
      ┣ Sec-Fetch-Dest：            image
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：            200
      ┣ 原始类型：                  System.Threading.Tasks.Task<RuoYi.Data.Slave.Dtos.SlaveSysUserDto>
      ┣ 最终类型：                  
      ┣ 最终返回值：                null
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━

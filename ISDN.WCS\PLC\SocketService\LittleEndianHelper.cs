using System;
using System.Text;

namespace ISDN.WCS.PLC.SocketService;

/// <summary>
/// 小端通信助手
/// 提供小端字节序转换和处理方法
/// </summary>
public static class LittleEndianHelper
{
    /// <summary>
    /// 将16位整数转换为小端字节数组
    /// </summary>
    /// <param name="value">16位整数</param>
    /// <returns>小端字节数组</returns>
    public static byte[] GetLittleEndianBytes(short value)
    {
        byte[] bytes = BitConverter.GetBytes(value);
        if (BitConverter.IsLittleEndian)
        {
            return bytes;
        }
        Array.Reverse(bytes);
        return bytes;
    }

    /// <summary>
    /// 将32位整数转换为小端字节数组
    /// </summary>
    /// <param name="value">32位整数</param>
    /// <returns>小端字节数组</returns>
    public static byte[] GetLittleEndianBytes(int value)
    {
        byte[] bytes = BitConverter.GetBytes(value);
        if (BitConverter.IsLittleEndian)
        {
            return bytes;
        }
        Array.Reverse(bytes);
        return bytes;
    }

    /// <summary>
    /// 将64位整数转换为小端字节数组
    /// </summary>
    /// <param name="value">64位整数</param>
    /// <returns>小端字节数组</returns>
    public static byte[] GetLittleEndianBytes(long value)
    {
        byte[] bytes = BitConverter.GetBytes(value);
        if (BitConverter.IsLittleEndian)
        {
            return bytes;
        }
        Array.Reverse(bytes);
        return bytes;
    }

    /// <summary>
    /// 将单精度浮点数转换为小端字节数组
    /// </summary>
    /// <param name="value">单精度浮点数</param>
    /// <returns>小端字节数组</returns>
    public static byte[] GetLittleEndianBytes(float value)
    {
        byte[] bytes = BitConverter.GetBytes(value);
        if (BitConverter.IsLittleEndian)
        {
            return bytes;
        }
        Array.Reverse(bytes);
        return bytes;
    }

    /// <summary>
    /// 将双精度浮点数转换为小端字节数组
    /// </summary>
    /// <param name="value">双精度浮点数</param>
    /// <returns>小端字节数组</returns>
    public static byte[] GetLittleEndianBytes(double value)
    {
        byte[] bytes = BitConverter.GetBytes(value);
        if (BitConverter.IsLittleEndian)
        {
            return bytes;
        }
        Array.Reverse(bytes);
        return bytes;
    }

    /// <summary>
    /// 从小端字节数组中解析16位整数
    /// </summary>
    /// <param name="bytes">小端字节数组</param>
    /// <returns>16位整数</returns>
    public static short ToInt16(byte[] bytes)
    {
        if (bytes.Length != 2)
        {
            throw new ArgumentException("字节数组长度必须为2");
        }

        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }
        return BitConverter.ToInt16(bytes, 0);
    }

    /// <summary>
    /// 从小端字节数组中解析32位整数
    /// </summary>
    /// <param name="bytes">小端字节数组</param>
    /// <returns>32位整数</returns>
    public static int ToInt32(byte[] bytes)
    {
        if (bytes.Length != 4)
        {
            throw new ArgumentException("字节数组长度必须为4");
        }

        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }
        return BitConverter.ToInt32(bytes, 0);
    }

    /// <summary>
    /// 从小端字节数组中解析64位整数
    /// </summary>
    /// <param name="bytes">小端字节数组</param>
    /// <returns>64位整数</returns>
    public static long ToInt64(byte[] bytes)
    {
        if (bytes.Length != 8)
        {
            throw new ArgumentException("字节数组长度必须为8");
        }

        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }
        return BitConverter.ToInt64(bytes, 0);
    }

    /// <summary>
    /// 从小端字节数组中解析单精度浮点数
    /// </summary>
    /// <param name="bytes">小端字节数组</param>
    /// <returns>单精度浮点数</returns>
    public static float ToSingle(byte[] bytes)
    {
        if (bytes.Length != 4)
        {
            throw new ArgumentException("字节数组长度必须为4");
        }

        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }
        return BitConverter.ToSingle(bytes, 0);
    }

    /// <summary>
    /// 从小端字节数组中解析双精度浮点数
    /// </summary>
    /// <param name="bytes">小端字节数组</param>
    /// <returns>双精度浮点数</returns>
    public static double ToDouble(byte[] bytes)
    {
        if (bytes.Length != 8)
        {
            throw new ArgumentException("字节数组长度必须为8");
        }

        if (!BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }
        return BitConverter.ToDouble(bytes, 0);
    }

    /// <summary>
    /// 将字符串转换为小端字节数组
    /// </summary>
    /// <param name="value">字符串</param>
    /// <returns>小端字节数组</returns>
    public static byte[] GetLittleEndianBytes(string value)
    {
        return Encoding.UTF8.GetBytes(value);
    }

    /// <summary>
    /// 从小端字节数组中解析字符串
    /// </summary>
    /// <param name="bytes">小端字节数组</param>
    /// <returns>字符串</returns>
    public static string ToString(byte[] bytes)
    {
        return Encoding.UTF8.GetString(bytes);
    }

    /// <summary>
    /// 将字节数组转换为十六进制字符串
    /// </summary>
    /// <param name="bytes">字节数组</param>
    /// <returns>十六进制字符串</returns>
    public static string ToHexString(this byte[] bytes)
    {
        if (bytes == null)
        {
            return string.Empty;
        }

        StringBuilder sb = new StringBuilder(bytes.Length * 2);
        foreach (byte b in bytes)
        {
            sb.AppendFormat("{0:x2}", b);
        }
        return sb.ToString();
    }

    /// <summary>
    /// 将十六进制字符串转换为字节数组
    /// </summary>
    /// <param name="hexString">十六进制字符串</param>
    /// <returns>字节数组</returns>
    public static byte[] FromHexString(string hexString)
    {
        if (string.IsNullOrEmpty(hexString))
        {
            return new byte[0];
        }

        // 确保十六进制字符串长度为偶数
        if (hexString.Length % 2 != 0)
        {
            hexString = "0" + hexString;
        }

        byte[] bytes = new byte[hexString.Length / 2];
        for (int i = 0; i < hexString.Length; i += 2)
        {
            bytes[i / 2] = Convert.ToByte(hexString.Substring(i, 2), 16);
        }
        return bytes;
    }

    /// <summary>
    /// 检查系统是否为小端字节序
    /// </summary>
    /// <returns>是否为小端字节序</returns>
    public static bool IsSystemLittleEndian()
    {
        return BitConverter.IsLittleEndian;
    }
}
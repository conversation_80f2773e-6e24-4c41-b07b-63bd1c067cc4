using System.Collections.Generic;
using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;

namespace ISDN.WCS.Data.Dtos
{
    /// <summary>
    ///  库位管理 对象 wcs_warehouse_location
    ///  author zgq
    ///  date   2025-08-13 16:36:43
    /// </summary>
    public class WcsWarehouseLocationDto : BaseDto
    {
        /// <summary>
        /// 货架编码
        /// </summary>
        [Excel(Name = "货架编码")]
        public string? Shelves { get; set; }
        
        /// <summary>
        /// x轴坐标
        /// </summary>
        [Excel(Name = "x轴坐标")]
        public string? X { get; set; }
        
        /// <summary>
        /// y轴坐标
        /// </summary>
        [Excel(Name = "y轴坐标")]
        public string? Y { get; set; }
        
        /// <summary>
        /// z轴坐标
        /// </summary>
        [Excel(Name = "z轴坐标")]
        public string? Z { get; set; }
        
        /// <summary>
        /// 所在层数
        /// </summary>
        [Excel(Name = "所在层数")]
        public int? Layer { get; set; }
        
        /// <summary>
        /// 货位深度(1或2)
        /// </summary>
        [Excel(Name = "货位深度(1或2)")]
        public int? Deep { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        [Excel(Name = "库位ID")]
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 库位名称
        /// </summary>
        [Excel(Name = "库位名称")]
        public string LocationName { get; set; }
        
        /// <summary>
        /// 库位编码
        /// </summary>
        [Excel(Name = "库位编码")]
        public string LocationCode { get; set; }
        
        /// <summary>
        /// 巷道ID
        /// </summary>
        [Excel(Name = "巷道ID")]
        public long? LaneId { get; set; }

    /// <summary>
    /// 巷道名称
    /// </summary>
    [Excel(Name = "巷道名称")]
    public string? LaneName { get; set; }
    /// <summary>
    /// 库位类型
    /// </summary>
    [Excel(Name = "库位类型")]
        public int? LocationType { get; set; }
        
        /// <summary>
        /// 生效
        /// </summary>
        [Excel(Name = "生效")]
        public int? IsVisibleFlag { get; set; }
        
        /// <summary>
        /// 创建人id
        /// </summary>
        [Excel(Name = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id
        /// </summary>
        [Excel(Name = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Excel(Name = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间
        /// </summary>
        [Excel(Name = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
    }
}

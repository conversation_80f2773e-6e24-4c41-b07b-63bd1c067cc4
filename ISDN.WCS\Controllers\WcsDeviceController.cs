using ISDN.WCS.Data.Dtos;
using ISDN.WCS.PLC.Socket;
using ISDN.WCS.PLC.SocketService;
//using ISDN.Data.Dtos;
using ISDN.WCS.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using RuoYi.Common.Enums;
using RuoYi.Common.Utils;
using RuoYi.Framework;
using RuoYi.Framework.Extensions;
using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace ISDN.WCS.Controllers
{
    /// <summary>
    /// 设备管理
    ///  author zgq
    ///  date   2025-08-13 16:36:40
    /// </summary>
    [ApiDescriptionSettings("Wcs")]
    [Route("wcs/device")]
    public class WcsDeviceController : ControllerBase
    {
        private readonly ILogger<WcsDeviceController> _logger;
        private readonly WcsDeviceService _wcsDeviceService;
        private readonly PLCClientSocketManager _plcClientSocketManager;
        public WcsDeviceController(ILogger<WcsDeviceController> logger,
            WcsDeviceService wcsDeviceService,
            PLCClientSocketManager plcClientSocketManager)
        {
            _logger = logger;
            _wcsDeviceService = wcsDeviceService;
            _plcClientSocketManager = plcClientSocketManager; // 直接注入具体实现类
        }

        /// <summary>
        /// 查询设备管理列表
        /// </summary>
        [HttpGet("list")]
        [AppAuthorize("wcs:device:list")]
        public async Task<SqlSugarPagedList<WcsDeviceDto>> GetWcsDevicePagedList([FromQuery] WcsDeviceDto dto)
        {
           return await _wcsDeviceService.GetDtoPagedListAsync(dto);
        }

    [HttpGet("ListAll")]
    public async Task<AjaxResult> GetList()
    {
      var lanes = await _wcsDeviceService.GetListAsync(new WcsDeviceDto());
      var ajax = AjaxResult.Success();
      ajax.Add("lanes", lanes);

      return ajax;
    }
    /// <summary>
    /// 获取 设备管理 详细信息
    /// </summary>
    [HttpGet("")]
        [HttpGet("{id}")]
        [AppAuthorize("wcs:device:query")]
        public async Task<AjaxResult> Get(long id)
        {
            var data = await _wcsDeviceService.GetDtoAsync(id);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 新增 设备管理
        /// </summary>
        [HttpPost("")]
        [AppAuthorize("wcs:device:add")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "设备管理", BusinessType = BusinessType.INSERT)]
        public async Task<AjaxResult> Add([FromBody] WcsDeviceDto dto)
        {
            var data = await _wcsDeviceService.InsertAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 修改 设备管理
        /// </summary>
        [HttpPut("")]
        [AppAuthorize("wcs:device:edit")]
        [TypeFilter(typeof(RuoYi.Framework.DataValidation.DataValidationFilter))]
        [RuoYi.System.Log(Title = "设备管理", BusinessType = BusinessType.UPDATE)]
        public async Task<AjaxResult> Edit([FromBody] WcsDeviceDto dto)
        {
            var data = await _wcsDeviceService.UpdateAsync(dto);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 删除 设备管理
        /// </summary>
        [HttpDelete("{ids}")]
        [AppAuthorize("wcs:device:remove")]
        [RuoYi.System.Log(Title = "设备管理", BusinessType = BusinessType.DELETE)]
        public async Task<AjaxResult> Remove(string ids)
        {
            var idList = ids.SplitToList<long>();
            var data = await _wcsDeviceService.DeleteAsync(idList);
            return AjaxResult.Success(data);
        }

        /// <summary>
        /// 导入 设备管理
        /// </summary>
        [HttpPost("import")]
        [AppAuthorize("wcs:device:import")]
        [RuoYi.System.Log(Title = "设备管理", BusinessType = BusinessType.IMPORT)]
        public async Task Import([Required] IFormFile file)
        {
            var stream = new MemoryStream();
            file.CopyTo(stream);
            var list = await ExcelUtils.ImportAsync<WcsDeviceDto>(stream);
            await _wcsDeviceService.ImportDtoBatchAsync(list);
        }

        /// <summary>
        /// 导出 设备管理
        /// </summary>
        [HttpPost("export")]
        [AppAuthorize("wcs:device:export")]
        [RuoYi.System.Log(Title = "设备管理", BusinessType = BusinessType.EXPORT)]
        public async Task Export(WcsDeviceDto dto)
        {
            var list = await _wcsDeviceService.GetDtoListAsync(dto);
            await ExcelUtils.ExportAsync(App.HttpContext.Response, list);
        }

        /// <summary>
        /// 设备上线连接
        /// </summary>
        /// <param name="deviceId">设备ID</param>
        /// <returns>连接结果</returns>
        [HttpGet("online/{deviceId}")]
        [AppAuthorize("wcs:device:online")]
        public async Task<AjaxResult> Online(long deviceId)
        {
            //var _plcClientSocketManager = App.GetService<PLCClientSocketManager>();
            try
            {
                // 获取设备信息
                var device = await _wcsDeviceService.GetAsync(deviceId);
                if (device == null)
                {
                    return AjaxResult.Error("设备不存在");
                }

                if (string.IsNullOrEmpty(device.Addr) || device.Port == null)
                {
                    return AjaxResult.Error("设备通信地址或端口未配置");
                }

                // 调用AddClientAsync方法连接设备
                bool connected = await _plcClientSocketManager.AddClientAsync(
                    device.DeviceCode,  // clientId
                    device.Addr,        // ipAddress
                    device.Port.Value   // port
                );

                if (connected)
                {
                    // 更新设备在线状态
                    device.Online = 2; // 2:在线
                    await _wcsDeviceService.UpdateAsync(device);
                    return AjaxResult.Success("设备连接成功");
                }
                else
                {
                    return AjaxResult.Error("设备连接失败");
                }
            }
            catch (Exception ex)
            {
                return AjaxResult.Error("设备上线连接异常：" + ex.Message);
            }
        }
    }
}

using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using RuoYi.Common.Data;
using RuoYi.Data;
using RuoYi.Data.Dtos;
using RuoYi.Data.Entities;
using SqlSugar;

namespace ISDN.WCS.Repositories
{
  /// <summary>
  ///  库位管理 Repository
  ///  author zgq
  ///  date   2025-08-13 16:36:43
  /// </summary>
  public class WcsWarehouseLocationRepository : BaseRepository<WcsWarehouseLocation, WcsWarehouseLocationDto>
  {
    public WcsWarehouseLocationRepository(ISqlSugarRepository<WcsWarehouseLocation> sqlSugarRepository)
    {
      Repo = sqlSugarRepository;
    }

    /// <summary>
    /// 构造条件查询器
    /// </summary>
    /// <param name="dto">domain</param>
    /// <returns></returns>
    public override ISugarQueryable<WcsWarehouseLocation> Queryable(WcsWarehouseLocationDto dto)
    {
      return Repo.AsQueryable()
                      .WhereIF(!string.IsNullOrWhiteSpace(dto.Shelves), (t) => t.Shelves == dto.Shelves)
                      .WhereIF(!string.IsNullOrWhiteSpace(dto.LocationName), (t) => t.LocationName.Contains(dto.LocationName))
                      .WhereIF(!string.IsNullOrWhiteSpace(dto.LocationCode), (t) => t.LocationCode == dto.LocationCode)
                      .WhereIF(dto.LocationType > 0, (t) => t.LocationType == dto.LocationType)
                      .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
;
    }

    /// <summary>
    /// 构造条件查询器
    /// </summary>
    /// <param name="dto">domain</param>
    /// <returns></returns>
    public override ISugarQueryable<WcsWarehouseLocationDto> DtoQueryable(WcsWarehouseLocationDto dto)
    {
      return Repo.AsQueryable()
                  .WhereIF(!string.IsNullOrWhiteSpace(dto.Shelves), (t) => t.Shelves == dto.Shelves)
                  .WhereIF(!string.IsNullOrWhiteSpace(dto.LocationName), (t) => t.LocationName.Contains(dto.LocationName))
                  .WhereIF(!string.IsNullOrWhiteSpace(dto.LocationCode), (t) => t.LocationCode == dto.LocationCode)
                  .WhereIF(dto.LocationType > 0, (t) => t.LocationType == dto.LocationType)
                  .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
      .Select((t) => new WcsWarehouseLocationDto
      {
        LocationId = t.LocationId
      }, true);
    }

    protected override async Task FillRelatedDataAsync(IEnumerable<WcsWarehouseLocationDto> dtos)
    {
      //await base.FillRelatedDataAsync(dtos);
      //var landIds = dtos.Select(d => d.LaneId).ToList();
      //var lanes = await base.Repo.Context.Queryable<WcsLane>().Where(d => landIds.Contains(d.LaneId)).ToListAsync();

      //foreach (var d in dtos)
      //{
      //  d.LaneName = lanes.FirstOrDefault(t => t.LaneId == d.LaneId)?.LaneName;
      //}
    }

    //protected override async Task FillRelatedDataAsync(IEnumerable<SysRoleDto> dtos)
    //{
    //  await base.FillRelatedDataAsync(dtos);

    //  foreach (var d in dtos)
    //  {
    //    d.StatusDesc = Status.ToDesc(d.Status);
    //    d.DataScopeDesc = DataScope.ToDesc(d.DataScope);
    //  }
    //}
  }
}

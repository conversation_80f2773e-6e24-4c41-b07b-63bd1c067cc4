﻿using Microsoft.Extensions.Configuration;
using RuoYi.Framework.ConfigurableOptions;

namespace RuoYi.Framework.DependencyInjection;

/// <summary>
///   依赖注入配置选项
/// </summary>
public sealed class DependencyInjectionSettingsOptions : IConfigurableOptions<DependencyInjectionSettingsOptions>
{
  /// <summary>
  ///   外部注册定义
  /// </summary>
  public ExternalService[] Definitions { get; set; }

  /// <summary>
  ///   后期配置
  /// </summary>
  /// <param name="options"></param>
  /// <param name="configuration"></param>
  public void PostConfigure(DependencyInjectionSettingsOptions options, IConfiguration configuration)
  {
    options.Definitions ??= Array.Empty<ExternalService>();
  }
}

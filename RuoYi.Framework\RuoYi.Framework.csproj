﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="UnifyResult\UnifyResultProvider.cs"/>
    </ItemGroup>

    <ItemGroup>
        <None Remove="Exceptions\Assets\error.html"/>
        <None Remove="SpecificationDocument\Assets\index-mini-profiler.html"/>
        <None Remove="SpecificationDocument\Assets\index.html"/>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="Exceptions\Assets\error.html"/>
        <EmbeddedResource Include="SpecificationDocument\Assets\index-mini-profiler.html"/>
        <EmbeddedResource Include="SpecificationDocument\Assets\index.html"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="AspectCore.Abstractions" Version="2.4.0"/>
        <PackageReference Include="AspectCore.Extensions.DependencyInjection" Version="2.4.0"/>
        <PackageReference Include="Ben.Demystifier" Version="0.4.1"/>
        <PackageReference Include="Hardware.Info" Version="101.0.0"/>
        <PackageReference Include="Lazy.Captcha.Core" Version="2.0.9"/>
        <PackageReference Include="Mapster" Version="7.4.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10"/>
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.11.0"/>
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10"/>
        <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="8.0.2"/>
        <PackageReference Include="MiniExcel" Version="1.34.2"/>
        <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.3.8"/>
        <PackageReference Include="MiniProfiler.Shared" Version="4.3.8"/>
        <PackageReference Include="RedisRateLimiting.AspNetCore" Version="1.2.0"/>
        <PackageReference Include="SharpZipLib" Version="1.4.2"/>
        <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.9" />
        <PackageReference Include="SqlSugarCore" Version="*********"/>
        <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="8.0.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.0.0"/>
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.0.0"/>
        <PackageReference Include="UAParser.Core" Version="4.0.4"/>
    </ItemGroup>

</Project>

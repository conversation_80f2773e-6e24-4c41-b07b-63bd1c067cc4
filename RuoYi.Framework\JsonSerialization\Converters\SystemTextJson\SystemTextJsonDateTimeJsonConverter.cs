﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace RuoYi.Framework.JsonSerialization;

/// <summary>
///   DateTime 类型序列化
/// </summary>
[SuppressSniffer]
public class SystemTextJsonDateTimeJsonConverter : JsonConverter<DateTime>
{
  /// <summary>
  ///   默认构造函数
  /// </summary>
  public SystemTextJsonDateTimeJsonConverter()
    : this(default)
  {
  }

  /// <summary>
  ///   构造函数
  /// </summary>
  /// <param name="format"></param>
  public SystemTextJsonDateTimeJsonConverter(string format = "yyyy-MM-dd HH:mm:ss")
  {
    Format = format;
  }

  /// <summary>
  ///   时间格式化格式
  /// </summary>
  public string Format { get; }

  /// <summary>
  ///   反序列化
  /// </summary>
  /// <param name="reader"></param>
  /// <param name="typeToConvert"></param>
  /// <param name="options"></param>
  /// <returns></returns>
  public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    return Penetrates.ConvertToDateTime(ref reader);
  }

  /// <summary>
  ///   序列化
  /// </summary>
  /// <param name="writer"></param>
  /// <param name="value"></param>
  /// <param name="options"></param>
  public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
  {
    writer.WriteStringValue(value.ToString(Format));
  }
}

/// <summary>
///   DateTime? 类型序列化
/// </summary>
[SuppressSniffer]
public class SystemTextJsonNullableDateTimeJsonConverter : JsonConverter<DateTime?>
{
  /// <summary>
  ///   默认构造函数
  /// </summary>
  public SystemTextJsonNullableDateTimeJsonConverter()
    : this(default)
  {
  }

  /// <summary>
  ///   构造函数
  /// </summary>
  /// <param name="format"></param>
  public SystemTextJsonNullableDateTimeJsonConverter(string format = "yyyy-MM-dd HH:mm:ss")
  {
    Format = format;
  }

  /// <summary>
  ///   时间格式化格式
  /// </summary>
  public string Format { get; }

  /// <summary>
  ///   反序列化
  /// </summary>
  /// <param name="reader"></param>
  /// <param name="typeToConvert"></param>
  /// <param name="options"></param>
  /// <returns></returns>
  public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
  {
    return Penetrates.ConvertToDateTime(ref reader);
  }

  /// <summary>
  ///   序列化
  /// </summary>
  /// <param name="writer"></param>
  /// <param name="value"></param>
  /// <param name="options"></param>
  public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
  {
    if (value == null) writer.WriteNullValue();
    else writer.WriteStringValue(value.Value.ToString(Format));
  }
}

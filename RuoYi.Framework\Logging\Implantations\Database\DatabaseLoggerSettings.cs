using Microsoft.Extensions.Logging;

namespace RuoYi.Framework.Logging;

/// <summary>
///   数据库日志配置类
/// </summary>
[SuppressSniffer]
public sealed class DatabaseLoggerSettings
{
  /// <summary>
  ///   最低日志记录级别
  /// </summary>
  public LogLevel MinimumLevel { get; set; } = LogLevel.Trace;

  /// <summary>
  ///   是否使用 UTC 时间戳，默认 false
  /// </summary>
  public bool UseUtcTimestamp { get; set; }

  /// <summary>
  ///   日期格式化
  /// </summary>
  public string DateFormat { get; set; } = "yyyy-MM-dd HH:mm:ss.fffffff zzz dddd";

  /// <summary>
  ///   是否启用日志上下文
  /// </summary>
  public bool IncludeScopes { get; set; } = true;

  /// <summary>
  ///   忽略日志循环输出
  /// </summary>
  /// <remarks>对性能有些许影响</remarks>
  public bool IgnoreReferenceLoop { get; set; } = true;

  /// <summary>
  ///   显示跟踪/请求 Id
  /// </summary>
  public bool WithTraceId { get; set; } = false;

  /// <summary>
  ///   显示堆栈框架（程序集和方法签名）
  /// </summary>
  public bool WithStackFrame { get; set; } = false;
}

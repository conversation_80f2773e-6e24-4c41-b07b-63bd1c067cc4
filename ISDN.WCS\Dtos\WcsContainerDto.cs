using System.Collections.Generic;
using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;

namespace ISDN.WCS.Data.Dtos
{
    /// <summary>
    ///  容器档 对象 wcs_container
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
    public class WcsContainerDto : BaseDto
    {
        /// <summary>
        /// 容器ID
        /// </summary>
        [Excel(Name = "容器ID")]
        public long? ContainerId { get; set; }
        
        /// <summary>
        /// 容器名称
        /// </summary>
        [Excel(Name = "容器名称")]
        public string ContainerName { get; set; }
        
        /// <summary>
        /// 容器编号
        /// </summary>
        [Excel(Name = "容器编号")]
        public string ContainerCode { get; set; }
        
        /// <summary>
        /// 所在库位
        /// </summary>
        public long? LocationId { get; set; }


        [Excel(Name = "所在库位")]
        public string? LocationCode { get; set; }
        /// <summary>
        /// 当前位置
        /// </summary>
        [Excel(Name = "当前位置")]
        public string? CurrentAdrr { get; set; }
        
        /// <summary>
        /// 生效
        /// </summary>
        [Excel(Name = "生效")]
        public int IsVisibleFlag { get; set; }
        
        /// <summary>
        /// 创建人id
        /// </summary>
        [Excel(Name = "创建人id")]
        public long? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人id
        /// </summary>
        [Excel(Name = "更新人id")]
        public long? UpdatedBy { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Excel(Name = "创建时间")]
        public DateTime? CreatedTime { get; set; }
        
        /// <summary>
        /// 修改时间
        /// </summary>
        [Excel(Name = "修改时间")]
        public DateTime? UpdatedTime { get; set; }
        
    }
}

using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace ISDN.WCS.PLC.SocketService;

/// <summary>
/// Socket后台服务
/// 负责管理Socket服务的生命周期
/// </summary>
public class SocketBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private SocketService _socketService;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public SocketBackgroundService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// 执行后台服务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns>任务</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        Console.WriteLine("Socket后台服务正在启动...");

        // 创建作用域来获取服务实例
        using (var scope = _serviceProvider.CreateScope())
        {
            _socketService = scope.ServiceProvider.GetRequiredService<SocketService>();
            
            try
            {
                // 启动Socket服务
                _socketService.Start();
                
                Console.WriteLine("Socket后台服务已启动");

                // 等待停止信号
                await Task.Delay(Timeout.Infinite, stoppingToken);
            }
            catch (TaskCanceledException)
            {
                // 正常停止，无需处理
                Console.WriteLine("Socket后台服务接收到停止信号");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Socket后台服务运行异常: {ex.Message}");
            }
            finally
            {
                // 确保Socket服务停止
                try
                {
                    _socketService.Stop();
                    Console.WriteLine("Socket后台服务已停止");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"停止Socket服务时发生异常: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    public override Task StopAsync(CancellationToken cancellationToken)
    {
        Console.WriteLine("Socket后台服务正在停止...");
        
        return base.StopAsync(cancellationToken);
    }
}
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ISDN.WCS.PLC.Socket;
using System.Text;
using System.Linq;

namespace ISDN.WCS.PLC.SocketService;

// 使用PLCServerSocket中定义的DataHandler委托类型

/// <summary>
/// Socket服务
/// 负责管理服务器Socket和设备连接
/// </summary>
public partial class SocketService : IDisposable
{
  private readonly PLCServerSocket _serverSocket;
  private const int DEFAULT_PORT = 8888;

  /// <summary>
  /// 构造函数
  /// </summary>
  public SocketService()
  {
    _serverSocket = new PLCServerSocket(DEFAULT_PORT);
    InitializeHandlers();
  }

  /// <summary>
  /// 初始化数据处理器
  /// </summary>
  private void InitializeHandlers()
  {
    //RegisterDataHandlers();
    // 注册事件处理器
    _serverSocket.ClientConnected += OnClientConnected;
    _serverSocket.ClientDisconnected += OnClientDisconnected;
  }

  /// <summary>
  /// 启动Socket服务
  /// </summary>
  public void Start()
  {
    try
    {
      _serverSocket.Start();
      Console.WriteLine($"Socket服务已启动，监听端口: {DEFAULT_PORT}");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"启动Socket服务失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 停止Socket服务
  /// </summary>
  public void Stop()
  {
    try
    {
      _serverSocket.Stop();
      Console.WriteLine("Socket服务已停止");
    }
    catch (Exception ex)
    {
      Console.WriteLine($"停止Socket服务失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 向指定客户端发送数据
  /// </summary>
  /// <param name="clientId">客户端ID</param>
  /// <param name="data">数据</param>
  /// <returns>是否发送成功</returns>
  public bool SendData(string clientId, string data)
  {
    try
    {
      return _serverSocket.SendData(clientId, data);
    }
    catch (Exception ex)
    {
      Console.WriteLine($"向客户端 {clientId} 发送数据失败: {ex.Message}");
      return false;
    }
  }

  /// <summary>
  /// 向指定客户端发送二进制数据（小端）
  /// </summary>
  /// <param name="clientId">客户端ID</param>
  /// <param name="bytes">二进制数据</param>
  /// <returns>是否发送成功</returns>
  public bool SendBinaryData(string clientId, byte[] bytes)
  {
    try
    {
      string hexData = bytes.ToHexString();
      return _serverSocket.SendData(clientId, "BINARY|" + hexData);
    }
    catch (Exception ex)
    {
      Console.WriteLine($"向客户端 {clientId} 发送二进制数据失败: {ex.Message}");
      return false;
    }
  }

  /// <summary>
  /// 向所有客户端广播数据
  /// </summary>
  /// <param name="data">数据</param>
  public void BroadcastData(string data)
  {
    try
    {
      _serverSocket.BroadcastData(data);
    }
    catch (Exception ex)
    {
      Console.WriteLine($"广播数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 向所有客户端广播二进制数据（小端）
  /// </summary>
  /// <param name="bytes">二进制数据</param>
  public void BroadcastBinaryData(byte[] bytes)
  {
    try
    {
      string hexData = bytes.ToHexString();
      _serverSocket.BroadcastData("BINARY|" + hexData);
    }
    catch (Exception ex)
    {
      Console.WriteLine($"广播二进制数据失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 向指定客户端发送数据（异步）
  /// </summary>
  /// <param name="clientId">客户端ID</param>
  /// <param name="dataType">数据类型</param>
  /// <param name="data">二进制数据</param>
  /// <returns>是否发送成功</returns>
  public async Task<bool> SendDataAsync(string clientId, string dataType, byte[] data)
  {
    return await Task.Run(() =>
    {
      try
      {
        // 将二进制数据转换为十六进制字符串并添加数据类型
        string hexData = data.ToHexString();
        string message = $"TYPE|{dataType}|{hexData}";

        return _serverSocket.SendData(clientId, message);
      }
      catch (Exception ex)
      {
        Console.WriteLine($"向客户端 {clientId} 异步发送数据失败: {ex.Message}");
        return false;
      }
    });
  }

  /// <summary>
  /// 向所有客户端广播数据（异步）
  /// </summary>
  /// <param name="dataType">数据类型</param>
  /// <param name="data">二进制数据</param>
  /// <returns>任务</returns>
  public async Task BroadcastDataAsync(string dataType, byte[] data)
  {
    await Task.Run(() =>
    {
      try
      {
        // 将二进制数据转换为十六进制字符串并添加数据类型
        string hexData = data.ToHexString();
        string message = $"TYPE|{dataType}|{hexData}";

        _serverSocket.BroadcastData(message);
      }
      catch (Exception ex)
      {
        Console.WriteLine($"异步广播数据失败: {ex.Message}");
      }
    });
  }

  /// <summary>
  /// 获取所有连接的客户端ID
  /// </summary>
  /// <returns>客户端ID列表</returns>
  public List<string> GetConnectedClients()
  {
    try
    {
      return _serverSocket.GetAllClientIds();
    }
    catch (Exception ex)
    {
      Console.WriteLine($"获取连接客户端列表失败: {ex.Message}");
      return new List<string>();
    }
  }

  /// <summary>
  /// 获取所有连接的客户端ID（兼容旧接口）
  /// </summary>
  /// <returns>客户端ID列表</returns>
  public List<string> GetAllConnectedClients()
  {
    return GetConnectedClients();
  }

  /// <summary>
  /// 获取客户端连接数量
  /// </summary>
  /// <returns>连接数量</returns>
  public int GetClientCount()
  {
    try
    {
      return _serverSocket.GetConnectedClientCount();
    }
    catch (Exception ex)
    {
      Console.WriteLine($"获取客户端连接数量失败: {ex.Message}");
      return 0;
    }
  }

  /// <summary>
  /// 获取客户端连接数量（兼容旧接口）
  /// </summary>
  /// <returns>连接数量</returns>
  public int GetConnectedClientCount()
  {
    return GetClientCount();
  }

  /// <summary>
  /// 检查客户端是否连接
  /// </summary>
  /// <param name="clientId">客户端ID</param>
  /// <returns>是否连接</returns>
  public bool IsClientConnected(string clientId)
  {
    try
    {
      return _serverSocket.IsClientConnected(clientId);
    }
    catch (Exception ex)
    {
      Console.WriteLine($"检查客户端 {clientId} 连接状态失败: {ex.Message}");
      return false;
    }
  }

  /// <summary>
  /// 断开指定客户端连接
  /// </summary>
  /// <param name="clientId">客户端ID</param>
  public void DisconnectClient(string clientId)
  {
    try
    {
      _serverSocket.DisconnectClient(clientId);
    }
    catch (Exception ex)
    {
      Console.WriteLine($"断开客户端 {clientId} 连接失败: {ex.Message}");
    }
  }

  #region 事件处理方法


  /// <summary>
  /// 客户端连接事件处理
  /// </summary>
  /// <param name="sender">发送者</param>
  /// <param name="e">事件参数</param>
  private void OnClientConnected(object sender, SocketClientConnectedEventArgs e)
  {
    Console.WriteLine($"客户端 {e.ClientId} (IP: {e.IpAddress}:{e.Port}) 已连接");
    // 可以在这里添加客户端连接后的初始化逻辑，例如：发送欢迎消息、配置信息等
  }

  /// <summary>
  /// 客户端断开连接事件处理
  /// </summary>
  /// <param name="sender">发送者</param>
  /// <param name="e">事件参数</param>
  private void OnClientDisconnected(object sender, SocketClientDisconnectedEventArgs e)
  {
    Console.WriteLine($"客户端 {e.ClientId} 已断开连接");
    // 可以在这里添加客户端断开后的清理逻辑，例如：更新设备状态、记录断开原因等
  }

  #endregion

  /// <summary>
  /// 释放资源
  /// </summary>
  public void Dispose()
  {
    Stop();
    _serverSocket?.Dispose();
  }
}

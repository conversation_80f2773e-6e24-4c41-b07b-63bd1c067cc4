using RuoYi.Data;
using RuoYi.Common.Data;
using ISDN.WCS.Data.Dtos;
using ISDN.WCS.Data.Entities;
using SqlSugar;

namespace ISDN.WCS.Repositories
{
    /// <summary>
    ///  容器档 Repository
    ///  author ruoyi.net
    ///  date   2025-08-13 16:36:43
    /// </summary>
public class WcsContainerRepository : BaseRepository<WcsContainer, WcsContainerDto>
    {
    public WcsContainerRepository(ISqlSugarRepository<WcsContainer> sqlSugarRepository)
        {
        Repo = sqlSugarRepository;
        }

        /// <summary>
        /// 构造条件查询器
        /// </summary>
        /// <param name="dto">domain</param>
        /// <returns></returns>
        public override ISugarQueryable<WcsContainer> Queryable(WcsContainerDto dto)
            {
            return Repo.AsQueryable()
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.ContainerName), (t) => t.ContainerName.Contains(dto.ContainerName))
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.ContainerCode), (t) => t.ContainerCode == dto.ContainerCode)
                            .WhereIF(dto.LocationId > 0, (t) => t.LocationId == dto.LocationId)
                            .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
;
            }

            /// <summary>
            /// 构造条件查询器
            /// </summary>
            /// <param name="dto">domain</param>
            /// <returns></returns>
            public override ISugarQueryable<WcsContainerDto> DtoQueryable(WcsContainerDto dto)
                {
                return Repo.AsQueryable()
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.ContainerName), (t) => t.ContainerName.Contains(dto.ContainerName))
                            .WhereIF(!string.IsNullOrWhiteSpace(dto.ContainerCode), (t) => t.ContainerCode == dto.ContainerCode)
                            .WhereIF(dto.LocationId > 0, (t) => t.LocationId == dto.LocationId)
                            .WhereIF(dto.IsVisibleFlag > 0, (t) => t.IsVisibleFlag == dto.IsVisibleFlag)
                .Select((t) => new WcsContainerDto
                {
                     ContainerId = t.ContainerId
                }, true);
        }
    }
}

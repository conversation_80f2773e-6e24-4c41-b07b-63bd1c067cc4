﻿using System.Collections.Generic;
using RuoYi.Data.Attributes;
using RuoYi.Data.Dtos;

namespace @(Model.PackageName).Data.Dtos
{
    @{
        var dto = Model.Table.IsTree() ? "TreeDto" : "BaseDto";
    }
    /// <summary>
    ///  @Model.FunctionName 对象 @Model.TableName
    ///  author @Model.Author
    ///  date   @Model.DateTime
    /// </summary>
    public class @(Model.ClassName)Dto : @dto
    {
@{
foreach (RuoYi.Data.Entities.GenTableColumn col in Model.Columns)
{
    // 自增字段,前端回传允许为null
    var pk = col.Is_Pk() ? "?" : "";
    @if(!Model.Table.IsSuperColumn(col.NetField))
    {
        // 添加Excel特性描述
        var colName=col.ColumnComment;
        if (string.IsNullOrWhiteSpace(colName)){
            colName=col.NetField;
        }
        // 字段属性
        @:/// <summary>
        @:/// @colName
        @:/// </summary>
        @:[Excel(Name = "@colName")]
        @:public @col.NetType@pk @col.NetField { get; set; }
        @:
    }
}
}
    }
}
